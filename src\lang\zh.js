export default {
  system: {
    reflesh: '刷新',
    close: '关闭',
    closeOther: '关闭其他',
    closeAll: '关闭全部'
  },
  test: {
    title: '系统布局配置',
    theme: '主题色'
  },
  login: {
    title: '账号登录',
    tips: '看不清，点击换一张',
    bodyTittle: '极致体验，尽在优势玩家',
    bodyDesc: '为您提供顺心的体验服务',
    placeholder: {
      account: '账号',
      password: '密码',
      verifyCode: '验证码'
    },
    validateMsg: {
      verifyCodeLen: '验证码长度为4',
      verifyCodeError: '验证码错误',
      verifyCode: '请输入验证码',
      account: '请输入登录账号',
      password: '请输入登录密码',
      passwordError: '登录密码错误',
      accountClosed: '账号已关闭',
      accountNotExisted: '账号未开通或者已经关闭'
    }
  },
  game: {
    gameType: '游戏类型',
    tableNo: '台号',
    matchNo: '靴号',
    gameNo: '局号',
    winAmount: '输赢金额',
    result: '开牌结果',
    betDetail: '注单详情',
    balance: '余额',
    betTime: '下注时间',
    gameNumber: '局号',
    limit: '限红',
    banker: 'B',
    player: 'P',
    luckSix: '幸运6',
    tie: 'T',
    bankerAbbr: 'B',
    playerAbbr: 'p',
    bankerPair: 'B.Pair',
    playerPair: 'P.Pair',
    allPair: 'B.Pair,P.Pair',
    noPair: '无对',
    dragon: '龙',
    tiger: '虎',
    pair: '对',
    winner: '获胜方',
    dragonAbbr: '龙',
    tigerAbbr: '虎',
    player1Equal: '闲1平倍',
    player2Equal: '闲2平倍',
    player3Equal: '闲3平倍',
    player1Double: '闲1翻倍',
    player2Double: '闲2翻倍',
    player3Double: '闲3翻倍',
    player1Super: '闲1超倍',
    player2Super: '闲2超倍',
    player3Super: '闲3超倍',
    chipCustom: '自定义',
    remain: '余',
    totalMax: '台红',
    dealer: '荷官',
    askPlayer: '闲问路',
    askBanker: '庄问路',
    askDragon: '龙问路',
    askTiger: '虎问路',
    changeTable: '换台',
    confirm: '确认',
    cancel: '取消',
    repeat: '重复',
    toBet: '请下注',
    openGame: '开牌中',
    waiting: '请稍后',
    addTime: '已加时',
    notChipTimes: '未下注次数超过',
    greaterTotalMax: '超出总台限红',
    lessMin: '低于最低限红',
    greaterMax: '超出最高限红',
    videoHigh: '高清',
    videoLow: '标清',
    yourAreHere: '您在这台',
    bull0: '无',
    bull10: '牛',
    remainPointLow: '余额不足',
    totalText: '汇总'
  },
  bull: {
    bankerWin: '庄:赢',
    player1Win: '闲1:赢',
    player2Win: '闲2:赢',
    player3Win: '闲3:赢',
    noBull: '无',
    bull: '牛'
  },
  home: {
    table: {
      totalAgent: '代理总数',
      newAgentToday: '今日新增代理',
      totalMember: '会员总数',
      newMembersToday: '今日新增会员',
      loginAccount: '登录账号',
      nickname: '用户昵称',
      loginTime: '登录时间',
      loginIp: '登录IP',
      loginAddress: '登录地址',
      availableBalance: '可用余额',
      share: '占股',
      rateOfCodeWashing: '佣金率',
      above: '以上'
    }
  },
  status: {
    enabled: '启用',
    disabled: '停用',
    open: '启用',
    closed: '停用',
    onLine: '在线',
    offLine: '离线'
  },
  operate: {
    login: '登 录',
    sysgen: '系统生成',
    save: '保存',
    confirm: '确 认',
    cancel: '取 消',
    add: '新 增',
    edit: '编 辑',
    modifyResult: '修改结果',
    deleteOne: '删除本局',
    delete: '删 除',
    change: '修 改',
    moreOperate: '更多',
    viewDetail: '查看详情',
    accountEnabled: '账号启用',
    accountDisabled: '账号停用',
    authSetting: '权限设置',
    betEnabled: '投注启用',
    betDisabled: '投注停用',
    memberEdit: '会员编辑',
    agentEdit: '代理编辑',
    passwordChange: '密码修改',
    rechargeWithdraw: '充值提现',
    settlement: '码佣结算',
    betHistory: '下注记录',
    transactionHistory: '交易记录',
    commissionHistory: '佣金记录',
    commissionSettlement: '佣金结算',
    block: '限制',
    unblock: '解除限制',
    message: {
      tips: '提示',
      cancel: '取消',
      success: '成功',
      fail: '失败'
    },
    info: {
      continue: '是否继续'
    }
  },
  chargeWithdrawForm: {
    operation: '操作',
    tittle: '充值提现',
    recharge: '充值',
    withdraw: '提现',
    rechargeType: '充值类型',
    cash: '虚拟分',
    sign: '签单',
    payout: '派彩',
    amount: '金额',
    priceUnit: '元',
    placeholder: {
      amount: '请输入金额'
    },
    validateMsg: {
      amount: '金额必须大于0!'
    }
  },
  memberAddForm: {
    validateMsg: {
      account: '请输入账号',
      accountExist: '该账号已存在',
      rateSetting: '请设置佣金率',
      rateLimit: '佣金率不能大于2%',
      chipOptions: '请选择下注筹码选项',
      chipOptionsLimit: '最多可选择3个下注筹码选项'
    }
  },
  member: {
    accountInfo: '账户信息',
    washRateInfo: '佣金率设定 ',
    limitInfo: '限红设定 ',
    operateInfo: {
      stopInfo: '此操作将停用投注, 是否继续?'
    },
    passForm: {
      tittle: '密码修改',
      loginAccount: '登录账号',
      oldPassword: '旧密码',
      newpassword: '新密码',
      password: '密码',
      passwordConfirm: '密码确认',
      placeholder: '密码(6-16字母,数字)',
      validateMsg: {
        password: '请输入登录密码',
        oldPassword: '请输入旧密码',
        oldPasswordError: '旧密码错误',
        newPassword: '请输入新密码',
        passwordConfirm: '请输入确认密码',
        passwordFormate: '密码格式不正确',
        passwordDifferent: '请输入确认密码'
      }
    },
    bettingStatus: '投注状态',
    subordinateAgent: '所属代理',
    account: '账号',
    nickName: '昵称',
    memberAccount: '会员账号',
    memberNickname: '会员昵称',
    fullName: '姓名',
    mobilePhone: '手机',
    memberType: '类型',
    normal: '正常',
    hide: '隐身',
    role: '角色',
    agent: '代理',
    avatar: '投注手',
    pagcor: '政府监管',
    cashier: '收银',
    currency: '币别',
    accountBalance: '账户余额',
    rateOfCodeWashing: '佣金率',
    limitRed: '限红',
    onLine: '在线',
    recentlyLoggedInIp: '最近登录IP',
    recentlyLoggedInTime: '最近登录时间',
    createdTime: '创建时间',
    codeWashingFrom: '码量（从）',
    codeWashingTo: '码量（到）',
    washTips: '0表示无穷大',
    operation: '操作',
    totalDeposit: '充值总金额',
    totalWithdrawal: '提现总金额',
    totalValidBet: '下注总金额',
    totalWinOrLose: '输/赢总金额',
    commission: '佣金(%)',
    commissionBalance: '佣金金额',
    jetton: {
      limitSetting: '限红设定',
      chipNo: '筹码编号',
      chipName: '筹码名称',
      lowerLimit: '投注下限',
      upperLimit: '投注上限',
      chipsOption: '筹码选项'
    },
    placeholder: {
      memberAccount: '根据账号查询'
    },
    level: '等级',
    betMin: '每次最低下注',
    betMax: '每次最高下注',
    betMax2: '每局最高下注',
    betMax3: '每局和最高下注',
    maxWin: '每天最高赢',
    dataError: ' 大于 '
  },
  agentForm: {
    accountStatus: '账号状态',
    bettingStatus: '投注状态',
    agentAccount: '代理账号',
    agentNickname: '代理昵称',
    fullName: '姓名',
    mobilePhone: '手机',
    accountBalance: '账户余额',
    rateOfCodeWashing: '佣金率',
    limitRed: '限红',
    onLine: '在线',
    share: '占成(%)',
    operation: '操作',
    parentAgent: '上级代理',
    createdTime: '创建时间',
    childAgent: '下级代理',
    info: {
      noData: '没有代理数据了',
      noParentData: '没有上级代理数据了',
      noChildData: '没有下级代理数据了'
    }
  },
  jetton: {
    chipInfo: '筹码信息',
    chipNo: '筹码编号',
    chipName: '筹码名称',
    betLowerLimit: '投注下限',
    betUpperLimit: '投注上限',
    chipOption: '筹码选项',
    operation: '操作',
    validateMsg: {
      jettonNo: '请输入筹码编号',
      jettonName: '请输入筹码名称',
      downLimit: '请输入投注下限',
      upperLimit: '请输入投注上限'
    },
    msg: {
      chipRepeat: '筹码重复设置',
      setChip: '请先设定筹码',
      threeChip: '请先设定3个筹码',
      deleteData: '请选择你需要删除的数据'
    }
  },
  tableInfo: {
    tableInfo: '桌台信息',
    tableNo: '桌台编号',
    tableName: '桌台名称',
    tableNameEn: '桌台英文名',
    tableType: '桌台类型',
    casino: '贵宾厅',
    cebu: 'Cebu',
    manila: 'Manila',
    networkTable: '网络台',
    liveTable: '现场台',
    gameType: '游戏类型',
    betMethod: '投注方式',
    PCVideoUrl: 'PC视频地址',
    PCVideoUrl1: 'PC视频地址1',
    PCVideoUrl2: 'PC视频地址2',
    PCVideoUrl3: 'PC视频地址3',
    dealerUrl: '荷官地址',
    AppVideoUrl: 'APP视频地址',
    AppVideoUrl1: 'APP视频地址1',
    AppVideoUrl2: 'APP视频地址2',
    AppVideoUrl3: 'APP视频地址3',
    shoeIp: '靴IP',
    port: '端口',
    shoePort: '比赛倒计时',
    serverPort: '服务端口',
    serverIp: '服务IP',
    notChipTimes: '未下注局数',
    chipInterval: '下注倒计时',
    totalMax: '最大总投注',
    totalMax2: '最大总投注2',
    totalMax3: '最大总投注3',
    betMin: '最低投注',
    betMax: '最高投注',
    tieMin: '和最低投注',
    tieMax: '和最高投注',
    pairMin: '对最低投注',
    pairMax: '对最高投注',
    superMin: '超级6最低投注',
    superMax: '超级6最高投注',
    isConfirm: '确认牌',
    isHide: '隐藏牌',
    isJoin: '是否加入',
    yes: '是',
    no: '否',
    need: '需要',
    noNeed: '不需要',
    showCard: '翻开牌',
    hideCard: '隐藏牌',
    freeCommission: '免佣',
    currencyCode: '币种',
    freeYes: '免佣',
    freeNo: '不免佣',
    status: '状态',
    operation: '操作',
    validateMsg: {
      tableNo: '请输入桌台编号',
      tableName: '请输入桌台名称',
      tableNameEn: '请输入桌台名称',
      gameType: '请输入游戏类型',
      gameCategory: '请输入投注类型',
      rtmp: '请输入PC视频地址1',
      rtmp2: '请输入PC视频地址2',
      rtmp3: '请输入PC视频地址3',
      dealerRtmp: '请输入荷官地址',
      rtsp: '请输入APP视频地址1',
      rtsp2: '请输入APP视频地址2',
      rtsp3: '请输入APP视频地址3',
      betMin: '请输入最低投注',
      betMax: '请输入最高投注',
      tieMin: '请输入和最低投注',
      tieMax: '请输入和最高投注',
      pairMin: '请输入对最低投注',
      pairMax: '请输入对最高投注',
      superMin: '请输入超级6最低投注',
      superMax: '请输入超级6最高投注',
      totalMax: '请输入最高总投注',
      totalMax2: '请输入最高总投注2',
      totalMax3: '请输入最高总投注3',
      serverIp: '请输入服务器IP',
      serverPort: '请输入服务器端口',
      shoeIp: '请输入靴IP',
      shoePort: '请输入比赛倒计时',
      notChipTimes: '未下注局数'
    }
  },
  gameCategory: {
    netBet: '网投',
    mobileBet: '电投',
    speed: '极速',
    proxy: '代理'
  },
  gameTypeEnum: {
    all: '全部',
    baccarat: '百家乐',
    dragonAndTiger: '龙虎',
    bullAndBull: '牛牛'
  },
  tableInfoStatusEnum: {
    new: '新局',
    end: '结束(已开牌)',
    betting: '投注中',
    bet: '投注结束',
    start: '发牌中',
    wait: '等待开牌',
    break: '休息',
    pause: '暂停',
    washing: '洗牌中'
  },
  timeUnit: {
    second: '秒'
  },
  noticeInfo: {
    notice: '消息',
    noticeStatus: '状态',
    noticeType: '消息类型',
    noticeTittle: '消息标题',
    noticeContent: '消息内容',
    noticeContentEn: '内容(英文)',
    operation: '操作',
    placeholder: {
      messageType: '选择消息类型'
    }
  },
  noticeStatus: {
    valid: '有效',
    invalid: '无效'
  },
  noticeType: {
    webMessage: '后台消息',
    frontMessage: '前端消息'
  },
  dealer: {
    account: '荷官账号',
    nickName: '荷官昵称',
    fullName: '姓名',
    phone: '手机',
    avatar: '荷官图像',
    onLine: '在线',
    createTime: '创建时间',
    remark: '备注',
    operation: '操作',
    info: '荷官信息',
    clickToAdd: '点击新增',
    clickToChange: '点击更改',
    validateMsg: {
      account: '请输入账号',
      avatar: '请上传图像',
      nickname: '请输入昵称',
      fullName: '请输入姓名',
      phoneNo: '请输入手机'
    },
    placeholder: {
      account: '根据账号查询'
    }
  },
  pointHistory: {
    operateTime: '操作时间',
    userType: '用户类型',
    userAccount: '用户账号',
    nickName: '用户昵称',
    balanceBefore: '操作前金额',
    operateAmount: '操作金额',
    balanceAfter: '操作后金额',
    operateAccount: '操作人[账号]',
    operateType: '操作类型',
    placeholder: {
      pointType: '根据操作类型查询',
      account: '根据操作账号查询'
    }
  },
  dateTemplate: {
    today: '今天',
    aWeekAgo: '一周前',
    tomorrow: '明天',
    selectDate: '选择日期',
    to: '至',
    startDate: '开始日期',
    endDate: '结束日期',
    lastMonth: '最近一个月',
    lastWeek: '最近一周',
    last3Months: '最近三个月'
  },
  userType: {
    agent: '代理',
    member: '会员'
  },
  betInfo: {
    betNo: '注单号',
    tableNo: '台号',
    shoeNo: '靴号',
    gameNo: '局号',
    memberAccount: '会员账号',
    memberName: '会员名称',
    betAmount: '下注金额',
    betDetail: '注单详情',
    cardType: '牌型',
    cardsView: '本局牌型',
    cardResult: '结果',
    winAmount: '输赢金额',
    banlance: '余额',
    washAmount: '码量',
    washRate: '佣金率(%)',
    commission: '码佣',
    betTime: '下注时间',
    placeholder: {
      gameType: '根据游戏类型查询',
      betNo: '根据注单号查询',
      userAccount: '根据账号查询',
      gameNo: '根据游戏编号查询',
      matchNo: '根据靴编号查询',
      tableNo: '根据桌台编号查询'
    }
  },
  macthInfo: {
    tableInfo: '台桌信息',
    shoeNo: '靴号',
    gameNo: '局号',
    cardsResult: '开牌结果',
    gameStartTime: '开局时间',
    placeholder: {
      table: '请选择台桌',
      gameNo: '根据局号查询'
    }
  },
  memberSettle: {
    placeholder: {
      gameType: '选择游戏类型'
    },
    gameType: '游戏类型',
    memberAccount: '会员账号',
    memberNickname: '用户昵称',
    currentAmount: '当前金额',
    betTimes: '下注次数',
    betAmount: '下注总额',
    winAmount: '输赢金额',
    totalWashAmount: '总码量',
    commissionAmount: '码佣总额'
  },
  agentSettle: {
    placeholder: {
      gameType: '选择游戏类型'
    },
    gameType: '游戏类型',
    agentAccount: '代理账号',
    agentNickname: '代理昵称',
    totalWinAmount: '总赢金额',
    totalWashAmount: '总码量',
    washRate: '佣金率(%)',
    commissionAmount: '码佣总额',
    washIncome: '佣金',
    share: '占成(%)',
    shareIncome: '占股收益',
    totalIncome: '总收益',
    superiorIncome: '上级收益',
    operation: '操作',
    back: '返回上级',
    agentReport: '代理报表'
  },
  jettonSettle: {
    agentAccount: '代理账号',
    agentNickname: '代理昵称',
    memberAccount: '会员账号',
    memberNickname: '会员昵称',
    currentWashRate: '当前佣金率',
    totalWashAmount: '总码量',
    commissionAmount: '码佣总额',
    operation: '操作',
    agentTittle: '代理码佣结算',
    memberTittle: '会员码佣结算'
  },
  header: {
    logout: '登出系统',
    backHome: '返回首页',
    passChange: '密码修改',
    appDownload: 'APP下载',
    menuNavigation: '菜单导航',
    playNow: '进入游戏',
    copyright: '优势玩家版权所有',
    welcome: '欢迎登录'
  },
  role: {
    roleName: '角色名称',
    roleNo: '角色编号',
    sortNo: '排序号',
    userType: '账号类型',
    status: '状态',
    remark: '备注',
    operation: '操作',
    roleInfo: '角色信息',
    nodeLocation: '节点位置',
    permissionConfiguration: '权限配置'
  },
  button: {
    name: '按钮名称',
    no: '按钮编号',
    sortNo: '排序号',
    buttonInfo: '角色信息',
    status: '状态',
    remark: '备注',
    operation: '操作',
    validateMsg: {
      buttonName: '请输入按钮名称',
      buttonNo: '请输入选择按钮编号',
      sortNo: '请输入按钮序号'
    }
  },
  menu: {
    locale: '本地化',
    componentName: '组件名称',
    componentPath: '组件路径',
    menuIcon: '菜单图标',
    menuName: '菜单名称',
    menuUrl: '菜单Url',
    sortNo: '排序号',
    status: '状态',
    remark: '备注',
    operation: '操作',
    menuInfo: '菜单信息',
    menuLocation: '菜单位置',
    systemMenu: '系统菜单',
    menuType: '菜单层级',
    ifHidden: '是否隐藏',
    funtionManage: '功能管理',
    buttonOptions: {
      waiting: '待选按钮',
      slected: '已选按钮'
    },
    levelOptions: {
      level1: '一级菜单',
      other: '其他菜单'
    }
  },
  commonStatus: {
    valid: '有效',
    invalid: '无效'
  },
  hiddenOptions: {
    hidden: '隐藏',
    show: '显示'
  },
  winRate: {
    placeholder: '选择游戏类型',
    gameType: '游戏类型',
    rateNo: '赔率编号',
    rate: '赔率',
    status: '状态',
    remark: '备注',
    operation: '操作',
    rateInfo: '赔率信息',
    validateMsg: {
      rate: '请输入赔率',
      remark: '请输入备注'
    }
  },
  currencyCode: {
    RMB: '人民币',
    USD: '美元'
  },
  router: {
    dashboard: '控制面板',
    changePassword: '修改密码',
    authorityModule: '权限模块',
    accountCenter: '账户中心',
    reportMange: '报表管理',
    gameSetting: '游戏设置',
    menuMange: '菜单管理',
    buttonMange: '按钮管理',
    roleManage: '角色管理',
    pointHistory: '余额记录',
    betBill: '下注记录',
    memberDailyReport: '会员报表',
    agentDailyReport: '代理报表',
    gameInTable: '台桌游戏',
    operateHistory: '操作历史',
    commissionSettlement: '码佣结算',
    agentList: '代理列表',
    agentAdd: '添加代理',
    agentEdit: '编辑代理',
    memberList: '会员列表',
    memberAdd: '添加会员',
    memberEdit: '编辑会员',
    subAccountList: '子账号列表',
    jetton: '筹码',
    tableInfo: '台桌',
    notice: '公告',
    dealer: '荷官',
    winRate: '赔率',
    country: 'IP限制',
    gameResult: '游戏结果',
    ipWhiteList: 'IP白名单',
    otherList: '其他人员',
    otherAdd: '其他人员新增',
    otherEdit: '其他人员编辑'
  },
  userTypeEnum: {
    superAdmin: '超级管理员',
    agent: '代理',
    member: '会员',
    subAccount: '子账号',
    dealer: '荷官'
  },
  operateTypeEnum: {
    login: '登录',
    accountOperate: '账号停用/开启',
    betOperate: '投注停用/启用',
    recharge: '充值',
    withdraw: '提现',
    passwordChange: '密码修改',
    memberEdit: '会员编辑',
    agentEdit: '代理编辑',
    commissionSettle: '码佣结算'
  },
  platformEnum: {
    pc: '电脑',
    android: '安卓',
    apple: '苹果',
    browser: '浏览器'
  },
  operateHistory: {
    placeholder: {
      operateType: '选择操作类型',
      userType: '选择账户类型',
      operatePlatform: '选择平台类型',
      operateAccount: '根据账号查询'
    },
    operateAccount: '操作账号',
    operateType: '操作类型',
    operatedAccount: '被操作账号',
    userType: '账号类型',
    operatePlatform: '操作平台',
    operateIp: '操作IP',
    operateArea: '操作地区',
    operateTime: '操作时间'
  },
  subAccount: {
    subAccountInfo: '子账号信息',
    placeholder: '根据账号查询',
    accountStatus: '账号状态',
    subordinateAgent: '所属代理',
    account: '账号',
    nickname: '昵称',
    name: '姓名',
    phone: '手机',
    onLine: '在线',
    createdTime: '创建时间',
    remark: '备注',
    operation: '操作',
    subAccountAuth: '子账号权限',
    password: '密码',
    confirmPassword: '确认密码'
  },
  blockStatusEnum: {
    all: '全部',
    blocked: '限制',
    unblocked: '不限制'
  },
  countryInfo: {
    info: '国家/地区信息',
    code: '编码',
    name: '名称',
    nameEn: '英文名称',
    block: '限制',
    yes: '是',
    no: '否',
    validateMsg: {
      name: '请输入国家/地区名称',
      code: '请输入国家/地区编号',
      nameEn: '请输入国家/地区英文名称'
    },
    msg: {
      blockData: '请选择你需要[限制]的数据',
      unblockData: '请选择你需要[解除限制]的数据',
      editData: '请选择你需要[编辑]的数据'
    },
    placeholder: {
      keywords: '根据名称/编号查询',
      blockStatus: '根据限制状态查询'
    }
  },
  ipWhiteListInfo: {
    title: 'IP信息',
    type: 'IP类型',
    ipAddress: 'IP地址',
    blackIp: '白名单',
    whiteIp: '黑名单',
    placeholder: {
      type: '根据IP类型查询',
      ipAddress: '根据IP地址查询'
    }
  }
}
