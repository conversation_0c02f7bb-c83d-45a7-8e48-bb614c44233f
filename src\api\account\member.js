import request from '@/utils/request'

export function listMember(data) {
  return request({
    url: '/member/list',
    method: 'post',
    data
  })
}
export function listMemberInfo(data) {
  return request({
    url: '/member/info/list',
    method: 'post',
    data
  })
}
export function addMember(data) {
  return request({
    url: '/member/add',
    method: 'post',
    data
  })
}
export function updateMember(data) {
  return request({
    url: '/member/update',
    method: 'post',
    data
  })
}
export function fetchMember(id) {
  return request({
    url: '/member/get/info',
    method: 'get',
    params: {
      id: id
    }
  })
}
export function getMemberStatistics(memberId) {
  return request({
    url: '/member/statistics',
    method: 'get',
    params: {
      memberId: memberId
    }
  })
}
export function doChipStatusChange(data) {
  return request({
    url: '/member/chip-status-change',
    method: 'post',
    data
  })
}
export function doAccountStatusChange(data) {
  return request({
    url: '/member/account-status-change',
    method: 'post',
    data
  })
}
export function genAccount(memberType) {
  return request({
    url: '/member/gen-account',
    method: 'get',
    params: {
      memberType: memberType
    }
  })
}
export function getMemberInfo(id) {
  return request({
    url: '/member/get/info',
    method: 'get',
    params: {
      id: id
    }
  })
}

