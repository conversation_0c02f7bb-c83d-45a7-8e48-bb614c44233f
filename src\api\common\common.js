import request from '@/utils/request'
export function uploadFile(data) {
  return request({
    url: '/file/upload',
    method: 'post',
    data: data
  })
}
export function listRouter(userId, systemId) {
  return request({
    url: '/menu/listRouter',
    method: 'get',
    params: {
      userId: userId,
      systemId: systemId
    }
  })
}
export function listUserRouter(userId) {
  return request({
    url: '/menu/listUserRouter',
    method: 'get',
    params: {
      userId: userId
    }
  })
}
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  })
}
export function getRegisterCode(verifyMobile, verifyType) {
  return request({
    url: '/user/register/verifyCode',
    method: 'post',
    params: {
      verifyMobile: verifyMobile,
      verifyType: verifyType
    }
  })
}
export function getAuthCode(verifyMobile) {
  return request({
    url: '/user/auth/verifyCode',
    method: 'post',
    params: {
      verifyMobile: verifyMobile
    }
  })
}

export function getForgetPwdCode(username, verifyMobile, verifyType) {
  return request({
    url: '/user/forgetPwd/verifyCode',
    method: 'post',
    params: {
      verifyMobile: verifyMobile,
      verifyType: verifyType,
      username: username
    }
  })
}
export function verifySmsCode(data) {
  return request({
    url: '/user/verifySmsCode',
    method: 'post',
    data
  })
}
export function verifyByEmail(verifyEmail, username) {
  return request({
    url: '/user/forgetPwd/verifyByEmail',
    method: 'post',
    params: {
      verifyEmail: verifyEmail,
      username: username
    }
  })
}
export function getUsernameByEmail(validKey) {
  return request({
    url: '/user/forgetPwd/getUsernameByEmail',
    method: 'get',
    params: {
      validKey: validKey
    }
  })
}
export function changePassword(username, password, validKey) {
  return request({
    url: '/user/changePassword',
    method: 'post',
    params: {
      username: username,
      password: password,
      validKey: validKey
    }
  })
}
