<template>
  <div>
    <div class="mobile-header">
      <i v-if="back" class="el-icon-arrow-left mback" @click.stop="handleClick" />
      <div class="mtitle">{{ title }}</div>
      <div class="mright">
        <span class="user-name el-icon-s-custom" @click="handleChangePassword"> {{ userName }}</span>
        <slot name="right" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'MobileHeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    back: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'nickName',
      'userName',
      'memberWashList',
      'device'
    ])
  },
  methods: {
    handleClick() {
      this.$emit('back')
    },
    handleChangePassword() {
      this.$router.push({ path: '/user-center/change-pwd' })
    }
  }
}
</script>

<style lang="scss">
.mobile-header {
  height: 40px;
  text-align: center;
  line-height: 40px;
  font-size: 18px;
  display: flex;
  background-color: #f5f7fa;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 1;
}

.mobile-header .mback {
  height: 40px;
  width: 40px;
  font-size: 25px;
  line-height: 40px;
  position: absolute;
  left: 0px;
}

.mobile-header .mright {
  height: 40px;
  line-height: 40px;
  position: absolute;
  right: 5px;
  font-size: 12px;
}

.mobile-header .mtitle {
  flex: 1;
  text-align: center;
  font-size: 18px !important;
}

.user-name {
  color: #666666;
  font-size: 14px;
  margin-right: 5px;
}
</style>
