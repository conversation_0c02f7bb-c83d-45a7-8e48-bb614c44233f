<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">{{ $t('operate.add') }}</el-button>
          <!-- <el-button v-show="this.delete" size="mini" type="danger" icon="el-icon-delete" @click="handleDeleteClick">删除</el-button> -->
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end" />
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="buttonName" :label="$t('button.name')" width="120" />
      <el-table-column prop="buttonNo" :label="$t('button.no')" width="184" />
      <el-table-column prop="sortNo" align="center" :label="$t('button.sortNo')" sortable width="94" />
      <el-table-column :label="$t('button.status')" class-name="status-col" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusTagFilter">
            {{ row.status | statusFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" :label="$t('button.remark')" show-overflow-tooltip />
      <el-table-column fixed="right" align="center" :label="$t('button.operation')" width="110">
        <template slot-scope="scope">
          <el-button v-show="scope.row.edit" plain size="mini" @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('button.buttonInfo')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="48%">
      <el-form ref="dataForm" :model="object" label-width="100px" :disabled="ifView" :rules="buttonRules">
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('button.no')" prop="buttonNo">
              <el-input v-model="object.buttonNo" auto-complete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('button.name')" prop="buttonName">
              <el-input v-model="object.buttonName" auto-complete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('button.sortNo')" prop="sortNo">
              <el-input-number v-model="object.sortNo" :min="1" auto-complete="off" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('button.status')">
              <el-switch v-model="object.status" :active-text="$t('commonStatus.valid')" :inactive-text="$t('commonStatus.invalid')" :active-value="1" :inactive-value="2" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item :label="$t('button.remark')">
              <el-input v-model="object.remark" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" :loading="loadingTags.add" @click="createData">{{ $t('operate.save') }}</el-button>
        <el-button v-else type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.edit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listButton, addButton, deleteButton, updateButton } from '@/api/authorization/button'
import { getDataStatusEnum } from '@/data/common/commonEnum'
import { deepClone } from '@/utils/transferUtil'
import i18n from '@/lang'
var statusOptions = [
  { key: '1', display_name: i18n.t('commonStatus.valid') },
  { key: '0', display_name: i18n.t('commonStatus.invalid') }
]
// arr to obj, such as { CN : "China", US : "USA" }
const statusTypeKeyValue = statusOptions.reduce((acc, cur) => {
  acc[cur.key] = cur.display_name
  return acc
}, {})
export default {
  name: 'ButtonList',
  filters: {
    statusTagFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    },
    statusFilter(type) {
      return statusTypeKeyValue[type]
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      objectList: [],
      multipleSelection: [],
      systemOptions: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {
        buttonNo: '',
        buttonName: ''
      },
      formLabelWidth: '80px',
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      },
      buttonRules: {
        buttonName: [{ required: true, trigger: 'blur', message: this.$t('button.validateMsg.buttonName') }],
        buttonNo: [{ required: true, trigger: 'blur', message: this.$t('button.validateMsg.buttonNo') }],
        sortNo: [{ required: true, trigger: 'blur', message: this.$t('button.validateMsg.sortNo') }]
      }
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      const param = {}
      param.pageSize = this.pageSize
      param.pageNo = this.currentPage
      listButton(param).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogFormVisible = true
      this.object = { status: 1, sortNo: 10 }
      this.ifView = false
      this.dialogStatus = 'create'
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.add = true
          addButton(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.save') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.object = deepClone(val)
      this.ifView = false
      this.dialogStatus = 'edit'
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.edit = true
          updateButton(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    removeData() {
      deleteButton(this.ids).then(() => {
        this.getData()
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    deleteRows() {
      if (this.ids.length > 0) {
        this.removeData()
      } else {
        this.$message({
          message: this.$t('jetton.msg.deleteData'),
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleDeleteClick() {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.deleteRows()
      })
    },
    formatDataStatus(row, column, cellValue) {
      return getDataStatusEnum(cellValue)
    }
  }
}
</script>
