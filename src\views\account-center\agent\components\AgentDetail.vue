<template>
  <div class="app-container">
    <el-form ref="postForm" label-width="120px" label-suffix=":" :model="postForm" :rules="rules"
      class="form-container">
      <el-input v-model="postForm.user.password" type="password" style="position:fixed;bottom:-9999px;" />
      <div class="createPost-main-container">
        <el-row :gutter="16">
          <!-- <div class="span-title mb-5">{{ $t('member.accountInfo') }}</div>-->
          <el-col :lg="10" :md="16" :xs="24">
            <el-row>
              <el-col>
                <el-form-item :label="$t('member.account')" prop="user.userName" class="input-with-select" required>
                  <el-input v-model="postForm.user.userName" :disabled="isEdit" clearable>
                    <el-button slot="append" :disabled="isEdit" :loading="remoteLoading" @click="genUserName">
                      {{ $t('operate.sysgen') }}
                    </el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('member.fullName')" class="postInfo-container-item">
                  <el-input v-model="postForm.member.fullName" clearable />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('member.mobilePhone')" class="postInfo-container-item">
                  <el-input v-model="postForm.member.phoneNo" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item :label="$t('member.passForm.password')" prop="user.password"
                  class="postInfo-container-item" required>
                  <el-input type="password" style="position:fixed;bottom:-9999px;" />
                  <el-input v-model="postForm.user.password" :disabled="isEdit" type="password" clearable />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('member.passForm.passwordConfirm')" prop="user.repeatPassword"
                  class="postInfo-container-item" required>
                  <el-input v-model="postForm.user.repeatPassword" :disabled="isEdit" type="password" clearable />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="到期时间" class="postInfo-container-item">
                  <el-date-picker v-model="postForm.member.expiredDt" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                    class="w-100p" clearable />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="风险等级" class="postInfo-container-item">
                  <el-checkbox-group v-model="riskLevel">
                    <el-checkbox v-for="item in riskLevelOptions" :key="item.value" :label="item.value">{{
                      item.label }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            <!--<el-row>
              <el-col>
                <el-form-item :label="$t('member.currency')" class="postInfo-container-item">
                  <el-radio v-if="userType === 1 || member.currencyCode==='KHR'" v-model="postForm.member.currencyCode" label="KHR">KHR</el-radio>
                  <el-radio v-if="userType === 1 || member.currencyCode==='USD'" v-model="postForm.member.currencyCode" label="USD">USD</el-radio>
                  <el-radio v-if="userType === 1 || member.currencyCode==='CNY'" v-model="postForm.member.currencyCode" label="CNY">CNY</el-radio>
                  <el-radio v-if="userType === 1 || member.currencyCode==='THB'" v-model="postForm.member.currencyCode" label="THB">THB</el-radio>
                </el-form-item>
              </el-col>
            </el-row> -->
            <!-- <el-row>
              <el-col>
                <el-form-item :label="$t('agentForm.share')" class="postInfo-container-item">
                  <el-input-number v-model="postForm.member.percentAgent" :max="100" />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('member.commission')" class="postInfo-container-item">
                  <el-input-number v-model="postForm.member.commission" :max="100" />
                </el-form-item>
              </el-col>
            </el-row> -->
            <el-form-item v-show="false" :label="$t('agentForm.share')">
              <template>
                <div style=" margin-right: 14px;">
                  <el-slider v-model="postForm.member.percentAgent" :max="member.percentAgent" :marks="marks" />
                </div>
              </template>
            </el-form-item>
            <!--<el-collapse v-show="false" v-model="activeNames" class="mt-20">
              <el-collapse-item :title="$t('member.washRateInfo')" name="1">
                <el-table ref="multipleTable" size="small" :data="postForm.washItems" border highlight-current-row>
                  <el-table-column type="index" width="42" />
                  <el-table-column prop="price" :label="$t('member.codeWashingFrom')" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-input v-model.number="scope.row.washFrom" :disabled="isEdit" class="inline-input" :placeholder="$t('member.codeWashingFrom')" :trigger-on-focus="false" clearable border="false" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" :label="$t('member.codeWashingTo') + '('+$t('member.washTips') + ')'" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-input v-model.number="scope.row.washTo" :disabled="isEdit" class="inline-input" :placeholder="$t('member.codeWashingTo')" :trigger-on-focus="false" clearable border="false" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" :label="$t('member.rateOfCodeWashing') + '(%)'" align="center" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.percentWash" :disabled="isEdit" :placeholder="$t('member.rateOfCodeWashing') + '(%)'" :trigger-on-focus="false" clearable border="false" />
                    </template>
                  </el-table-column>
                  <el-table-column width="155" :label="$t('member.operation')" fixed="right" align="center" class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                      <el-button-group>
                        <el-button plain size="mini" :disabled="isEdit" icon="el-icon-plus" @click="plusRow(scope.$index,scope.row)" />
                        <el-button plain size="mini" :disabled="isEdit" icon="el-icon-minus" @click="minusRow(scope.$index,scope.row)" />
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>-->
            <el-row type="flex" justify="center" class="mt-10">
              <el-col :span="6">
                <el-form-item>
                  <el-row type="flex" justify="center" class="mt-10">
                    <el-button :loading="loading" type="primary" @click="submitForm">
                      {{ $t('operate.save') }}
                    </el-button>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>
<script>
import { addMember, fetchMember, genAccount } from '@/api/account/member'
import { mapGetters } from 'vuex'
import { checkAccountExist } from '@/api/account/accountCheck'
import { isValidPassword } from '@/utils/validate'
import { deepClone } from '@/utils/transferUtil'
import moment from 'moment'
const defaultForm = {
  user: {
    id: 0,
    userName: ''
  }, // 前台展示时间
  member: {
    // percentAgent: '0',
    memberType: 2,
    parentId: 0,
    currencyCode: 'CNY',
    memberLevel: '1',
    expiredDt: moment().add(1, 'days').format('YYYY-MM-DD HH:mm:ss')
  },
  washItems: [],
  removedItems: []
}

export default {
  name: 'AgentDetail',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('memberAddForm.validateMsg.account')))
      } else {
        checkAccountExist(
          value,
          2,
          this.postForm.user.id
        ).then(r => {
          if (r.data) {
            callback(new Error(this.$t('memberAddForm.validateMsg.accountExist')))
          } else {
            callback()
          }
        })
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.password')))
      } else if (!isValidPassword(value)) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordFormate')))
      } else {
        callback()
      }
    }
    const validateCheckPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordConfirm')))
      } else if (value !== this.postForm.user.password) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordDifferent')))
      } else {
        callback()
      }
    }
    return {
      activeName: 'first',
      postForm: Object.assign({}, defaultForm),
      loading: false,
      userListOptions: [],
      rules: {
        'user.userName': [{ validator: validateUsername }],
        'user.password': [{ validator: validatePassword }],
        'user.repeatPassword': [{ validator: validateCheckPassword }]
      },
      tempRoute: {},
      remoteLoading: false,
      activeNames: ['1', '2'],
      marks: {
        0: {
          style: {
            color: '#1989FA'
          },
          label: this.$createElement('strong', '0%')
        },
        25: {
          style: {
            color: '#1989FA'
          },
          label: this.$createElement('strong', '25%')
        },
        50: {
          style: {
            color: '#1989FA'
          },
          label: this.$createElement('strong', '50%')
        },
        75: {
          style: {
            color: '#1989FA'
          },
          label: this.$createElement('strong', '75%')
        },
        100: {
          style: {
            color: '#1989FA'
          },
          label: this.$createElement('strong', '100%')
        }
      },
      riskLevelOptions: [{ label: '稳', value: '1' }, { label: '中', value: '2' }, { label: '进', value: '3' }],
      riskLevel: []
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'userName',
      'userType',
      'device'
    ])
  },
  created() {
    if (this.isEdit) {
      const id = this.$route.params && this.$route.params.id
      this.fetchData(id)
    } else {
      this.postForm = deepClone(defaultForm)
      this.postForm.member.currencyCode = this.member.currencyCode
      this.initEmbedTable(1)
    }
    this.tempRoute = Object.assign({}, this.$route)
  },
  methods: {
    genUserName() {
      this.remoteLoading = true
      genAccount(2).then(r => {
        this.postForm.user.userName = r.data
        this.remoteLoading = false
      })
    },
    fetchData(id) {
      fetchMember(id).then(response => {
        console.log('fetchMember............', response)
        this.postForm.member = response.data
        this.postForm.user = response.data.user
        this.postForm.user.repeatPassword = this.postForm.user.password
        this.postForm.washItems = response.data.memberWashList
        this.postForm.member.memberLevel = this.postForm.member.memberLevel ? this.postForm.member.memberLevel + '' : ''
        if (this.postForm.washItems.length > 0) {
          this.postForm.member.commission = this.postForm.washItems[0].percentWash
        }
        this.riskLevel = this.postForm.member.riskLevel.split(',')
        console.log('this.riskLevel...........1', this.riskLevel)
      }).catch(err => {
        console.log(err)
      })
    },
    submitForm() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          if (!this.riskLevel.length) {
            this.$message.error('请选择风险等级')
            return
          }
          console.log('this.riskLevel...........2', this.riskLevel)
          this.postForm.member.riskLevel = this.riskLevel.join(',')
          this.postForm.member.defaultLevel = this.riskLevel[0]
          this.loading = true
          if (!this.postForm.washItems) {
            this.postForm.washItems = []
            this.postForm.washItems.push({ percentWash: this.postForm.member.commission, washFrom: 0, washTo: 0 })
          }
          if (this.postForm.washItems.length === 0) {
            this.$message({
              showClose: true,
              message: this.$t('memberAddForm.validateMsg.rateSetting'),
              type: 'error'
            })
            this.loading = false
            return false
          } else {
            if (!this.checkBetLimit()) {
              this.loading = false
              return
            }
            this.postForm.washItems[0].percentWash = this.postForm.member.commission
            this.postForm.washItems[0].washFrom = 0
            this.postForm.washItems[0].washTo = 0
            for (var i = 0; i < this.postForm.washItems.length; i++) {
              if (this.postForm.washItems[i].percentWash > 6) {
                this.$message({
                  showClose: true,
                  message: this.$t('memberAddForm.validateMsg.rateLimit'),
                  type: 'error'
                })
                this.loading = false
                return false
              }
            }
          }
          if (this.member.memberType === 4) {
            this.postForm.member.parentId = this.member.parentId
          } else {
            this.postForm.member.parentId = this.member.id
          }
          console.log('this.postForm..............1', this.postForm)
          addMember(this.postForm).then(r => {
            if (this.isEdit) {
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$message({
                message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
                type: 'success',
                duration: 2000
              })
              this.postForm = deepClone(defaultForm)
              this.initEmbedTable(1)
              this.$refs.postForm.resetFields()
              this.riskLevel = []
            }
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    initEmbedTable(size) {
      var i = 0
      while (i < size) {
        i++
        var tempObject = { id: 0 }
        this.$set(tempObject, 'willShow', true)
        this.postForm.washItems.push(tempObject)
      }
    },
    plusRow(index, val) {
      var startData = this.postForm.washItems.slice(0, index + 1)
      var endData = this.postForm.washItems.slice(index + 1, this.postForm.washItems.length)
      var tempObject = { id: 0, willShow: true }
      startData.push(tempObject)
      this.postForm.washItems = startData.concat(endData)
    },
    minusRow(index, row) {
      if (this.postForm.washItems.length === 1) {
        return
      }
      if (!this.postForm.removedItems) {
        this.postForm.removedItems = []
      }
      this.postForm.removedItems.push(row.id)
      this.postForm.washItems.splice(index, 1)
    },
    checkBetLimit() {
      if (this.member.betMin) {
        if (this.postForm.member.betMin > this.member.betMin) {
          this.$message.error(this.$t('member.betMin') + this.$t('member.dataError') + this.member.betMin)
          return false
        }
      }
      if (this.member.betMax) {
        if (this.postForm.member.betMax > this.member.betMax) {
          this.$message.error(this.$t('member.betMax') + this.$t('member.dataError') + this.member.betMax)
          return false
        }
      }
      if (this.member.betMax2) {
        if (this.postForm.member.betMax2 > this.member.betMax2) {
          this.$message.error(this.$t('member.betMax2') + this.$t('member.dataError') + this.member.betMax2)
          return false
        }
      }
      if (this.member.betMax3) {
        if (this.postForm.member.betMax3 > this.member.betMax3) {
          this.$message.error(this.$t('member.betMax3') + this.$t('member.dataError') + this.member.betMax3)
          return false
        }
      }
      if (this.member.maxWinPoint) {
        if (this.postForm.member.maxWinPoint > this.member.maxWinPoint) {
          this.$message.error(this.$t('member.maxWin') + this.$t('member.dataError') + this.member.maxWinPoint)
          return false
        }
      }
      if (this.member.bet2Min) {
        if (this.postForm.member.bet2Min > this.member.bet2Min) {
          this.$message.error(this.$t('member.betMin') + this.$t('member.dataError') + this.member.bet2Min)
          return false
        }
      }
      if (this.member.bet2Max) {
        if (this.postForm.member.bet2Max > this.member.bet2Max) {
          this.$message.error(this.$t('member.betMax') + this.$t('member.dataError') + this.member.bet2Max)
          return false
        }
      }
      if (this.member.bet2Max2) {
        if (this.postForm.member.bet2Max2 > this.member.bet2Max2) {
          this.$message.error(this.$t('member.betMax2') + this.$t('member.dataError') + this.member.bet2Max2)
          return false
        }
      }
      if (this.member.bet2Max3) {
        if (this.postForm.member.bet2Max3 > this.member.bet2Max3) {
          this.$message.error(this.$t('member.betMax3') + this.$t('member.dataError') + this.member.bet2Max3)
          return false
        }
      }
      if (this.member.maxWinPoint2) {
        if (this.postForm.member.maxWinPoint2 > this.member.maxWinPoint2) {
          this.$message.error(this.$t('member.maxWin') + this.$t('member.dataError') + this.member.maxWinPoint2)
          return false
        }
      }
      return true
    }
  }
}
</script>
<style>
.input-with-select .el-input-group__append {
  background-color: #fff;
  color: #006dfe;
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.createPost-container {
  position: relative;

  .createPost-main-container {
    padding: 40px 45px 20px 50px;
    margin-left: 10px;

    .postInfo-container {
      position: relative;
      @include clearfix;
      margin-bottom: 10px;

      .postInfo-container-item {
        float: left;
      }
    }
  }

  .word-counter {
    width: 40px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
}

.article-textarea /deep/ {
  textarea {
    padding-right: 40px;
    resize: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #bfcbd9;
  }
}
</style>
