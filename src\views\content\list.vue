<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px; ">
      <el-col :span="8">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddClick"
        >新增文章
        </el-button>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <CategorySelect @getSelectedCategory="getSelectedCategory" />
          <el-input v-model="listQuery.title" style="width:160px;margin-left:10px" placeholder="根据标题查询" clearable />
          <el-button icon="fa fa-search" type="primary" @click="getList" />
        </el-row>
      </el-col>
    </el-row>
    <el-table :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column width="120px" align="center" label="文章编号">
        <template slot-scope="scope">
          <span>{{ scope.row.articleNo }}</span>
        </template>
      </el-table-column>
      <el-table-column width="180px" align="center" label="发布时间">
        <template slot-scope="scope">
          <span>{{ scope.row.createDt | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column width="120px" align="center" label="作者">
        <template slot-scope="scope">
          <span>{{ scope.row.author }}</span>
        </template>
      </el-table-column>
      <el-table-column width="100px" label="排序信息">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNo }}</span>
        </template>
      </el-table-column>
      <el-table-column width="240px" align="center" label="摘要">
        <template slot-scope="scope">
          <span>{{ scope.row.author }}</span>
        </template>
      </el-table-column>
      <el-table-column min-width="300px" label="标题">
        <template slot-scope="{row}">
          <router-link :to="'/article-manage/edit-article/'+row.id" class="link-type">
            <span>{{ row.title }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="120">
        <template slot-scope="scope">
          <router-link :to="'/article-manage/edit-article/'+scope.row.id">
            <el-button plain size="small" icon="el-icon-edit">
              编辑
            </el-button>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>

<script>
import { listArticle } from '@/api/content/article'
import CategorySelect from '@/components/CategorySelect/index'
export default {
  name: 'ArticleList',
  components: { CategorySelect },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        oneCategoryId: '',
        twoCategoryId: '',
        pageNo: 1,
        pageSize: 10
      },
      currentPage: 1,
      pageSize: 10
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleAddClick() {
      this.$router.push('/article-manage/create-article')
    },
    getSelectedCategory(oneLevelCategoryId, twoLevelCategoryId) {
      this.listQuery.oneCategoryId = oneLevelCategoryId
      this.listQuery.twoCategoryId = twoLevelCategoryId
    },
    getList() {
      this.listLoading = true
      listArticle(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
      })
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNo = val
      this.getData()
    }
  }
}
</script>

<style scoped>
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style>
