<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane :label="$t('jettonSettle.memberTittle')" name="first">
        <member-settle />
      </el-tab-pane>
      <el-tab-pane :label="$t('jettonSettle.agentTittle')" name="second">
        <agent-settle />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MemberSettle from './member/index'
import AgentSettle from './agent/index'
export default {
  name: 'Settle',
  components: {
    'member-settle': MemberSettle,
    'agent-settle': AgentSettle
  },
  data() {
    return {
      activeName: 'first'
    }
  }
}
</script>

<style scoped>

</style>
