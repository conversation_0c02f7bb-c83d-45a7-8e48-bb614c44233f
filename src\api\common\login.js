import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo(token, goCache) {
  return request({
    url: '/user/info',
    method: 'get',
    params: {
      token: token,
      goCache: goCache
    }
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}
export function checkUser(username, userType) {
  return request({
    url: '/user/checkUser',
    method: 'post',
    params: {
      username: username,
      userType: userType
    }
  })
}
export function checkCaptchaCode(captchaCode, publicKey) {
  return request({
    url: '/captcha/checkCaptchaCode',
    method: 'get',
    params: {
      captchaCode: captchaCode,
      publicKey: publicKey
    }
  })
}
