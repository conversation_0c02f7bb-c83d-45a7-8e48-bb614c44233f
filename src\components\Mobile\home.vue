<template>
  <div ref="wrapper" class="mobile-home">
    <mobile-header title="首页" :back="false">
      <!--<template #right>
        <i class="el-icon-s-custom real-name" />{{ userName }}
      </template> -->
    </mobile-header>
    <el-carousel height="150px" class="carousel">
      <el-carousel-item>
        <img src="../../assets/other/carousel1.jpg">
      </el-carousel-item>
      <el-carousel-item>
        <img src="../../assets/other/carousel2.jpg">
      </el-carousel-item>
    </el-carousel>
    <div class="main-content">
      <!--快捷图标 开始-->
      <div class="grid">
        <div class="item" @click="shortcutClick('dealer')">
          <img src="../../assets/images/add.png">
          <div class="text">SPD</div><!--算牌端-->
        </div>
        <div class="item" @click="shortcutClick('player')">
          <img src="../../assets/images/approve.png">
          <div class="text">NXM</div><!--下注端-->
        </div>
        <div class="item" @click="shortcutClick('betList2')">
          <img src="../../assets/images/bet.png">
          <div class="text">NXM-JL</div><!--下注端-->
        </div>
        <div class="item" @click="shortcutClick('betList')">
          <img src="../../assets/images/bet.png">
          <div class="text">XZJL</div><!--下注端-->
        </div>

        <div class="item" @click="shortcutClick('logout')">
          <img src="../../assets/images/exit.png">
          <div class="text">TCXT</div><!--退出系统-->
        </div>
        <div class="clear" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'MobileHome',
  data() {
    return {
      shortcutList: [
        { text: '运单录入', routeName: 'TransportBill', role: [1], queryType: 0 }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'nickName',
      'userName',
      'memberWashList',
      'device'
    ])
  },
  methods: {
    shortcutClick(val) {
      if (val === 'logout') {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push({ path: '/login' })
        })
      } else if (val === 'dealer') {
        this.$router.push({ path: '/game-bet/dealer' })
      } else if (val === 'player') {
        this.$router.push({
          path: '/game-bet/player2'
        })
      } else if (val === 'betList') {
        this.$router.push({ path: '/game-bet/bet-list' })
      } else if (val === 'betList2') {
        this.$router.push({ path: '/game-bet/history2' })
      }
    }
  }
}
</script>

<style>
.real-name {
  font-size: 14px;
  margin-right: 5px;
}

.carousel {
  margin-top: 40px;
  height: 150px;
  width: 100%;
}

.carousel img {
  width: 100%;
  height: 100%;
}

/* 快捷菜单 */
.main-content {
  margin-top: 5px;
  margin-left: 5px;
  margin-right: 5px;
  padding: 5px;
  border-radius: 5px;
  background-color: #ffffff;
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  /* 相当于 1fr 1fr 1fr */
  grid-gap: 1px;
  /* grid-column-gap 和 grid-row-gap的简写 */
  grid-auto-flow: row;
  background-color: #f5f7fa;
  padding: 1px
}

.grid .item {
  text-align: center;
  background: white;
}

.grid .item img {
  padding-top: 20%;
  width: 20%;
}

.grid .item .text {
  text-align: center;
  color: #000000;
  font-size: 12px;
  padding-top: 5%;
  padding-bottom: 20%;
}
</style>
