<template>
  <div class="game-container" :style="'background-color:' + (isBlocked ? '#ffffff' : '') + ';position:absolute;left:'+ left +'px;top:' + top +'px;width:'+width+'px;height:' + height +'px;' ">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </div>
</template>

<script>
import { isMobile, isApp, isNoDesktop } from '@/utils'
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'LayoutLogin',
  mixins: [ResizeMixin],
  data() {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      tempWidth: window.innerWidth,
      tempHeight: window.innerHeight,
      left: 0,
      top: 0,
      setWidth: 1920,
      setHeight: 1080,
      maxScale: 16 / 7.5,
      resizeTimeOut: null,
      isMobileOrApp: false
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    },
    isBlocked() {
      return this.$store.state.app.isBlocked
    }
  },
  mounted() {
    /* window.onresize = () => {
      var _this = this
      return (() => {
        if (this.resizeTimeOut) {
          clearTimeout(this.resizeTimeOut)
        }
        this.resizeTimeOut = setTimeout(function() {
          _this.onResize()
        }, 300)
      })()
    }
    this.onResize() */
  },
  methods: {
    onResize() {
      var res = isMobile() || isApp()
      var ww = window.innerWidth
      var wh = window.innerHeight
      var rate = this.setWidth / this.setHeight
      var w1 = ww
      var h1 = Math.round(w1 / rate)
      var h2 = wh
      var w2 = Math.round(h2 * rate)
      if (res) {
        this.width = ww
        this.height = wh
        this.top = (wh - this.height) / 2
        this.left = (ww - this.width) / 2
        document.documentElement.style.fontSize = (this.width / 35) + 'px'
      } else if (isNoDesktop()) {
        res = isNoDesktop()
        this.width = 435
        this.height = wh
        this.top = (wh - this.height) / 2
        this.left = (ww - this.width) / 2
        document.documentElement.style.fontSize = (this.width / 35) + 'px'
      } else {
        this.width = (w1 < w2) ? w1 : w2
        this.height = (w1 < w2) ? h1 : h2
        this.top = (wh - this.height) / 2
        this.left = (ww - this.width) / 2
        document.documentElement.style.fontSize = (this.width / 100) + 'px'
        console.log('game layout top:' + this.top + '/left:' + this.left + '/width:' + this.width + '/height:' + this.height)
      }
      this.$store.dispatch('app/toggleDevice', res ? 'mobile' : 'desktop')
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
 .game-container {
    overflow:hidden;
    position:absolute;
    pointer-events:visible;
    -webkit-transform-origin:top left;
    transform-origin:top left;
    z-index:1;
    background-size: 100% 100%;
 }
</style>
