<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">{{
            $t('operate.add') }}</el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-input v-model="agentParam.userName" style="width:184px" :placeholder="$t('subAccount.placeholder')"
            clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border
      highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="accountStatus" :label="$t('subAccount.accountStatus')" align="center" width="110">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.accountStatus" type="success">{{ $t('status.open') }}</el-tag>
          <el-tag v-else type="danger">{{ $t('status.closed') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="user.userName" align="center" :label="$t('subAccount.subordinateAgent')" width="164">
        {{ agentInfo }}
      </el-table-column>
      <el-table-column prop="user.userName" align="center" :label="$t('subAccount.account')" width="124" />
      <el-table-column prop="user.nickName" align="center" :label="$t('subAccount.nickname')" width="114" />
      <el-table-column prop="fullName" align="center" :label="$t('subAccount.name')" width="100" />
      <el-table-column prop="phoneNo" align="center" :label="$t('subAccount.phone')" width="144" />
      <el-table-column prop="isOnline" align="center" :label="$t('subAccount.onLine')" width="72">
        <template slot-scope="scope">
          {{ scope.row.isOnline ? $t('status.onLine') : $t('status.offLine') }}
        </template>
      </el-table-column>
      <el-table-column prop="createDt" :label="$t('subAccount.createdTime')" align="center" width="144"
        :formatter="dateTimeFormat" />
      <el-table-column prop="remark" :label="$t('subAccount.remark')" show-overflow-tooltip />
      <el-table-column fixed="right" align="center" :label="$t('subAccount.operation')" width="314">
        <template slot-scope="scope">
          <el-button-group>
            <el-button v-if="!scope.row.accountStatus" v-show="accountOperate" plain type="success" size="mini"
              @click="handleAccountClick(scope.row)">
              <span>{{ $t('operate.accountEnabled') }}</span>
            </el-button>
            <el-button v-else v-show="accountOperate" plain type="danger" size="mini"
              @click="handleAccountClick(scope.row)">
              <span>{{ $t('operate.accountDisabled') }}</span>
            </el-button>
            <el-dropdown>
              <el-button plain size="mini">
                {{ $t('operate.moreOperate') }}<i class="el-icon-arrow-down el-icon--right" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-show="password" @click.native="handlePassSetClick(scope.row)"> {{
                  $t('operate.passwordChange') }}</el-dropdown-item>
                <el-dropdown-item v-show="authSetting" @click.native="handleAuthClick(scope.row)"> {{
                  $t('operate.authSetting') }}</el-dropdown-item>
                <el-dropdown-item v-show="authSetting" @click.native="handleEditClick(scope.row)"> {{ $t('operate.edit')
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('subAccount.subAccountInfo')" :visible.sync="dialogFormVisible" class="form-item-normal"
      :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="40%">
      <el-form ref="postForm" label-width="80px" :model="object" label-suffix=":" :rules="rules" class="form-container">
        <div class="createPost-main-container">
          <el-form-item :label="$t('subAccount.subordinateAgent')">
            {{ agentInfo }}
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('subAccount.account')" prop="user.userName">
                <el-input v-model="object.user.userName" :disabled="isEdit" clearable>
                  <el-button slot="append" :disabled="isEdit" :loading="remoteLoading" type="text"
                    @click="genUserName">{{
                      $t('operate.sysgen') }}</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('subAccount.nickname')">
                <el-input v-model="object.user.nickName" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('subAccount.password')" prop="user.password">
                <el-input v-model="object.user.password" :disabled="isEdit" type="password" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('subAccount.confirmPassword')" prop="user.passwordRepeat">
                <el-input v-model="object.user.passwordRepeat" :disabled="isEdit" type="password" clearable />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('subAccount.name')">
                <el-input v-model="object.member.fullName" clearable />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item :label="$t('subAccount.phone')">
                <el-input v-model="object.member.phoneNo" clearable />
              </el-form-item>
            </el-col>-->
            <el-col :span="12">
              <el-form-item label="到期时间" class="postInfo-container-item">
                <el-date-picker v-model="object.member.expiredDt" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                  class="w-100p" clearable />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="loading" @click="submitSubAccountEdit">{{ $t('operate.save') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="$t('subAccount.subAccountAuth')" :visible.sync="dialogAuthVisible" class="form-item-normal"
      :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="40%">
      <el-table ref="menuTreeForm" row-key="menuId" :data="menuButtonOptionTree"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" border>
        <el-table-column prop="menuId" label="ID" width="104" />
        <el-table-column prop="menuText" :label="$t('menu.menuName')" width="104" />
        <el-table-column fixed="right" align="center" :label="$t('menu.operation')">
          <template slot-scope="scope">
            <el-checkbox-group v-model="scope.row.relatedButtonIds" size="mini">
              <el-checkbox-button v-for="item in scope.row.buttonOptionList" :key="item.buttonId" :label="item.buttonId"
                border>{{ item.buttonText }}</el-checkbox-button>
            </el-checkbox-group>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAuthVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" @click="submitAuthClick">{{ $t('operate.save') }}</el-button>
      </div>
    </el-dialog>
    <change-password :visible="dialogPassVisible" :user-id="currentUserId" @cancelCall="passCancel"
      @doneCall="passDone" />
  </div>
</template>
<script>
import { addMember, listMemberInfo, genAccount, doAccountStatusChange } from '@/api/account/member'
import { listUserMenuButtonOption } from '@/api/authorization/menu'
import { updateUserAuth } from '@/api/authorization/auth'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { getToken } from '@/utils/auth' // 验权
import ChangePass from '../components/ChangePass.vue'
import { checkAccountExist } from '@/api/account/accountCheck'
import { isValidPassword } from '@/utils/validate'
import { deepClone } from '@/utils/transferUtil'
export default {
  name: 'SubAccountList',
  components: {
    'change-password': ChangePass
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    password: {
      type: Boolean,
      default: false
    },
    accountOperate: {
      type: Boolean,
      default: false
    },
    authSetting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('memberAddForm.validateMsg.account')))
      } else {
        checkAccountExist(
          value,
          4,
          this.object.user.id
        ).then(r => {
          if (r.data) {
            callback(new Error(this.$t('memberAddForm.validateMsg.accountExist')))
          } else {
            callback()
          }
        })
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.password')))
      } else if (!isValidPassword(value)) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordFormate')))
      } else {
        callback()
      }
    }
    const validateCheckPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordConfirm')))
      } else if (value !== this.object.user.password) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordDifferent')))
      } else {
        callback()
      }
    }
    return {
      menuButtonOptionTree: [],
      agentParam: {
        memberType: 4
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogAuthVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      object: {
        user: {
          avatar: '',
          userName: '',
          nickName: ''
        },
        member: {
          percentAgent: 0,
          memberType: 4,
          parentId: 0
        }
      },
      rules: {
        'user.userName': [{ validator: validateUsername }],
        'user.password': [{ validator: validatePassword }],
        'user.passwordRepeat': [{ validator: validateCheckPassword }]
      },
      roleParam: {},
      loading: false,
      memberStatusParam: {},
      remoteLoading: false,
      isEdit: false
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'nickName',
      'userName'
    ]),
    myHeaders: function () {
      return {
        'X-Token': getToken()
      }
    },
    'agentInfo': function () {
      return this.userName + '(' + this.nickName + ')'
    }
  },
  watch: {
    multipleSelection: function () {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    genUserName() {
      this.remoteLoading = true
      genAccount(4).then(r => {
        this.object.user.userName = r.data
        this.remoteLoading = false
      })
    },
    handlePassSetClick(data) {
      this.currentUserId = data.user.id
      this.dialogPassVisible = true
    },
    passCancel() {
      this.dialogPassVisible = false
    },
    passDone(data) {
      this.dialogPassVisible = false
    },
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      this.agentParam.parentId = this.member.id
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = this.currentPage
      listMemberInfo(this.agentParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    initMenuButtonOption(userId) {
      listUserMenuButtonOption(userId).then(r => {
        this.menuButtonOptionTree = r.data
      })
    },
    handleAddClick() {
      this.dialogFormVisible = true
      this.object.user = {
        avatar: '',
        userName: '',
        nickName: ''
      }
      this.object.member =
      {
        percentAgent: 0,
        memberType: 4,
        parentId: 0
      }
      this.$refs.postForm.resetFields()
    },
    handleEditClick(val) {
      this.object.member = deepClone(val)
      this.object.user = deepClone(val.user)
      this.object.user.passwordRepeat = this.object.user.password
      this.isEdit = true
      this.dialogFormVisible = true
    },
    handleAuthClick(data) {
      this.roleParam.userId = data.user.id
      this.initMenuButtonOption(data.user.id)
      this.dialogAuthVisible = true
    },
    submitAuthClick() {
      const menuButtonKeys = []
      this.parseMenuParam(this.menuButtonOptionTree, menuButtonKeys)
      var key = { menuId: 15, buttonIdList: [2] }
      var key1 = { menuId: 16, buttonIdList: [2] }
      menuButtonKeys.push(key)
      menuButtonKeys.push(key1)
      this.roleParam.menuButtonKeys = menuButtonKeys
      console.log('submitAuthClick................', menuButtonKeys, this.roleParam)
      updateUserAuth(this.roleParam).then(r => {
        this.dialogAuthVisible = false
      })
    },
    parseMenuParam(treeData, menuButtonKeys) {
      var _this = this
      treeData.forEach(function (item, index, array) {
        if (item.relatedButtonIds.length > 0) {
          menuButtonKeys.push({ menuId: item.menuId, buttonIdList: item.relatedButtonIds })
        }
        if (item.children.length > 0) {
          _this.parseMenuParam(item.children, menuButtonKeys)
        }
      })
    },
    handleAccountClick(val) {
      var message = ''
      this.memberStatusParam.agentId = val.id
      this.memberStatusParam.accountStatus = !val.accountStatus ? 1 : 0
      this.memberStatusParam.isDisabledAlone = 0
      this.memberStatusParam.memberType = 2
      if (!val.accountStatus) {
        message = this.$t('operate.accountEnabled')
      } else {
        message = this.$t('operate.accountDisabled')
      }
      this.$confirm(message + '!' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        doAccountStatusChange(this.memberStatusParam).then(r => {
          this.$message({
            message: message + '.' + this.$t('operate.message.success') + '!',
            type: 'success',
            center: true
          })
          this.listTableData()
        })
      }).catch(() => {
        this.$message({
          message: this.$t('operate.message.cancel') + '!',
          type: 'success',
          center: true
        })
      })
    },
    submitSubAccountEdit() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.object.member.parentId = this.member.id
          addMember(this.object).then(r => {
            this.dialogFormVisible = false
            this.loading = false
            this.listTableData()
          })
        }
      })
    },
    setAvatar() {
      this.avatarShow = true
    },
    cropSuccess(imgDataUrl, field) {
      this.imgDataUrl = imgDataUrl
    },
    cropUploadSuccess(jsonData, field) {
      this.avatarUrl = jsonData.data[0].url
      this.object.user.avatar = this.avatarUrl
    },
    cropUploadFail(status, field) {
      console.log(status)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function (row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
<style>
.input-with-select .el-input-group__append {
  background-color: #fff;
  color: #006dfe;
}
</style>
