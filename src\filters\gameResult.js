import { getBaccaratResult1Enum, getBaccaratResult2Enum, getDragonAndTiger1Enum, getDragonAndTiger2Enum } from '@/enums/gameResult'
export function gameResultFilter(gameType, gameResult) {
  if (!gameResult) {
    return ''
  }
  var gameResults = gameResult.split('|')
  if (gameResults.length < 2) {
    return ''
  }
  var gameResultItems = []
  switch (Number(gameType)) {
    case 1:
      gameResultItems.push(getBaccaratResult1Enum(gameResults[0]).label)
      if (getBaccaratResult2Enum(gameResults[1]).label) {
        gameResultItems.push(getBaccaratResult2Enum(gameResults[1]).label)
      }
      break
    case 2:
      gameResultItems.push(getDragonAndTiger1Enum(gameResults[0]).label)
      if (getDragonAndTiger2Enum(gameResults[1]).label) {
        gameResultItems.push(getDragonAndTiger2Enum(gameResults[1]).label)
      }
      break
    case 3:
      if (gameResults[0] === '1' && gameResults[1] === '1' && gameResults[2] === '1' && gameResults[3] === '1') {
        gameResultItems.push('bull.bankerWin')
      } else {
        if (gameResults[1] === '2') {
          gameResultItems.push('bull.player1Win')
        }
        if (gameResults[2] === '2') {
          gameResultItems.push('bull.player2Win')
        }
        if (gameResults[3] === '2') {
          gameResultItems.push('bull.player3Win')
        }
      }
      break
  }
  return gameResultItems
}
