import router from './router'
import store from './store'
import NProgress from 'nprogress' // Progress 进度条
import 'nprogress/nprogress.css'// Progress 进度条样式
import { Message } from 'element-ui'
import { getToken } from '@/utils/auth' // 验权

const whiteList = ['/login', '/register', '/forgetpwd', '/forgetpwd/verifyByEmail', '/company-register', '/member-register', '/driver-register'] // 不重定向白名单
router.beforeEach((to, from, next) => {
  NProgress.start()
  console.log('to..........................')
  console.log(to)
  if (getToken()) {
    if (to.path === '/login') {
      next({ path: '/dashboard/index' })
    } else {
      if (!store.getters.userId) {
        store.dispatch('GetInfo').then(() => { // 拉取用户信息
          const userInfo = {}
          userInfo.userId = store.getters.userId
          userInfo.systemId = store.getters.systemId
          userInfo.userType = store.getters.userType
          store.dispatch('GenerateRoutes', userInfo).then(() => { // 根据roles权限生成可访问的路由表
            router.addRoutes(store.getters.addRouters) // 动态添加可访问路由表
            console.log('store.getters.addRouters.....................')
            console.log(store.getters.addRouters)
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
          })
        }).catch(() => {
          store.dispatch('FedLogOut').then(() => {
            Message.error('验证失败,请重新登录')
            next({ path: '/login' })
          })
        })
      } else if (to.matched.length === 0) { // 如果未匹配到路由
        next({ path: '/404' })
      } else {
        next()
      }
    }
  } else {
    console.log('to.path' + to.path)
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next('/login')
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done() // 结束Progress
})
