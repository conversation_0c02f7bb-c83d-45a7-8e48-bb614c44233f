<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd">
            {{ $t('operate.add') }}
          </el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete">
            删除
          </el-button>
          <el-button size="mini" icon="el-icon-document-copy" @click="handleCopy">
            复制
          </el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-input v-model="searchParam.verifyName" clearable class="w-150" placeholder="验证名称" />
          <el-button icon="fa fa-search" type="primary" @click="handleSearch" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="modelList" tooltip-effect="dark" style="width: 100%" border
      highlight-current-row :height="height" @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="verifyName" label="验证名称" align="center" width="200" />
      <!--<el-table-column prop="gameType" label="游戏类型" align="center" width="80" /> -->
      <el-table-column prop="bankerAmount" label="庄下注" align="center" width="80" />
      <!--<el-table-column prop="playerAmount" label="闲下注" align="center" width="80" />
      <el-table-column prop="tieAmount" label="和下注" align="center" width="80" /> -->
      <el-table-column prop="totalAmount" label="总下注" align="center" width="80" />
      <el-table-column prop="bankerCount" label="庄赢" align="center" width="80" />
      <!--<el-table-column prop="playerCount" label="闲赢" align="center" width="80" />
      <el-table-column prop="tieCount" label="和赢" align="center" width="80" /> -->
      <el-table-column prop="matchCount" label="靴数" align="center" width="80" />
      <el-table-column prop="winAmount" label="盈利" align="center" width="80">
        <template slot-scope="scope">
          <span :class="scope.row.winAmount > 0 ? 'c-blue' : 'c-red'"> {{ scope.row.winAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="winCount" label="赢局数" align="center" width="80" />
      <el-table-column prop="loseCount" label="输局数" align="center" width="80" />
      <el-table-column prop="ruleName" label="规则名称" align="center" width="144">
        <template slot-scope="scope">
          <div class="pointer" @click="handleSetting(scope.row)">
            {{ scope.row.ruleName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createDt" :label="$t('member.createdTime')" align="center" width="144"
        :formatter="dateTimeFormat" />
      <el-table-column prop="statusName" label="状态" align="center" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 2" class="c-red"> {{ scope.row.statusName }}</span>
          <span v-else-if="scope.row.status === 3" class="c-blue"> {{ scope.row.statusName }}</span>
          <span v-else> {{ scope.row.statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="maxAmount" label="峰值" align="center" width="80">
        <template slot-scope="scope">
          <span :class="scope.row.maxAmount > 0 ? 'c-blue' : 'c-red'"> {{ scope.row.maxAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="minAmount" label="谷值" align="center" width="80">
        <template slot-scope="scope">
          <span :class="scope.row.minAmount > 0 ? 'c-blue' : 'c-red'"> {{ scope.row.minAmount }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="commissionAmount" label="抽佣" align="center" width="80" />
      <el-table-column prop="washAmount" label="总洗码" align="center" width="80" />
      <el-table-column prop="bankerWashAmount" label="庄洗码" align="center" width="80" />
      <el-table-column prop="playerWashAmount" label="闲洗码" align="center" width="80" /> -->
      <el-table-column fixed="right" align="center" :label="$t('member.operation')" width="100">
        <template slot-scope="scope">
          <el-row>
            <el-col>
              <el-dropdown>
                <el-button plain size="mini">
                  {{ $t('member.operation') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-edit" @click.native="handleEdit(scope.row)">
                    修改
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 0 || scope.row.status === 3" icon="el-icon-check"
                    @click.native="handleStartVerify(scope.row)">
                    开始验证
                  </el-dropdown-item>
                  <!--<el-dropdown-item @click.native="handleStopVerify(scope.row)">
                    停止验证
                  </el-dropdown-item>-->
                  <el-dropdown-item icon="el-icon-s-data" @click.native="handleProfitResult(scope.row)">
                    盈利曲线
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-s-data" @click.native="handleWinResult(scope.row)">
                    胜负曲线
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-download" @click.native="handleExport(scope.row)">
                    数据导出
                  </el-dropdown-item>
                  <el-dropdown-item icon="el-icon-download" @click.native="handleExportStat(scope.row)">
                    统计导出
                  </el-dropdown-item>
                  <!--<el-dropdown-item icon="el-icon-download" @click.native="handleExportWinRoad(scope.row)">
                    胜负路(纵向)
                  </el-dropdown-item> -->
                  <el-dropdown-item icon="el-icon-download" @click.native="handleExportWinRoad2(scope.row)">
                    胜负路(横向)
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="searchParam.pageNo" :page-sizes="[10, 50, 100, 200]"
        :page-size="searchParam.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="searchParam.total"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog title="验证" :visible.sync="visibleTags.add" class="form-item-normal" :close-on-press-escape="false"
      :close-on-click-modal="false" top="60px" width="600px">
      <el-form ref="modelForm" :model="model" label-width="120px">
        <el-form-item label="验证名称" prop="verifyName">
          <el-input v-model="model.verifyName" clearable />
        </el-form-item>
        <el-form-item label="验证规则" prop="ruleName">
          <el-input v-model="model.ruleName" clearable disabled />
        </el-form-item>
        <el-form-item label="批量验证" v-if="!model.id">
          <el-checkbox v-model="model.batchAdd" :true-label="1" :false-label="2" />
          <span class="text ml-10">共验证</span>
          <el-input v-model="model.batchTotal" class="input" :disabled="model.batchAdd !== 1" />
          <span class="text">次</span>
        </el-form-item>
        <el-form-item label="每次验证" v-if="!model.id">
          <el-input v-model="model.batchQty" class="input" :disabled="model.batchAdd !== 1" />
          <span class="text">靴</span>
          <span class="text ml-10">从</span>
          <el-input v-model="model.batchStart" class="input" :disabled="model.batchAdd !== 1" />
          <span class="text">靴开始验证</span>
        </el-form-item>
        <!--<el-form-item label="备注" prop="remark">
          <el-input v-model="model.remark" type="textarea" rows="3" clearable />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visibleTags.add = false"> {{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="loadingTags.add" @click="handleSave">{{ $t('operate.confirm') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="visibleTags.profit" class="form-item-normal" :close-on-press-escape="false"
      :close-on-click-modal="false" top="60px" width="1200px">
      <line-chart ref="lineChart" width="100%" height="500px" :x-data="xData" :y-data="yData" />
      <div slot="footer" class="dialog-footer hide">
        <el-button type="primary" @click="visibleTags.profit = false">{{ $t('operate.confirm') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog title="规则设置" :visible.sync="visibleTags.setting" class="form-item-normal" :close-on-press-escape="false"
      :close-on-click-modal="false" top="60px" width="800px" style="height:80%">
      <setting ref="setting" width="100%" @submit="handleSubmit" />
    </el-dialog>
  </div>
</template>

<script>
  import { list, del, add, addList, update } from '@/api/game/gameVerify'
  import { getByRuleNo } from '@/api/setting/gameRule'
  import LineChart from '../../components/LineChart'
  import moment from 'moment'
  import { listByVerifyId } from '@/api/game/gameTransHist'
  import Setting from './setting'
  import { formatNumber } from '@/utils/formatter'

  export default {
    name: 'GameSetting',
    components: { LineChart, Setting },
    data() {
      return {
        ruleNo: 'LUCK_SIX_SETTING',
        searchParam: {
          verifyNo: 'LUCK_SIX_SETTING',
          verifyName: '',
          pageNo: 1,
          total: 0,
          pageSize: 10
        },
        modelList: [],
        model: { verifyNo: 'LUCK_SIX_SETTING', verifyName: '', ruleNo: '', ruleName: '', remark: '', batchAdd: 2, batchTotal: null, batchQty: null, batchStart: null },
        multipleSelection: [],
        visibleTags: { add: false, profit: false, setting: false },
        loadingTags: { add: false },
        gameRule: {},
        title: '盈利曲线',
        xData: [],
        yData: [],
        id: 0,
        height: 500
      }
    },
    watch: {
      multipleSelection: function () {
        const arr = []
        for (const i in this.multipleSelection) {
          arr.push(this.multipleSelection[i].id)
        }
        this.ids = arr.join()
      }
    },
    created() {
      this.getRuleNo()
      this.handleSearch()
      this.hanldeHeight()
    },
    methods: {
      hanldeHeight() {
        this.height = window.innerHeight - 215
      },
      handleSubmit(val) {
        var model = { id: this.id, ruleParam: JSON.stringify(val) }
        update(model).then(r => {
          if (r.code === 20000) {
            this.$message.success('操作成功')
            this.visibleTags.setting = false
            this.handleSearch()
          } else {
            this.$message.success('操作失败')
          }
        })
      },
      handleSetting(row) {
        this.id = row.id
        var gameRule = JSON.parse(row.ruleParam)
        this.visibleTags.setting = true
        this.$nextTick(() => {
          this.$refs.setting.setData(gameRule)
        })
      },
      dateTimeFormat: function (row, column) {
        var date = row[column.property]
        if (!date) {
          return ''
        }
        return moment(date).format('YYYY-MM-DD HH:mm:ss')
      },
      getRuleNo() {
        getByRuleNo(this.ruleNo, this.$store.getters.userId).then(r => {
          if (r.data) {
            this.gameRule = r.data
          }
        })
      },
      handleSearch() {
        list(this.searchParam).then(r => {
          this.formatList(r.data.list)
          this.modelList = r.data.list
          this.searchParam.total = r.data.total
        })
      },
      handleAdd() {
        this.visibleTags.add = true
        this.$nextTick(() => {
          this.$refs.modelForm.resetFields()
          this.model = { verifyNo: 'LUCK_SIX_SETTING', verifyName: '', ruleNo: '', ruleName: '', remark: '', batchAdd: 2, batchTotal: null, batchQty: null, batchStart: null }
          this.model.id = null
          this.model.ruleId = this.gameRule.id
          this.model.ruleNo = this.gameRule.ruleNo
          this.model.ruleName = this.gameRule.ruleName
          this.model.ruleParam = JSON.stringify(this.gameRule)
        })
      },
      handleSave() {
        if (!this.model.verifyName) {
          this.$message.error('请输入验证名称')
          return
        }
        if (!this.model.ruleName) {
          this.$message.error('请选择验证规则')
          return
        }
        var modelList = []
        if (this.model.batchAdd === 1) {
          if (!this.model.batchTotal) {
            this.$message.error('请设置验证次数')
            return
          }
          if (!this.model.batchQty) {
            this.$message.error('请设置每次验证靴数')
            return
          }
          if (!this.model.batchStart) {
            this.$message.error('请设置从第几靴开始验证')
            return
          }
          var batchTotal = parseFloat(this.model.batchTotal)
          var batchQty = parseFloat(this.model.batchQty)
          var batchStart = parseFloat(this.model.batchStart)
          console.log('this.model.......................1', this.model)
          for (var i = 0; i < batchTotal; i++) {
            var batchStartTemp = batchStart + i * batchQty;
            var model = JSON.parse(JSON.stringify(this.model))
            var ruleParam = JSON.parse(model.ruleParam)
            ruleParam.ruleValue7 = batchQty;
            ruleParam.ruleValue9 = batchStartTemp
            model.ruleParam = JSON.stringify(ruleParam)
            model.verifyName = this.model.verifyName + " " + batchStartTemp + "-" + batchQty
            model.status = 1
            modelList.push(model)
          }
        } else {
          modelList.push(this.model)
        }
        console.log('handleSave.........................', modelList)
        addList(modelList).then(r => {
          console.log(r)
          if (r.code === 20000) {
            this.visibleTags.add = false
            this.$refs.modelForm.resetFields()
            this.handleSearch()
            this.$message.success('操作成功')
          } else {
            this.$message.success('操作失败')
          }
        })
      },
      handleSave2() {
        if (!this.model.verifyName) {
          this.$message.error('请输入验证名称')
          return
        }
        if (!this.model.ruleName) {
          this.$message.error('请选择验证规则')
          return
        }
        add(this.model).then(r => {
          console.log(r)
          if (r.code === 20000) {
            this.visibleTags.add = false
            this.$refs.modelForm.resetFields()
            this.handleSearch()
            this.$message.success('操作成功')
          } else {
            this.$message.success('操作失败')
          }
        })
      },
      handleDelete() {
        this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
          confirmButtonText: this.$t('operate.confirm'),
          cancelButtonText: this.$t('operate.cancel'),
          type: 'warning'
        }).then(() => {
          if (this.ids.length > 0) {
            del(this.ids).then(() => {
              this.handleSearch()
              this.$message({
                message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
                type: 'success'
              })
            })
          }
        })
      },
      handleCopy() {
        if (!this.multipleSelection.length) {
          this.$message.error('请选择一条记录')
          return
        }
        if (this.multipleSelection.length > 1) {
          this.$message.error('请只选一条记录')
          return
        }
        this.visibleTags.add = true
        this.$nextTick(() => {
          this.$refs.modelForm.resetFields()
          this.model = { verifyNo: 'FIVE_BASE_SETTING', verifyName: '', ruleNo: '', ruleName: '', remark: '', batchAdd: 2, batchTotal: null, batchQty: null, batchStart: null }
          this.model.id = null
          this.model.verifyName = this.multipleSelection[0].verifyName
          this.model.verifyNo = this.multipleSelection[0].verifyNo
          this.model.ruleId = this.multipleSelection[0].ruleId
          this.model.ruleNo = this.multipleSelection[0].ruleNo
          this.model.ruleName = this.multipleSelection[0].ruleName
          this.model.ruleParam = this.multipleSelection[0].ruleParam
        })
      },
      handleEdit(row) {
        this.model.id = row.id
        this.model.verifyName = row.verifyName
        this.model.ruleId = row.ruleId
        this.model.ruleNo = row.ruleNo
        this.model.ruleName = row.ruleName
        this.model.remark = row.remark

        /* this.model.ruleId = this.gameRule.id
        this.model.ruleNo = this.gameRule.ruleNo
        this.model.ruleName = this.gameRule.ruleName
        this.model.ruleParam = JSON.stringify(this.gameRule) */
        this.visibleTags.add = true
      },
      handleStartVerify(row) {
        if (row.status === 1) {
          this.$message.error('正在验证中，请耐心等待')
          return
        }
        this.$confirm('开始验证，确定吗？', '询问', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var entity = {
            id: row.id, status: 1,
            bankerAmount: 0, playerAmount: 0, tieAmount: 0, winAmount: 0, maxAmount: 0, minAmount: 0,
            totalAmount: 0, commissionAmount: 0, bankerCount: 0, playerCount: 0, tieCount: 0,
            winCount: 0, loseCount: 0, balance: 0, matchCount: 0,
            profitResult: '', winResult: ''
          }
          update(entity).then(r => {
            console.log(r)
            if (r.code === 20000) {
              this.handleSearch()
              this.$message.success('验证开始')
            } else {
              this.$message.success('操作失败')
            }
          })
        })
      },
      handleStopVerify(row) {

      },
      handleProfitResult(row) {
        this.title = '盈利曲线'
        if (!row.profitResult) {
          this.yData = []
          this.xData = []
        }
        this.yData = row.profitResult.split(',')
        for (var i = 0; i < this.yData.length; i++) {
          this.yData[i] = parseFloat(this.yData[i])
          this.xData[i] = i + 1
        }
        this.visibleTags.profit = true
        this.$nextTick(() => {
          this.$refs.lineChart.initChart()
        })
      },
      handleWinResult(row) {
        this.title = '胜负曲线'
        if (!row.winResult) {
          this.yData = []
          this.xData = []
        }
        this.yData = row.winResult.split(',')
        for (var i = 0; i < this.yData.length; i++) {
          this.yData[i] = parseFloat(this.yData[i])
          this.xData[i] = i + 1
        }
        this.visibleTags.profit = true
        this.$nextTick(() => {
          this.$refs.lineChart.initChart()
        })
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      handleSizeChange(val) {
        this.searchParam.pageSize = val
        this.handleSearch()
      },
      handleCurrentChange(val) {
        this.searchParam.pageNo = val
        this.handleSearch()
      },
      formatList(list) {
        for (var model of list) {
          if (model.status === 0) {
            model.statusName = '待验证'
          } else if (model.status === 1) {
            model.statusName = '开始验证'
          } else if (model.status === 2) {
            model.statusName = '验证中'
          } else if (model.status === 3) {
            model.statusName = '验证完成'
          }
        }
      },
      handleExport(row) {
        listByVerifyId(row.id).then(r => {
          this.exportData(r.data, row.verifyName)
        })
      },
      exportData(dataList, fileName) {
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['本局编号', '本局结果', '下注方向', '下注金额', '输赢金额', '盈利金额']
          const filterVal = ['gameId', 'gameResult', 'chipInResult', 'chipInAmount', 'winAmount', 'balance']
          const list = dataList
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: fileName,
            autoWidth: true
          })
        })
      },
      formatJson(filterVal, jsonData) {
        return jsonData.map(v => filterVal.map(j => {
          if (j === 'billDate' || j === 'transportDate') {
            return this.parseTime(v[j])
          } else {
            return v[j]
          }
        }))
      },
      handleExportStat(row) {
        if (!row.statisticResult) {
          this.$message.error('暂无数据，请确认是否已验证完成')
          return
        }
        var statisticResult = JSON.parse(row.statisticResult)
        var header = []
        var total = 0
        for (var i = 0; i < statisticResult.length; i++) {
          header.push('第' + (i + 1) + '局赢')
          total = total + parseInt(statisticResult[i])
        }
        if (!total) {
          total = 1
        }
        var dataList = []
        var statistic = {}
        var percent = {}
        var filter = []
        for (i = 0; i < statisticResult.length; i++) {
          statistic['stat' + (i + 1)] = statisticResult[i]
          percent['stat' + (i + 1)] = statisticResult[i] ? formatNumber(statisticResult[i] / total * 100) + '%' : ''
          filter.push('stat' + (i + 1))
        }
        dataList.push(statistic)
        dataList.push(percent)
        this.exportDataStatistic(header, filter, dataList, row.verifyName + '统计')
      },
      exportDataStatistic(header, filter, dataList, fileName) {
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = header
          const filterVal = filter
          const list = dataList
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: fileName,
            autoWidth: true
          })
        })
      },
      handleExportWinRoad(row) {
        // row.winRoad = 'X,X,V;X,V,V;X,V,V;X,V,V;X,V,V'
        if (!row.winRoad) {
          this.$message.error('暂无数据，请确认是否已验证完成')
          return
        }
        var header = []
        var dataList = []
        var filter = []

        var winRoads = row.winRoad.split(';')
        var roads = []

        var rows = 0
        for (var i = 0; i < winRoads.length; i++) {
          header.push('第' + (i + 1) + '回')
          filter.push('item' + i)
          var tempRoads = winRoads[i].split(',')
          roads.push(tempRoads)
          if (tempRoads.length > rows) {
            rows = tempRoads.length
          }
        }

        for (var j = 0; j < rows; j++) {
          var item = {}
          for (i = 0; i < winRoads.length; i++) {
            item['item' + i] = roads[i][j]
          }
          dataList.push(item)
        }
        this.exportDataStatistic(header, filter, dataList, row.verifyName + '胜负路(纵向)')
      },
      handleExportWinRoad2(row) {
        // row.winRoad2 = 'X,X,V,V,X,V,V,X,V,V,X,X,X,V,V'
        if (!row.winRoad2) {
          this.$message.error('暂无数据，请确认是否已验证完成')
          return
        }
        var header = []
        var dataList = []
        var filter = []

        var winRoads = []
        var tempWinRoads = row.winRoad2.split(',')
        var tempRoads = []
        var res = ''
        for (var i = 0; i < tempWinRoads.length; i++) {
          if (res !== tempWinRoads[i]) {
            tempRoads = []
            winRoads.push(tempRoads)
            res = tempWinRoads[i]
          }
          tempRoads.push(tempWinRoads[i])
        }
        var rows = 0
        for (i = 0; i < winRoads.length; i++) {
          header.push('第' + (i + 1) + '列')
          filter.push('item' + i)
          if (winRoads[i].length > rows) {
            rows = winRoads[i].length
          }
        }
        for (var j = 0; j < rows; j++) {
          var item = {}
          for (i = 0; i < winRoads.length; i++) {
            item['item' + i] = winRoads[i][j]
          }
          dataList.push(item)
        }
        if (row.winTime) {
          item = {}
          dataList.push(item)
          item = { 'item0': '汇总', 'item1': '赢次数：' + row.winTime.split(',')[0], 'item2': '输次数：' + row.winTime.split(',')[1] }
          dataList.push(item)
        }
        this.exportDataStatistic(header, filter, dataList, row.verifyName + '胜负路(横向)')
      }
    }
  }
</script>

<style>
  .form-item-normal .input {
    width: 100px;
  }
</style>
