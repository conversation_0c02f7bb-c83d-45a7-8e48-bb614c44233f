import request from '@/utils/request'

export function getGameMatch(id) {
  return request({
    url: '/game-match/get',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function listGameMatch(data) {
  return request({
    url: '/game-match/list',
    method: 'post',
    data
  })
}
export function deleteGameMatch(id) {
  return request({
    url: '/game-match/delete-result',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function addGameMatch(data) {
  return request({
    url: '/game-match/info/add',
    method: 'post',
    data
  })
}

export function updateGameMatch(data) {
  return request({
    url: '/game-match/update',
    method: 'post',
    data
  })
}
