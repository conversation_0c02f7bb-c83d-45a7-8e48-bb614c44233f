<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">{{ $t('operate.add') }}</el-button>
          <!-- <el-button size="mini" type="danger" icon="el-icon-delete" round @click="handleDeleteClick">{{ $t('operate.delete') }}</el-button> -->
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <!-- <el-input v-model="param.userName" style="width:144px" placeholder="根据账号查询" clearable />
          <el-button icon="fa fa-search" type="primary" @click="getData" /> -->
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="44" />
      <el-table-column type="index" align="center" width="44" />
      <el-table-column prop="jettonNo" align="center" :label="$t('jetton.chipNo')" width="124" />
      <el-table-column prop="jettonName" align="center" :label="$t('jetton.chipName')" width="124" />
      <el-table-column prop="downLimit" align="center" :label="$t('jetton.betLowerLimit')" width="120">
        <template slot-scope="scope">
          <div> {{ scope.row.downLimit| numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="upperLimit" align="center" :label="$t('jetton.betUpperLimit')" width="120">
        <template slot-scope="scope">
          <div> {{ scope.row.upperLimit| numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="jetton" :label="$t('jetton.chipOption')" show-overflow-tooltip />
      <el-table-column fixed="right" align="center" :label="$t('jetton.operation')" width="94">
        <template slot-scope="scope">
          <el-button plain size="mini" @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('jetton.chipInfo')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="30%">
      <el-form ref="dataForm" :model="object" label-width="120px" :disabled="ifView" :rules="buttonRules">
        <el-form-item :label="$t('jetton.chipNo')" prop="jettonNo">
          <el-input v-model="object.jettonNo" auto-complete="off" clearable />
        </el-form-item>
        <el-form-item :label="$t('jetton.chipName')" prop="jettonName">
          <el-input v-model="object.jettonName" auto-complete="off" clearable />
        </el-form-item>
        <el-form-item :label="$t('jetton.betLowerLimit')" prop="downLimit">
          <el-input v-model.number="object.downLimit" auto-complete="off" clearable />
        </el-form-item>
        <el-form-item :label="$t('jetton.betUpperLimit')" prop="upperLimit">
          <el-input v-model.number="object.upperLimit" auto-complete="off" clearable />
        </el-form-item>
        <el-form-item :label="$t('jetton.chipOption')">
          <!-- <el-tag
            v-for="tag in dynamicTags"
            :key="tag"
            closable
            :disable-transitions="false"
            effect="plain"
            @close="handleClose(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="saveTagInput"
            v-model.number="inputValue"
            class="input-new-tag"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 添加</el-button> -->
          <el-checkbox-group
            v-model="jettonCheckedList"
            :min="3"
          >
            <el-checkbox v-for="jettionOption in jettionOptions" :key="jettionOption" :label="jettionOption">{{ jettionOption }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" :loading="loadingTags.add" @click="createData">{{ $t('operate.add') }}</el-button>
        <el-button v-else type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listJetton, addJetton, updateJetton, deleteJetton } from '@/api/setting/jetton'
import { deepClone } from '@/utils/transferUtil'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'JettonList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      jettonCheckedList: ['100', '1000', '10000'],
      jettionOptions: ['100', '1000', '5000', '10000', '20000', '50000', '100000', '200000', '500000', '1000000', '2000000', '5000000', '10000000'],
      param: {},
      objectList: [],
      multipleSelection: [],
      systemOptions: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {
        buttonNo: '',
        buttonName: ''
      },
      formLabelWidth: '80px',
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      },
      buttonRules: {
        jettonNo: [{ required: true, trigger: 'blur', message: this.$t('jetton.validateMsg.jettonNo') }],
        jettonName: [{ required: true, trigger: 'blur', message: this.$t('jetton.validateMsg.jettonName') }],
        downLimit: [{ required: true, trigger: 'blur', message: this.$t('jetton.validateMsg.downLimit') }],
        upperLimit: [{ required: true, trigger: 'blur', message: this.$t('jetton.validateMsg.upperLimit') }]
      },
      dynamicTags: [],
      inputVisible: false,
      inputValue: ''
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      const inputValue = this.inputValue
      if (inputValue) {
        if (this.dynamicTags.indexOf(inputValue) > -1) {
          this.$message({
            showClose: true,
            message: this.$t('jetton.msg.chipRepeat'),
            type: 'warning'
          })
        } else {
          this.dynamicTags.push(inputValue)
        }
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    getData() {
      const param = {}
      listJetton(param).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
        this.currentPage = r.data.pageNum
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogFormVisible = true
      this.object = {}
      this.jettonCheckedList = ['1', '10', '20']
      this.ifView = false
      this.dialogStatus = 'create'
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.jettonCheckedList.length > 0 && this.jettonCheckedList.length >= 3) {
            this.jettonCheckedList.sort(function(a, b) {
              return a - b
            })
            this.object.jetton = this.jettonCheckedList.join(',')
            this.loadingTags.add = true
            addJetton(this.object).then(() => {
              this.dialogFormVisible = false
              this.getData()
              this.$message({
                message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
                type: 'success'
              })
            })
          } else {
            this.$message({
              showClose: true,
              message: this.$t('jetton.msg.setChip'),
              type: 'error'
            })
          }
        }
      })
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.object = deepClone(val)
      if (!this.object.jetton) {
        this.jettonCheckedList = []
      } else {
        this.jettonCheckedList = this.object.jetton.split(',')
      }
      this.ifView = false
      this.dialogStatus = 'edit'
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.jettonCheckedList.length > 0 && this.jettonCheckedList.length >= 3) {
            this.jettonCheckedList.sort(function(a, b) {
              return a - b
            })
            this.object.jetton = this.jettonCheckedList.join(',')
            this.loadingTags.edit = true
            updateJetton(this.object).then(() => {
              this.dialogFormVisible = false
              this.getData()
              this.$message({
                message: this.$t('operate.save') + ' ' + this.$t('operate.message.success'),
                type: 'success'
              })
            })
          } else {
            this.$message({
              showClose: true,
              message: this.$t('jetton.msg.threeChip'),
              type: 'error'
            })
            return
          }
        }
      })
    },
    removeData() {
      deleteJetton(this.ids).then(() => {
        this.getData()
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    deleteRows() {
      if (this.ids.length > 0) {
        this.removeData()
      } else {
        this.$message({
          message: this.$t('jetton.msg.deleteData'),
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleDeleteClick() {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.deleteRows()
      })
    }
  }
}
</script>
<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
