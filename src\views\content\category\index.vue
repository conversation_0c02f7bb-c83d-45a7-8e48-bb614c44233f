<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px; ">
      <el-col :span="8">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddClick"
        >新增
        </el-button>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <!-- <el-button icon="fa fa-search" type="primary" @click="getData" /> -->
        </el-row>
      </el-col>
    </el-row>
    <el-table
      :data="objectList"
      style="width: 100%;"
      row-key="id"
      border
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column
        prop="id"
        label="ID"
        width="104"
      />
      <el-table-column
        prop="title"
        label="分类名称"
        width="144"
      />
      <el-table-column
        prop="categoryNo"
        label="分类编号"
        width="164"
      />
      <el-table-column
        prop="sortNo"
        label="排序编号"
        sortable
        align="center"
        width="104"
      />
      <el-table-column
        prop="remark"
        label="备注"
      />
      <el-table-column label="操作" align="center" fixed="right" width="148">
        <template slot-scope="scope">
          <el-button-group>
            <el-button size="mini" plain @click="handleEditClick(scope.row)">编辑</el-button>
            <el-button size="mini" plain @click="handleRemoveClick(scope.row)">删除</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end">
      <el-pagination background :current-page="listQuery.pageNo" :page-sizes="[10, 50, 100, 200]" :page-size="listQuery.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog title="文章分类" :visible.sync="dialogVisible" :close-on-click-modal="false" width="500px">
      <el-form ref="menu" :model="object" label-width="80px" class="form-item-normal">
        <el-form-item label="分类名称">
          <el-input v-model="object.title" />
        </el-form-item>
        <el-form-item label="分类编号">
          <el-input v-model="object.categoryNo" />
        </el-form-item>
        <el-form-item label="排序号">
          <el-input-number v-model="object.sortNo" :min="1" label="数字越大优先级越高" />
        </el-form-item>
        <el-form-item label="分类类型">
          <el-radio-group v-model="object.level">
            <el-radio :label="1">一级分类</el-radio>
            <el-radio :label="2">二级分类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="object.level == 2" label="分类位置">
          <el-tree
            ref="roleTreeForm"
            :data="roleTree"
            show-checkbox
            node-key="id"
            :default-checked-keys="[object.parentId]"
            :check-strictly="true"
            :props="defaultProps"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="actionType ==='ADD'" :lading="loadingTags.add" type="primary" @click="addData">确 定</el-button>
        <el-button v-else type="primary" :lading="loadingTags.edit" @click="modifyData">修 改</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { listAllCategory, listOneLevelCategory, addArticleCategory, updateArticleCategory, deleteArticleCategory } from '@/api/content/articleCategory'
import { mapGetters } from 'vuex'
import { deepClone } from '@/utils/transferUtil'
export default {
  name: 'Category',
  filters: {
    statusTagFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },
  props: {
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      loading: false,
      ifShow: true,
      roleTree: [],
      menuButtonOptionTree: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      dialogVisible: false,
      loadingTags: {
        add: false,
        edit: false
      },
      formLabelWidth: '80px',
      // 选项卡对应变量
      activeName: 'first',
      object: {},
      objectList: [],
      actionType: ''
    }
  },
  computed: {
    ...mapGetters([
      'userName',
      'userId',
      'refId'
    ])
  },
  created() {
    this.getData()
  },
  methods: {
    buildRoleTree() {
      this.listQuery.ifPage = false
      listOneLevelCategory(this.listQuery).then(r => {
        this.roleTree = r.data
      })
    },
    getData() {
      const _this = this
      this.listQuery.ifPage = true
      listAllCategory(this.listQuery).then(r => {
        r.data.list.forEach(function(item, index, array) {
          item['edit'] = _this.edit
          item['delete'] = _this.delete
        })
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.listQuery.pageNo = val
      this.getData()
    },
    removeData(id) {
      deleteArticleCategory(id).then(r => {
        this.getData()
        this.$message({
          message: '删除成功',
          type: 'success'
        })
      })
    },
    initRoleParam() {
      if (!this.$refs.roleTreeForm.getCheckedKeys()) {
        this.object.parentId = 0
      } else {
        this.object.parentId = this.$refs.roleTreeForm.getCheckedKeys()[0]
      }
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogVisible = true
      this.object = deepClone(val)
      this.actionType = 'EDIT'
      this.buildRoleTree()
    },
    modifyData() {
      this.initRoleParam(0)
      this.loadingTags.edit = true
      updateArticleCategory(this.object).then(r => {
        this.dialogVisible = false
        this.$message({
          message: '修改成功',
          type: 'success'
        })
        this.getData()
      }).catch(() => {
        this.loadingTags.edit = false
        this.$message({
          message: '修改失败',
          type: 'error'
        })
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogVisible = true
      this.object = { status: 1, sortNo: 10, level: 1 }
      this.actionType = 'ADD'
      this.buildRoleTree()
    },
    addData() {
      this.loadingTags.add = true
      this.initRoleParam()
      addArticleCategory(this.object).then(r => {
        this.dialogVisible = false
        this.$message({
          message: '创建成功',
          type: 'success'
        })
        this.getData()
      }).catch(() => {
        this.loadingTags.add = false
        this.$message({
          message: '创建失败',
          type: 'error'
        })
      })
    },
    handleRemoveClick(val) {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.object = val
        delete this.object.children
        delete this.object.parent
        this.removeData(this.object.id)
      })
    }
  }
}
</script>
