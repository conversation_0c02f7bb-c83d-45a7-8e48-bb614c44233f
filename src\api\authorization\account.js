import request from '@/utils/request'

export function getDefaultToken() {
  return request({
    url: '/account/default-token',
    method: 'get'
  })
}
export function listAccount(data) {
  return request({
    url: '/account/list',
    method: 'post',
    data
  })
}

export function registerAccount(data) {
  return request({
    url: '/account/register',
    method: 'post',
    data
  })
}
export function changePasswordByAdmin(userId, password) {
  return request({
    url: '/user/inner/changePassword',
    method: 'post',
    params: {
      userId: userId,
      password: password
    }
  })
}
export function companyRegister(data) {
  return request({
    url: '/account/company-register',
    method: 'post',
    data
  })
}
export function memberRegister(data) {
  return request({
    url: '/account/member-register',
    method: 'post',
    data
  })
}
export function driverRegister(data) {
  return request({
    url: '/account/driver-register',
    method: 'post',
    data
  })
}
export function checkUserNameExist(userName, userType) {
  return request({
    url: '/account/checkUserNameExist',
    method: 'get',
    params: {
      userName: userName,
      userType: userType
    }
  })
}
export function getAccountInfo(token) {
  return request({
    url: '/account/info',
    method: 'get',
    params: {
      token: token
    }
  })
}
export function setValidateMobile(accountInfoId, validateMobile) {
  return request({
    url: '/account/validate-mobile/set',
    method: 'post',
    params: {
      accountInfoId: accountInfoId,
      validateMobile: validateMobile
    }
  })
}

export function setPayPassword(data) {
  return request({
    url: '/account/pay-password/set',
    method: 'post',
    data
  })
}

export function checkPasswordQuestion(accountInfoId, passQuestion, passAnswer) {
  return request({
    url: '/account/password-question/check',
    method: 'post',
    params: {
      accountInfoId: accountInfoId,
      passQuestion: passQuestion,
      passAnswer: passAnswer
    }
  })
}
export function forgetPayPassword(accountInfoId, payPassword) {
  return request({
    url: '/account/pay-password/forget',
    method: 'post',
    params: {
      accountInfoId: accountInfoId,
      payPassword: payPassword
    }
  })
}
export function checkPayPassword(accountInfoId, payPassword) {
  return request({
    url: '/account/pay-password/check',
    method: 'post',
    params: {
      accountInfoId: accountInfoId,
      payPassword: payPassword
    }
  })
}
export function changePayPassword(accountInfoId, payPassword) {
  return request({
    url: '/account/pay-password/change',
    method: 'post',
    params: {
      accountInfoId: accountInfoId,
      payPassword: payPassword
    }
  })
}
