<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">
            {{ $t('operate.add') }}
          </el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-input v-model="memberParam.userName" style="width:214px;margin-right:10px" :placeholder="$t('member.placeholder.memberAccount')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="chipInStatus" :label="$t('member.bettingStatus')" align="center" width="94">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.chipInStatus" type="success">
            {{ $t('status.open') }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t('status.closed') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="memberType" :label="$t('member.role')" align="center" width="84">
        <template slot-scope="scope">
          <span v-if="scope.row.memberType === 7">
            {{ $t('member.avatar') }}
          </span>
          <span v-if="scope.row.memberType === 8">
            {{ $t('member.pagcor') }}
          </span>
          <span v-if="scope.row.memberType === 9">
            {{ $t('member.cashier') }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="user.userName" align="center" :label="$t('member.memberAccount')" width="124" />
      <el-table-column prop="user.nickName" align="center" :label="$t('member.memberNickname')" width="100" />
      <el-table-column prop="fullName" align="center" :label="$t('member.fullName')" width="84" />
      <el-table-column prop="phoneNo" align="center" :label="$t('member.mobilePhone')" width="124" />
      <el-table-column prop="remainPoint" align="center" :label="$t('member.accountBalance')" width="132">
        <template slot-scope="scope">
          {{ scope.row.remainPoint | numberFilter | moneyFilter }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="maxPoint" align="center" label="充值限额" width="94" />
      <el-table-column prop="maxWinPoint" align="center" label="最大赢额" width="94" /> -->
      <el-table-column prop="isOnline" :label="$t('member.onLine')" align="center" width="72">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isOnline" type="success">
            {{ $t('status.onLine') }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t('status.offLine') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginIp" :label="$t('member.recentlyLoggedInIp')" align="center" width="144" />
      <el-table-column prop="lastLoginDt" :label="$t('member.recentlyLoggedInTime')" align="center" width="144" :formatter="dateTimeFormat" />
      <el-table-column prop="createDt" :label="$t('member.createdTime')" align="center" :formatter="dateTimeFormat" />
      <el-table-column fixed="right" align="center" :label="$t('member.operation')" width="144">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="12">
              <el-dropdown>
                <el-button plain size="mini">
                  {{ $t('operate.moreOperate') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-show="edit">
                    <router-link :to="'/account-center/other-edit/'+scope.row.id">
                      {{ $t('operate.edit') }}
                    </router-link>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('chargeWithdrawForm.tittle')" :visible.sync="dialogFormVisible" class="form-item-normal" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="32%">
      <el-form ref="dataForm" :model="amountData" label-width="94px">
        <el-form-item :label="$t('chargeWithdrawForm.operation')">
          <el-radio-group v-model="amountData.pointType">
            <el-radio :label="1" border>
              {{ $t('chargeWithdrawForm.recharge') }}
            </el-radio>
            <el-radio :label="2" border>
              {{ $t('chargeWithdrawForm.withdraw') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="amountData.pointType === 1" :label="$t('chargeWithdrawForm.rechargeType')">
          <el-radio-group v-model="amountData.pointMethod" class="dialog-footer">
            <el-radio :label="1" border>
              {{ $t('chargeWithdrawForm.cash') }}
            </el-radio>
            <el-radio :label="2" border>
              {{ $t('chargeWithdrawForm.sign') }}</el-radio>
            <el-radio :label="3" border>
              {{ $t('chargeWithdrawForm.payout') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('chargeWithdrawForm.amount')">
          <el-input-number v-model="amountData.point" :placeholder="$t('chargeWithdrawForm.placeholder.amount')" :min="0" :controls="false" :precision="2" clearable>
            {{ $t('chargeWithdrawForm.priceUnit') }}
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">  {{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="pointLoading" @click="submitPontChange">{{ $t('operate.confirm') }}</el-button>
      </div>
    </el-dialog>
    <change-password :visible="dialogPassVisible" :user-id="currentUserId" @cancelCall="passCancel" @doneCall="passDone" />
  </div>
</template>
<script>
import { listMemberInfo, doChipStatusChange } from '@/api/account/member'
import { addPointHistory } from '@/api/account/pointHistory'
import moment from 'moment'
import { mapGetters } from 'vuex'
import ChangePass from '../components/ChangePass.vue'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'MemberList',
  components: {
    'change-password': ChangePass
  },
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    password: {
      type: Boolean,
      default: false
    },
    chipOperate: {
      type: Boolean,
      default: false
    },
    rechargeWithdraw: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        memberType: 0
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      amountData: {
        pointType: 1,
        pointMethod: 1
      },
      memberStatusParam: {},
      pointLoading: false
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ])
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  activated() {
    this.listTableData()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      this.memberParam.memberTypeIncludeStr = '7, 9, 8'
      listMemberInfo(this.memberParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleAddClick() {
      this.$router.push('/account-center/other-create')
    },
    handleChipClick(val) {
      var message = ''
      this.memberStatusParam.memberId = val.id
      this.memberStatusParam.chipInStatus = !val.chipInStatus ? 1 : 0
      this.memberStatusParam.isDisabledAlone = 1
      this.memberStatusParam.memberType = 3
      if (!val.chipInStatus) {
        message = this.$t('operate.betEnabled')
      } else {
        message = this.$t('operate.betDisabled')
      }
      this.$confirm(message + '!' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        doChipStatusChange(this.memberStatusParam).then(r => {
          this.$message({
            message: message + '.' + this.$t('operate.message.success') + '!',
            type: 'success',
            center: true
          })
          this.listTableData()
        })
      }).catch(() => {
        this.$message({
          message: this.$t('operate.message.cancel') + '!',
          type: 'info',
          center: true
        })
      })
    },
    handlePontChangeClick(data) {
      this.amountData.memberId = data.id
      this.amountData.parentMemberId = data.parentId
      this.amountData.handleBy = this.member.id
      this.dialogFormVisible = true
      this.amountData.point = undefined
    },
    submitPontChange() {
      this.pointLoading = true
      if (!this.amountData.point) {
        this.$message({
          showClose: true,
          message: this.$t('chargeWithdrawForm.validateMsg.amount'),
          type: 'error'
        })
        this.pointLoading = false
        return
      }
      addPointHistory(this.amountData).then(r => {
        if (r.data) {
          this.$message({
            showClose: true,
            message: this.$t('operate.message.success') + '!',
            type: 'success'
          })
          this.listTableData()
          this.dialogFormVisible = false
        } else {
          this.$message({
            showClose: true,
            message: this.$t('operate.message.fail') + '!',
            type: 'error'
          })
        }
        this.pointLoading = false
      })
    },
    handlePassSetClick(data) {
      this.currentUserId = data.user.id
      this.dialogPassVisible = true
    },
    passCancel() {
      this.dialogPassVisible = false
    },
    passDone(data) {
      this.dialogPassVisible = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
