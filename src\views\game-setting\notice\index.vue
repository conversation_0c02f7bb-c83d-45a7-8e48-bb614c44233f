<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button size="mini" type="primary" icon="el-icon-plus" round @click="handleAddClick">{{ $t('operate.add') }}</el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" round @click="handleDeleteClick">{{ $t('operate.delete') }}</el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <!-- <el-input v-model="agentParam.userName" style="width:144px" placeholder="根据账号查询" clearable /> -->
          <el-button icon="fa fa-search" type="primary" @click="getData" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="status" align="center" :label="$t('noticeInfo.noticeStatus')" width="104">
        <template slot-scope="scope">
          <div> {{ scope.row.status ? $t('noticeStatus.valid') : $t('noticeStatus.invalid') }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="messageType" align="center" :label="$t('noticeInfo.noticeType')" width="104">
        <template slot-scope="scope">
          <div> {{ scope.row.messageType === 1 ? $t('noticeType.webMessage'):$t('noticeType.frontMessage') }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="title" align="center" :label="$t('noticeInfo.noticeTittle')" width="144" />
      <el-table-column prop="message" :label="$t('noticeInfo.noticeContent')" show-overflow-tooltip />
      <el-table-column prop="messageEn" :label="$t('noticeInfo.noticeContentEn')" show-overflow-tooltip />
      <el-table-column fixed="right" align="center" :label="$t('noticeInfo.operation')" width="94">
        <template slot-scope="scope">
          <el-button plain size="mini" @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('noticeInfo.notice')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="40%">
      <el-form ref="dataForm" :model="object" label-width="100px" :disabled="ifView">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('noticeInfo.noticeType')">
              <el-select v-model="object.messageType" clearable :placeholder="$t('noticeInfo.placeholder.messageType')">
                <el-option
                  :label="$t('noticeType.webMessage')"
                  :value="1"
                />
                <el-option
                  :label="$t('noticeType.frontMessage')"
                  :value="2"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('noticeInfo.noticeStatus')">
              <el-switch v-model="object.status" :active-text="$t('noticeStatus.valid')" :inactive-text="$t('noticeStatus.invalid')" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('noticeInfo.noticeTittle')">
          <el-input v-model="object.title" auto-complete="off" />
        </el-form-item>
        <el-form-item :label="$t('noticeInfo.noticeContent')">
          <el-input v-model="object.message" type="textarea" />
        </el-form-item>
        <el-form-item :label="$t('noticeInfo.noticeContentEn')">
          <el-input v-model="object.messageEn" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" :loading="loadingTags.add" @click="createData">{{ $t('operate.save') }}</el-button>
        <el-button v-else type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.edit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listMessage, addMessage, deleteMessage, updateMessage } from '@/api/setting/message'
import { deepClone } from '@/utils/transferUtil'
export default {
  name: 'NoticeList',
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      agentParam: {},
      objectList: [],
      multipleSelection: [],
      systemOptions: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {
        buttonNo: '',
        buttonName: ''
      },
      formLabelWidth: '90px',
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      }
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      const param = {}
      listMessage(param).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
        this.currentPage = r.data.pageNum
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogFormVisible = true
      this.object = { status: 1, sortNo: 10 }
      this.ifView = false
      this.dialogStatus = 'create'
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.add = true
          addMessage(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.save') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.object = deepClone(val)
      this.ifView = false
      this.dialogStatus = 'edit'
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.edit = true
          updateMessage(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    removeData() {
      deleteMessage(this.ids).then(() => {
        this.getData()
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    deleteRows() {
      if (this.ids.length > 0) {
        this.removeData()
      } else {
        this.$message({
          message: this.$t('jetton.msg.deleteData'),
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleDeleteClick() {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.deleteRows()
      })
    }
  }
}
</script>
