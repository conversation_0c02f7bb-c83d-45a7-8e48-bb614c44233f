<template>
  <div class="app-container mobile2">
    <mobile-header title="NXM下注历史" @back="handleBack" />
    <el-row class="pl-5 pr-5">
      <el-col>
        <el-row class="mt-5 mb-5">
          <el-form ref="postForm" label-width="80px" :model="memberParam" class="mini-form">
            <el-form-item label="开始时间">
              <el-date-picker size="mini" clearable v-model="memberParam.dateFrom" type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss" class="w-100p" />
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker size="mini" clearable v-model="memberParam.dateTo" type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss" class="w-100p" />
            </el-form-item>
            <el-form-item label="">
              <el-button class="w-150" icon="fa fa-search" type="primary" @click="handFilter">查询</el-button>
            </el-form-item>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <div>
      <div class="data-info" v-for="(item, index) in objectList">
        <div class="date">{{ dateFormat(item.createDt) }} <span class="time">{{ timeFormat(item.createDt) }} - {{
          timeFormat(item.modifiedDt) }} </span>
        </div>
        <div class="hist">输赢：{{ item.winResult }}</div>
        <div class="win">金额：{{ item.betResult }}</div>
        <div class="profit">利润：{{ item.profit }}</div>
      </div>
    </div>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
import { listGameBet } from '@/api/game/gameBet'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'BetHistory',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        dateFrom: null,
        dateTo: null,
        memberId: 0
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      sums: []
    }
  },
  computed: {
    ...mapGetters(['member', 'userType', 'device', 'userName']),
  },
  watch: {
    show() {
      if (this.show) {
        this.listTableData()
      }
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    formatMoney,
    formatNumber,
    handleBack() {
      // this.$router.push({ path: '/game-bet/player2' })
      // history.back()
      // this.$router.back()
      // console.log('history.go......................1')
      this.$emit('close')
    },
    listTableData() {
      this.memberParam.memberId = this.member.id
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      console.log('listTableData......................1 ', this.memberParam, this.member, this.userName, this.userType)
      listGameBet(this.memberParam).then(r => {
        // this.sumTotal(r.data.list)
        this.objectList = r.data.list
        this.total = r.data.total
        console.log('listGameTransHistInfo.....................', r.data.list)
      })
    },

    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat(date) {
      return moment(date).format('MM-DD HH:mm:ss')
    },
    dateFormat(date) {
      return moment(date).format('MM-DD')
    },
    timeFormat(date) {
      return moment(date).format('HH:mm:ss')
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        if (this.sums.length) {
          sums[index] = this.sums[index]
        }
      })
      return sums
    },
    sumTotal(objectList) {
      this.sums = ['', '', '', '', '', '', 0, '', '', '', 0, 0, 0, '', 0, '']
      this.sums[2] = this.$t('game.totalText')
      for (var i = 0; i < objectList.length; i++) {
        // 6-下注金额 10-输赢 11-余额 12-码量  14-码佣
        this.sums[10] = this.sums[10] + objectList[i].winAmount
        this.sums[11] = this.sums[11] + objectList[i].balance
        this.sums[12] = this.sums[12] + objectList[i].washAmout
        this.sums[14] = this.sums[14] + (objectList[i].washAmout * objectList[i].washRate / 100)
        this.sums[6] = this.sums[6] + objectList[i].bankerAmout +
          objectList[i].bankerPairAmout +
          objectList[i].bankerSuperAmout +
          objectList[i].playerAmout +
          objectList[i].playerPairAmout +
          objectList[i].tieAmout
      }
      for (i = 0; i < this.sums.length; i++) {
        if (i > 2 && this.sums[i]) {
          this.sums[i] = formatMoney(formatNumber(this.sums[i]))
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.data-info {
  line-height: 25px;
  border-top: 1px solid #f5f7fa;
  padding: 5px 10px;
  word-break: break-all;
}

.data-info .date {
  color: #666666;
  font-size: 10px;
}

.data-info .time {
  color: #000000;
}

.data-info .title {
  color: #666666;
}

/*开牌 */
.container .mipai-box {
  top: 0%;
  left: 0%;
  width: 175px;
  height: 125px;
  position: absolute;
  flex-direction: column;
  border-radius: .5rem;
  z-index: 999;
  padding-top: 1%;
  background: #131313;
  opacity: 0.9;
  border-radius: 0.5rem;
}

.container .mipai-box2 {
  width: 250px;
  height: 250px;
}

.container .mipai-box.active {
  display: block;
  animation: bounce02 1.5s;
}

.container .mipai-box.active-a {
  display: block !important;
  animation: bounce03 1.5s;
}

.container .mipai-box.inactive {
  display: none;
}

.container .mipai-box .xian-box,
.container .mipai-box .zhuang-box {
  height: 60px;
  position: relative;
  display: flex;
  border-bottom: 1px solid #222222;
}

.container .mipai-box .total-box {
  font-size: 16px;
  position: absolute;
  left: 5px;
  top: 30px;
  padding: 0 5px 0 5px;
  text-align: center;
  border-radius: 50%;
}

.container .mipai-box .title-box {
  position: absolute;
  font-size: 16px;
  top: 5px;
  left: 5px;
  text-align: center;
}

.container .mipai-box .zhuang-box .total-box,
.container .mipai-box .long-box .total-box {
  color: #ffffff;
  background-color: #c1182e;
}

.container .mipai-box .xian-box .total-box,
.container .mipai-box .hu-box .total-box {
  color: #ffffff;
  background-color: #2b4be9;
}

.container .mipai-box .zhuang-box .title-box,
.container .mipai-box .long-box .title-box {
  color: #c1182e;
}

.container .mipai-box .xian-box .title-box,
.container .mipai-box .hu-box .title-box,
.container .mipai-box .feng-box .title-box {
  color: #2b4be9;
}

.container .mipai-box .smallcard {
  position: absolute;
}

.container .mipai-box .smallcard.on {
  display: block;
}

.container .mipai-box .smallcard.card1 {
  top: 10px;
  height: 40px;
  left: 40px;
  width: 30px;
}

.container .mipai-box .smallcard.card2 {
  top: 10px;
  height: 40px;
  left: 80px;
  width: 30px;
}

.container .mipai-box .smallcard.card3 {
  top: 20px;
  height: 30px;
  left: 120px;
  width: 40px;
}

.container .mipai-box2 .smallcard.card3 {
  top: 10px;
  height: 40px;
  left: 120px;
  width: 30px;
}

.container .mipai-box .smallcard.card4 {
  top: 10px;
  height: 40px;
  left: 160px;
  width: 30px;
}

.container .mipai-box .smallcard.card5 {
  top: 10px;
  height: 40px;
  left: 200px;
  width: 30px;
}

.mobile2.app-container {
  margin: 0px;
  margin-top: 20px;
  padding-top: 0px;
  padding-left: 0;
  padding-right: 0;
  background-color: white;
  padding: 0;
  margin: 0;
  border: none;
}
</style>
