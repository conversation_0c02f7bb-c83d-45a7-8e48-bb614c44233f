import { login, getInfo } from '@/api/common/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { genPassword } from '@/utils/transferUtil'
const user = {
  state: {
    token: getToken(),
    avatar: '',
    refId: 0,
    userId: 0,
    nickName: '',
    userName: '',
    userType: 0,
    member: {},
    memberWashList: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_USER_ID: (state, userId) => {
      state.userId = userId
    },
    SET_USER_NAME: (state, userName) => {
      state.userName = userName
    },
    SET_USER_TYPE: (state, userType) => {
      state.userType = userType
    },
    SET_NICK_NAME: (state, nickName) => {
      state.nickName = nickName
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_REF_ID: (state, refId) => {
      state.refId = refId
    },
    SET_MEMBER: (state, member) => {
      state.member = member
    },
    SET_MEMBER_WASH: (state, memberWashList) => {
      state.memberWashList = memberWashList
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const loginParam = {}
      loginParam.username = username
      loginParam.password = genPassword(userInfo.password)
      loginParam.userType = userInfo.userType
      loginParam.platform = 4
      loginParam.systemType = 1
      loginParam.loginIp = userInfo.loginIp
      loginParam.loginAddr = userInfo.loginAddr
      return new Promise((resolve, reject) => {
        login(loginParam).then(response => {
          const data = response.data
          setToken(data.token)
          commit('SET_TOKEN', data.token)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token).then(response => {
          const data = response.data
          commit('SET_USER_ID', data.id)
          commit('SET_USER_NAME', data.userName)
          commit('SET_NICK_NAME', data.nickName)
          commit('SET_AVATAR', data.avatar)
          commit('SET_REF_ID', data.refId)
          commit('SET_MEMBER', data.member)
          commit('SET_USER_TYPE', data.userType)
          commit('SET_MEMBER_WASH', data.memberWashList)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        commit('SET_TOKEN', '')
        commit('SET_USER_ID', '')
        removeToken()
        resolve()
        /* logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })*/
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
