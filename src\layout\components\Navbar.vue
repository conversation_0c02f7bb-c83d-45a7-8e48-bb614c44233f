<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" />

    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <search id="header-search" class="right-menu-item hover-effect" />

        <error-log class="errLog-container right-menu-item hover-effect" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="Global Size" effect="dark" placement="bottom">
          <size-select v-show="false" id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

      </template>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img v-if="!avatar" class="user-avatar" src="../../assets/images/avatar-default.png">
          <img v-else class="user-avatar" :src="avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <div class="user-info">
            <el-dropdown-item>
              <div class="user-name">{{ userInfo }}</div>
            </el-dropdown-item>
            <el-dropdown-item divided>
              <div class="account-balance">{{ accountBalance }}</div>
              <div class="account-available">{{ accountAvailable }}</div>
            </el-dropdown-item>
          </div>
          <router-link to="/dashboard/index">
            <el-dropdown-item divided>
              {{ $t('header.backHome') }}
            </el-dropdown-item>
          </router-link>
          <router-link to="/user-center/change-pwd">
            <el-dropdown-item>
              {{ $t('header.passChange') }}
            </el-dropdown-item>
          </router-link>
          <div @click="logout">
            <el-dropdown-item divided>
              <span>{{ $t('header.logout') }}</span>
            </el-dropdown-item>
          </div>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import Breadcrumb from '@/components/Breadcrumb'
  import Hamburger from '@/components/Hamburger'
  import ErrorLog from '@/components/ErrorLog'
  import Screenfull from '@/components/Screenfull'
  import SizeSelect from '@/components/SizeSelect'
  import Search from '@/components/HeaderSearch'
  import io from 'socket.io-client'
  import { getToken } from '@/utils/auth' // 验权
  import { socketUrl } from '@/data/config'
  export default {
    components: {
      Breadcrumb,
      Hamburger,
      ErrorLog,
      Screenfull,
      SizeSelect,
      Search
    },
    data() {
      return {
        accountBalance: '',
        accountAvailable: '',
        userInfo: '',
        dialogShow: false,
        keywords: '',
        socket: null
      }
    },
    computed: {
      ...mapGetters([
        'sidebar',
        'avatar',
        'device'
      ]),
      oldToken() {
        return this.$store.state.user.token
      }
    },
    watch: {
      oldToken(data) {
        if (!data) {
          this.closeSocket()
        }
      }
    },
    created() {
      this.getUserInfo()
    },
    mounted() {
      this.connectSocket()
    },
    methods: {
      connectSocket() {
        const _this = this
        const opts = {
          query: 'loginUser=' + getToken()
        }
        this.socket = io.connect(socketUrl, opts)
        this.socket.on('connect', function () {
          console.log('连接成功')
        })
        this.socket.on('push_event', function (data) {
          if (Number(data.type) === 1) {
            switch (data.topic) {
              case 'LOGOUT':
                if (getToken() === data.content) {
                  _this.$alert('您的账号在别去登录，请重新登录', '下线提示', {
                    confirmButtonText: '确定',
                    callback: action => {
                      location.reload()
                    }
                  })
                  _this.$store.dispatch('LogOut').then(() => {
                  })
                }
                break
              case 'OFFLINE':
                _this.$alert('您的账号已经被禁用', '下线提示', {
                  confirmButtonText: '确定',
                  callback: action => {
                    location.reload()
                  }
                })
                _this.$store.dispatch('LogOut').then(() => {
                })
                break
            }
          } else if (Number(data.type) === 3) {
            console.log(data.content)
            _this.$store.dispatch('app/setSocketInfo', data.content)
          }
          console.log(data)
        })
        this.socket.on('disconnect', function () {
          console.log('已经下线')
        })
      },
      toggleSideBar() {
        this.$store.dispatch('app/toggleSideBar')
      },
      logout() {
        this.$store.dispatch('LogOut').then(() => {
          location.reload()
        })
      },
      getUserInfo() {
        this.userInfo = this.$store.getters.nickName
        if (!this.userInfo) {
          this.userInfo = this.$store.getters.userName
        }
        if (this.$store.getters.stationName && this.$store.getters.stationNo) {
          this.userInfo = '' + this.$store.getters.stationName + '[' + this.$store.getters.stationNo + ']' + '：' + this.userInfo
        }
      },
      sendMessage() {
        var jsonObject = { loginUser: 'wk', content: 'xx', topic: 'chat', type: 1, toUserId: 0 }
        this.socket.emit('request_event', jsonObject)
      },
      closeSocket() {
        console.log('连接已断开')
        this.socket.close()
      },
      messageShow(title, message) {
        this.$notify({
          title: title,
          type: 'info',
          center: true,
          message: message,
          customClass: 'message',
          position: 'bottom-right',
          dangerouslyUseHTMLString: true,
          duration: 0
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: #171717;
    box-shadow: 0 1px 4px rgba(0, 21, 41, .08);

    .hamburger-container {
      line-height: 46px;
      height: 100%;
      float: left;
      cursor: pointer;
      transition: background .3s;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }

    .breadcrumb-container {
      float: left;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .right-menu {
      float: right;
      height: 100%;
      line-height: 50px;

      &:focus {
        outline: none;
      }

      .right-menu-item {
        display: inline-block;
        padding: 0 8px;
        height: 100%;
        font-size: 18px;
        color: white;
        vertical-align: text-bottom;
        opacity: 0.5;

        &.hover-effect {
          cursor: pointer;
          transition: background .3s;

          &:hover {
            background: rgba(0, 0, 0, .025);
            opacity: 1;
          }
        }
      }

      .avatar-container {
        margin-right: 30px;

        .avatar-wrapper {
          margin-top: 5px;
          position: relative;

          .user-avatar {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 50%;
          }

          .el-icon-caret-bottom {
            cursor: pointer;
            position: absolute;
            right: -20px;
            top: 25px;
            font-size: 12px;
          }
        }
      }
    }

    .order-search {
      float: left;
      margin-right: 80px;
      text-align: center;

      .el-button--primary {
        color: #999999;
        background-color: #252526;
        border-color: #252526;
      }

      .el-button--primary:hover {
        color: #ffffff;
      }
    }
  }

  .user-info {
    text-align: left;
  }

  .user-name {
    font-size: 14px !important;
    font-weight: bold;
    line-height: 30px;
  }

  .account-balance {
    font-size: 12px !important;
    font-weight: bold;
    line-height: 30px;
  }

  .account-available {
    font-size: 12px !important;
    font-weight: bold;
    line-height: 30px;
  }

  .el-dropdown-menu__item:hover {
    background-color: white;
    color: #171717;
  }
</style>
