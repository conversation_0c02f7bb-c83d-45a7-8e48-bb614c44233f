import request from '@/utils/request'
export function listIpWhiteList(data) {
  return request({
    url: '/ip-white-list/list',
    method: 'post',
    data
  })
}
export function deleteIpWhiteList(ids) {
  return request({
    url: '/ip-white-list/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
export function addIpWhiteList(data) {
  return request({
    url: '/ip-white-list/add',
    method: 'post',
    data
  })
}
