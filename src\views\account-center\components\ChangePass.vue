<template>
  <el-dialog
    :title="$t('member.passForm.tittle')"
    :visible.sync="dialogVisible"
    :show-close="false"
    :close-on-click-modal="false"
    width="500px"
  >
    <el-form ref="passwordForm" :model="passwordData" :rules="passwordRules" label-width="120px" class="form-item-normal">
      <el-form-item :label="$t('member.passForm.password')" prop="password">
        <el-input v-model="passwordData.password" type="password" autocomplete="off" />
      </el-form-item>
      <el-form-item :label="$t('member.passForm.passwordConfirm')" prop="repeatPassword">
        <el-input v-model="passwordData.repeatPassword" type="password" autocomplete="off" />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="cancelCall">
        {{ $t('operate.cancel') }}
      </el-button>
      <el-button type="primary" :loading="isLoading" @click="submitData">
        {{ $t('operate.confirm') }}
      </el-button>
    </span>
  </el-dialog>
</template>
<script>
import { changePassword } from '@/api/common/user'
import { genPassword } from '@/utils/transferUtil'
import { isValidPassword } from '@/utils/validate'
export default {
  name: 'ChangePass',
  props: {
    visible: {
      type: Boolean,
      default: false,
      required: false
    },
    userId: {
      type: Number,
      default: 0,
      required: false
    }
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.password')))
      } else if (!isValidPassword(value)) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordFormate')))
      } else {
        callback()
      }
    }
    const validateRepeatPass = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordConfirm')))
      } else if (value !== this.passwordData.password) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordDifferent')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      isLoading: false,
      passwordData: {},
      passwordRules: {
        password: [
          { required: true, trigger: 'blur', validator: validatePass }
        ],
        repeatPassword: [
          { required: true, trigger: 'blur', validator: validateRepeatPass }
        ]
      }
    }
  },
  watch: {
    visible() {
      this.dialogVisible = this.visible
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('cancelCall')
      }
    }
  },
  methods: {
    cancelCall() {
      this.bankCardVisible = false
      this.$emit('cancelCall')
    },
    doneCall() {
      this.bankCardVisible = false
      this.$emit('doneCall')
    },
    submitData() {
      this.isLoading = true
      this.$refs.passwordForm.validate(valid => {
        if (valid) {
          this.isLoading = true
          changePassword(this.userId, genPassword(this.passwordData.password)).then(r => {
            this.$refs.passwordForm.resetFields()
            this.isLoading = false
            this.doneCall()
          })
        } else {
          this.isLoading = false
        }
      })
    }
  }
}
</script>

<style  rel="stylesheet/scss" lang="scss" scoped>
.captcha {
  width: 250px;
  float: left;
  margin-right: 5px;
}
</style>
