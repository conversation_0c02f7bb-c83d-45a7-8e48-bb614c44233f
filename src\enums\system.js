export const UserTypeEnum = [{
  type: 1,
  value: 'userTypeEnum.superAdmin'
},
{
  type: 2,
  value: 'userTypeEnum.agent'
},
{
  type: 3,
  value: 'userTypeEnum.member'
},
{
  type: 4,
  value: 'userTypeEnum.subAccount'
},
{
  type: 5,
  value: 'userTypeEnum.dealer'
}]
export function getUserTypeEnum(type) {
  for (const val of UserTypeEnum) {
    if (val.type === type) {
      return val.value
    }
  }
}
export const PointTypeEnum = [{
  type: 1,
  value: 'chargeWithdrawForm.recharge'
},
{
  type: 2,
  value: 'chargeWithdrawForm.withdraw'
},
{
  type: 3,
  value: 'operate.settlement'
}]
export function getPointTypeEnum(type) {
  for (const val of PointTypeEnum) {
    if (val.type === type) {
      return val.value
    }
  }
}
export const OperateTypeEnum = [{
  type: 1,
  value: 'operateTypeEnum.login'
},
{
  type: 2,
  value: 'operateTypeEnum.accountOperate'
},
{
  type: 3,
  value: 'operateTypeEnum.betOperate'
},
{
  type: 4,
  value: 'operateTypeEnum.recharge'
},
{
  type: 5,
  value: 'operateTypeEnum.withdraw'
},
{
  type: 6,
  value: 'operateTypeEnum.passwordChange'
},
{
  type: 7,
  value: 'operateTypeEnum.memberEdit'
},
{
  type: 8,
  value: 'operateTypeEnum.agentEdit'
},
{
  type: 9,
  value: 'operateTypeEnum.commissionSettle'
}]
export function getOperateTypeEnum(type) {
  for (const val of OperateTypeEnum) {
    if (val.type === type) {
      return val.value
    }
  }
}
