export const BaccaratResult1Enum = [{
  type: 1, label: 'game.banker'
}, {
  type: 2, label: 'game.player'
},
{
  type: 3, label: 'game.tie'
}
]
export const BaccaratResult2Enum = [{
  type: 1, label: 'game.bankerPair'
}, {
  type: 2, label: 'game.playerPair'
}, {
  type: 3, label: 'game.allPair'
},
{
  type: 4, label: ''
}
]
export function getBaccaratResult1Enum(type) {
  for (const val of BaccaratResult1Enum) {
    if (val.type === Number(type)) {
      return val
    }
  }
}
export function getBaccaratResult2Enum(type) {
  for (const val of BaccaratResult2Enum) {
    if (val.type === Number(type)) {
      return val
    }
  }
}
export const DragonAndTiger1Enum = [{
  type: 2, label: 'game.dragon'
}, {
  type: 1, label: 'game.tiger'
},
{
  type: 3, label: 'game.tie'
}
]
export const DragonAndTiger2Enum = [{
  type: 1, label: 'game.pair'
},
{
  type: 4, label: ''
}
]
export function getDragonAndTiger1Enum(type) {
  for (const val of DragonAndTiger1Enum) {
    if (val.type === Number(type)) {
      return val
    }
  }
  return {}
}
export function getDragonAndTiger2Enum(type) {
  for (const val of DragonAndTiger2Enum) {
    if (val.type === Number(type)) {
      return val
    }
  }
  return {}
}

