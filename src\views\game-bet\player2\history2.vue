<template>
  <div class="app-container dealer" :class="device === 'mobile' ? 'mobile' : ''">
    <div v-if="device === 'mobile'">
      <mobile-header title="NXM下注记录" @back="handleBack" />
      <el-row v-if="device === 'mobile'" class="pl-5 pr-5">
        <el-col>
          <el-row class="mt-5 mb-5">
            <el-form ref="postForm" label-width="80px" :model="memberParam" class="mini-form">
              <el-form-item label="开始时间">
                <el-date-picker size="mini" clearable v-model="memberParam.dateFrom" type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss" class="w-100p" />
              </el-form-item>
              <el-form-item label="结束时间">
                <el-date-picker size="mini" clearable v-model="memberParam.dateTo" type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss" class="w-100p" />
              </el-form-item>
              <el-form-item label="账号">
                <el-input size="mini" v-model="memberParam.userName" clearable class="w-100p" />
              </el-form-item>
              <el-form-item label="">
                <el-button class="w-150" icon="fa fa-search" type="primary" @click="handFilter">查询</el-button>
              </el-form-item>
            </el-form>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div v-else>
      <el-row class="pl-5 pr-5 pb-5" type="flex" justify="end">
        <el-date-picker size="mini" clearable v-model="memberParam.dateFrom" type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss" class="w-150" placeholder="开始时间" />
        <el-date-picker size="mini" clearable v-model="memberParam.dateTo" type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss" class="w-150" placeholder="结束时间" />
        <el-input size="mini" v-model="memberParam.userName" clearable class="w-150" placeholder="账号" />
        <el-button size="mini" icon="fa fa-search" type="primary" @click="handFilter" class="ml-5">查询</el-button>
      </el-row>
    </div>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border
      :max-height="screenHeight" highlight-current-row show-summary :summary-method="getSummaries"
      @selection-change="handleSelectionChange">
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="createDt" align="center" label="日期" width="100" :formatter="dateFormat" />
      <el-table-column prop="user.userName" align="center" label="账户" width="120" show-overflow-tooltip />
      <el-table-column prop="startAmount" align="center" label="基码金额" width="80" show-overflow-tooltip />
      <el-table-column prop="profit" align="center" label="利润" width="80" show-overflow-tooltip />
      <el-table-column prop="createDt" align="center" label="开始时间" width="80" :formatter="timeFormat"
        show-overflow-tooltip />
      <el-table-column prop="modifiedDt" align="center" label="结束时间" width="80" :formatter="timeFormat2"
        show-overflow-tooltip />
      <el-table-column prop="totalMinute" align="center" label="小计分钟" width="80" show-overflow-tooltip />
      <el-table-column prop="winCount" align="center" label="赢次数" width="80" show-overflow-tooltip />
      <el-table-column prop="loseCount" align="center" label="输次数" width="80" show-overflow-tooltip />
      <el-table-column prop="totalCount" align="center" label="投注总次数" width="100" show-overflow-tooltip />
      <el-table-column prop="winRate" align="center" label="命中率" width="80" show-overflow-tooltip />
      <el-table-column prop="avgSecond" align="center" label="平均每口(秒)" width="100" show-overflow-tooltip />
      <el-table-column prop="riskLevel" align="center" label="风险" width="60" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.riskLevel === '1'">稳</span>
          <span v-if="scope.row.riskLevel === '2'">中</span>
          <span v-if="scope.row.riskLevel === '3'">进</span>
        </template>
      </el-table-column>
      <el-table-column prop="nextType" align="center" label="庄闲" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="scope.row.nextType === 1">自主庄闲</span>
          <span v-if="scope.row.nextType === 2">天眼</span>
          <span v-if="scope.row.nextType === 3">点数</span>
        </template>
      </el-table-column>
      <el-table-column prop="winResult" align="center" label="输赢" width="150" show-overflow-tooltip />
      <el-table-column prop="betResult" align="center" label="下注" width="150" show-overflow-tooltip />
    </el-table>
    <el-row type="flex" justify="start" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[100, 200, 500, 1000, 5000]"
        :page-size="pageSize" layout="total, sizes, prev, pager, next" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
import { listGameBet } from '@/api/game/gameBet'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber, formatNumeric } from '@/utils/formatter'
export default {
  name: 'BetHistory2',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    },
    hide: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        dateFrom: null,
        dateTo: null,
        memberId: null
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 500,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      screenHeight: 550,
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      sums: []
    }
  },
  computed: {
    ...mapGetters(['member', 'userType', 'device', 'userName']),
    gameTypeEnums() {
      return GameTypeEnum
    },
    hideEnums() {
      return HideEnum
    }
  },
  watch: {
    multipleSelection: function () {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    // this.listTableData()
    this.screenHeight = document.body.clientHeight - 210
  },
  methods: {
    formatMoney,
    formatNumber,
    handleBack() {
      this.$router.push({ path: '/dashboard/index' })
    },
    listTableData() {
      // this.memberParam.userName = this.userName
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      if (this.member.id !== 1 && this.member.id !== 2 && !(this.member.parentId === 2 && this.userType === 4)) {
        this.memberParam.memberId = this.member.id
      }
      console.log('listTableData......................1 ', this.memberParam, this.member, this.userName, this.userType)
      listGameBet(this.memberParam).then(r => {
        var list = r.data.list
        this.formatData(list)
        this.sumTotal(list)
        this.objectList = list
        this.total = r.data.total
        console.log('listGameTransHistInfo.....................', r.data.list)
      })
    },
    formatData(list) {
      for (var item of list) {
        item.totalMinute = formatNumeric(moment(item.modifiedDt).diff(item.createDt) / (1000 * 60))
        // item.totalMinute = item.totalMinute.toFixed(2)
        item.totalCount = item.winCount + item.loseCount
        item.winRate = 0
        item.avgSecond = 0
        if (item.totalCount) {
          item.winRate = formatNumeric(item.winCount / item.totalCount * 100)
          // item.winRate = item.winRate.toFixed(2)
          item.avgSecond = formatNumeric(moment(item.modifiedDt).diff(item.createDt) / 1000 / item.totalCount)
          // item.avgSecond = item.avgSecond.toFixed(0)
        }
      }
      this.objectList = list
    },
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    timeFormat(date) {
      return moment(date.createDt).format('HH:mm')
    },
    timeFormat2(date) {
      return moment(date.modifiedDt).format('HH:mm')
    },
    dateFormat(date) {
      return moment(date.createDt).format('YYYY-MM-DD')
    },
    dateTimeFormat(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        if (this.sums.length) {
          sums[index] = this.sums[index]
        }
      })
      return sums
    },
    sumTotal(objectList) {
      this.sums = ['', '', '', '', 0, '', '', 0, 0, 0, 0, '', '']
      this.sums[1] = '汇总'
      for (var i = 0; i < objectList.length; i++) {
        // 6-下注金额 10-输赢 11-余额 12-码量  14-码佣
        this.sums[4] = this.sums[4] + objectList[i].profit
        this.sums[7] = this.sums[7] + objectList[i].totalMinute
        this.sums[8] = this.sums[8] + objectList[i].winCount
        this.sums[9] = this.sums[9] + objectList[i].loseCount
        this.sums[10] = this.sums[10] + objectList[i].winCount + objectList[i].loseCount
      }
      for (i = 0; i < this.sums.length; i++) {
        if (i > 2 && this.sums[i]) {
          this.sums[i] = formatMoney(formatNumber(this.sums[i]))
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.data-info {
  line-height: 25px;
  border-top: 1px solid #f5f7fa;
  padding: 5px 10px;
  word-break: break-all;
}

/*开牌 */
.container .mipai-box {
  top: 0%;
  left: 0%;
  width: 175px;
  height: 125px;
  position: absolute;
  flex-direction: column;
  border-radius: .5rem;
  z-index: 999;
  padding-top: 1%;
  background: #131313;
  opacity: 0.9;
  border-radius: 0.5rem;
}

.container .mipai-box2 {
  width: 250px;
  height: 250px;
}

.container .mipai-box.active {
  display: block;
  animation: bounce02 1.5s;
}

.container .mipai-box.active-a {
  display: block !important;
  animation: bounce03 1.5s;
}

.container .mipai-box.inactive {
  display: none;
}

.container .mipai-box .xian-box,
.container .mipai-box .zhuang-box {
  height: 60px;
  position: relative;
  display: flex;
  border-bottom: 1px solid #222222;
}

.container .mipai-box .total-box {
  font-size: 16px;
  position: absolute;
  left: 5px;
  top: 30px;
  padding: 0 5px 0 5px;
  text-align: center;
  border-radius: 50%;
}

.container .mipai-box .title-box {
  position: absolute;
  font-size: 16px;
  top: 5px;
  left: 5px;
  text-align: center;
}

.container .mipai-box .zhuang-box .total-box,
.container .mipai-box .long-box .total-box {
  color: #ffffff;
  background-color: #c1182e;
}

.container .mipai-box .xian-box .total-box,
.container .mipai-box .hu-box .total-box {
  color: #ffffff;
  background-color: #2b4be9;
}

.container .mipai-box .zhuang-box .title-box,
.container .mipai-box .long-box .title-box {
  color: #c1182e;
}

.container .mipai-box .xian-box .title-box,
.container .mipai-box .hu-box .title-box,
.container .mipai-box .feng-box .title-box {
  color: #2b4be9;
}

.container .mipai-box .smallcard {
  position: absolute;
}

.container .mipai-box .smallcard.on {
  display: block;
}

.container .mipai-box .smallcard.card1 {
  top: 10px;
  height: 40px;
  left: 40px;
  width: 30px;
}

.container .mipai-box .smallcard.card2 {
  top: 10px;
  height: 40px;
  left: 80px;
  width: 30px;
}

.container .mipai-box .smallcard.card3 {
  top: 20px;
  height: 30px;
  left: 120px;
  width: 40px;
}

.container .mipai-box2 .smallcard.card3 {
  top: 10px;
  height: 40px;
  left: 120px;
  width: 30px;
}

.container .mipai-box .smallcard.card4 {
  top: 10px;
  height: 40px;
  left: 160px;
  width: 30px;
}

.container .mipai-box .smallcard.card5 {
  top: 10px;
  height: 40px;
  left: 200px;
  width: 30px;
}

.mobile.app-container {
  margin: 5px;
  margin-top: 40px;
  padding-top: 0px;
  padding-left: 0;
  padding-right: 0;
  background-color: white;
}
</style>
