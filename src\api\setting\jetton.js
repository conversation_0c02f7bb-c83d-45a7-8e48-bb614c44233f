import request from '@/utils/request'

export function listJetton(data) {
  return request({
    url: '/jetton/list',
    method: 'post',
    data
  })
}
export function addJetton(data) {
  return request({
    url: '/jetton/add',
    method: 'post',
    data
  })
}
export function updateJetton(data) {
  return request({
    url: '/jetton/update',
    method: 'post',
    data
  })
}
export function deleteJetton(ids) {
  return request({
    url: '/jetton/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
