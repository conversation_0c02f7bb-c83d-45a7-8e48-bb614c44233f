import request from '@/utils/request'
export function listGameResult(data) {
  return request({
    url: '/game/list-result',
    method: 'post',
    data
  })
}
export function deleteGame(id) {
  return request({
    url: '/game/delete-result',
    method: 'get',
    params: {
      id: id
    }
  })
}
export function updateGameResult(data) {
  return request({
    url: '/game/update-result',
    method: 'post',
    data
  })
}

export function addGame(data) {
  return request({
    url: '/game/info/add',
    method: 'post',
    data
  })
}

export function updateGame(data) {
  return request({
    url: '/game/info/update',
    method: 'post',
    data
  })
}

export function chipGame(data) {
  return request({
    url: '/game/info/chip',
    method: 'post',
    data
  })
}
