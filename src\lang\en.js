export default {
  system: {
    reflesh: 'Reflesh',
    close: 'Close',
    closeOther: 'Close Other',
    closeAll: 'Close All'
  },
  test: {
    title: 'Page style setting',
    theme: 'Theme Color'
  },
  login: {
    title: 'User Login',
    tips: 'Can not see clearly. Click to change',
    bodyTittle: 'Ultimate Experience in Advantage Club',
    bodyDesc: 'high-quality service, let you enjoy the fun of the game',
    placeholder: {
      account: 'Account',
      password: 'Password',
      verifyCode: 'Verification Code'
    },
    validateMsg: {
      verifyCodeLen: 'Verification code length is 4',
      verifyCodeError: 'The verification code error',
      verifyCode: 'Please enter the verification code',
      account: 'Please enter the login account',
      password: 'Please enter the login password',
      passwordError: 'Password Error',
      accountClosed: 'Account Closed',
      accountNotExisted: 'The account has not been opened or has been closed'
    }
  },
  game: {
    gameType: 'Game Type',
    tableNo: 'Table No',
    matchNo: 'Sheo',
    gameNo: 'Game',
    gameNumber: 'NO.',
    winAmount: 'Win Amount',
    result: 'Game Result',
    betDetail: 'Bet Detail',
    balance: 'Balance',
    betTime: 'Bet Time',
    limit: 'Limit',
    banker: 'Banker',
    player: 'Player',
    luckSix: 'Luck Six',
    tie: 'Tie',
    pair: 'Pair',
    winner: 'Winner',
    bankerAbbr: 'R',
    playerAbbr: 'B',
    bankerPair: 'C',
    playerPair: 'Player Pair',
    allPair: 'Banker Pair,Player Pair',
    noPair: 'None',
    dragon: 'Dragon',
    tiger: 'Tiger',
    dragonAbbr: 'D',
    tigerAbbr: 'T',
    player1Equal: 'Player 1 Equal',
    player2Equal: 'Player 2 Equal',
    player3Equal: 'Player 3 Equal',
    player1Double: 'Player 1 Double',
    player2Double: 'Player 2 Double',
    player3Double: 'Player 3 Double',
    player1Super: 'Player 1 Super',
    player2Super: 'Player 2 Super',
    player3Super: 'Player 3 Super',
    chipCustom: 'Custom',
    remain: '',
    totalMax: 'Table Limit',
    dealer: 'Dealer',
    askPlayer: 'Next P',
    askBanker: 'Next B',
    askDragon: 'Next D',
    askTiger: 'Next T',
    changeTable: 'Table List',
    confirm: 'Confirm',
    cancel: 'Cancel',
    repeat: 'Repeat',
    toBet: 'To Bet',
    openGame: 'Opening',
    waiting: 'Waiting',
    addTime: 'Added Time',
    notChipTimes: 'not bet times is greater than ',
    greaterTotalMax: 'bet amount is greater than limit',
    lessMin: 'bet amount is less than min. limit',
    greaterMax: 'bet amount is greater than max. limit',
    videoHigh: 'high',
    videoLow: 'low',
    yourAreHere: 'You are here',
    bull0: 'N',
    bull10: 'B',
    remainPointLow: 'balance is not enough',
    totalText: 'Total'
  },
  bull: {
    bankerWin: 'Banker Win',
    player1Win: 'Player1 Win',
    player2Win: 'Player2 Win',
    player3Win: 'Player3 Win',
    noBull: 'No',
    bull: 'Bull'
  },
  home: {
    table: {
      totalAgent: 'Total Agent',
      newAgentToday: 'New Agents Today',
      totalMember: 'Total Member',
      newMembersToday: 'New Members Today',
      loginAccount: 'Login Account',
      nickname: 'Nickname',
      loginTime: 'Login Time',
      loginIp: 'Login Ip',
      loginAddress: 'Login Address',
      availableBalance: 'Available Balance',
      share: 'Share',
      rateOfCodeWashing: 'Commission Rate',
      above: 'above'
    }
  },
  status: {
    enabled: 'Enabled',
    disabled: 'Disabled',
    open: 'Opened',
    closed: 'Closed',
    onLine: 'On-line',
    offLine: 'Off-line'
  },
  operate: {
    login: 'Login',
    sysgen: 'Sysgen',
    add: 'Add',
    edit: 'Edit',
    modifyResult: 'Modify Result',
    deleteOne: 'Delete One',
    save: 'Save',
    change: 'Modify',
    delete: 'Delete',
    confirm: 'Confirm',
    cancel: 'Cancel',
    viewDetail: 'View Detail',
    moreOperate: 'More',
    accountEnabled: 'Account Enabled',
    accountDisabled: 'Account Disabled',
    authSetting: 'Auth Setting',
    betEnabled: 'Bet Enabled',
    betDisabled: 'Bet Disabled',
    memberEdit: 'Member Edit',
    agentEdit: 'Agent Edit',
    passwordChange: 'Password Change',
    rechargeWithdraw: 'Deposit Withdraw',
    settlement: 'Settlement',
    betHistory: 'Bet History',
    transactionHistory: 'Transaction History',
    commissionHistory: 'Commission History',
    commissionSettlement: 'Commission Settlement',
    block: 'Block',
    unblock: 'Unblock',
    message: {
      tips: 'Tips',
      cancel: 'Cancelled',
      success: 'Success',
      fail: 'Fail'
    },
    info: {
      continue: 'Do you want to continue'
    }
  },
  chargeWithdrawForm: {
    tittle: 'Deposit Withdraw',
    operation: 'Operation',
    recharge: 'Deposit',
    withdraw: 'Withdraw',
    rechargeType: 'Deposit Type',
    amount: 'Amount',
    cash: 'Cash',
    sign: 'Sign',
    payout: 'Payout',
    priceUnit: 'dollar',
    placeholder: {
      amount: 'Please enter the amount'
    },
    validateMsg: {
      amount: 'Amount must be greater than 0!'
    }
  },
  memberAddForm: {
    validateMsg: {
      account: 'Please enter your account',
      accountExist: 'The account already exists',
      rateSetting: 'Please set the washing rate',
      rateLimit: 'Washing rate cannot be greater than 2%',
      chipOptions: 'Please select bet chip option',
      chipOptionsLimit: 'Up to 3 bet chips'
    }
  },
  member: {
    accountInfo: 'Account Info',
    washRateInfo: 'Washing Rate Setting ',
    limitInfo: 'Limit Setting',
    operateInfo: {
      stopInfo: 'This operation will disable betting. Do you want to continue?'
    },
    passForm: {
      tittle: 'Password Change',
      loginAccount: 'Login Account',
      oldPassword: 'Old Password',
      newpassword: 'New password',
      password: 'Password',
      passwordConfirm: 'Confirm',
      placeholder: 'Password (6-16 letters or numbers)',
      validateMsg: {
        password: 'Please enter the password',
        oldPassword: 'Please enter the old password',
        newPassword: 'Please enter the new password',
        oldPasswordError: 'Old Password Error',
        passwordConfirm: 'Please enter the confirmation password',
        passwordFormate: 'Incorrect password format',
        passwordDifferent: 'Passwords are inconsistent'
      }
    },
    bettingStatus: 'Bet Status',
    subordinateAgent: 'Agent',
    account: 'Account',
    nickname: 'Nickname',
    memberAccount: 'Member Account',
    memberNickname: 'Nickname',
    fullName: 'Full Name',
    mobilePhone: 'Phone No',
    memberType: 'Type',
    normal: 'Normal',
    hide: 'Hide',
    role: 'Role',
    agent: 'Agent',
    avatar: 'Avatar',
    pagcor: 'Pagcor',
    cashier: 'Cashier',
    currency: 'Currency',
    accountBalance: 'Balance',
    rateOfCodeWashing: 'Commission Rate',
    limitRed: 'Bet Limit',
    onLine: 'On Line',
    recentlyLoggedInIp: 'Last Login Ip',
    recentlyLoggedInTime: 'Last Login Time',
    createdTime: 'Created Time',
    codeWashingFrom: 'From Chip Count',
    codeWashingTo: 'To Chip Count',
    washTips: '0 for Infinity',
    operation: 'Operation',
    totalDeposit: 'Total Deposit',
    totalWithdrawal: 'Total Withdrawal',
    totalValidBet: 'Total Valid Bet',
    totalWinOrLose: 'Total Win/Loss',
    commission: 'Commission(%)',
    commissionBalance: 'Commission Amount',
    jetton: {
      limitSetting: 'Limit Setting',
      chipNo: 'Chip No',
      chipName: 'Chip Name',
      lowerLimit: 'Lower Limit',
      upperLimit: 'Upper Limit',
      chipsOption: 'Chip Options'
    },
    placeholder: {
      memberAccount: 'Search By Member Account'
    },
    level: 'Level',
    betMin: 'Min Bet Per Time',
    betMax: 'Max Bet Per Time',
    betMax2: 'Max Bet Per Game',
    betMax3: 'Max Bet Draw Per Game',
    maxWin: 'Max Win Per Day',
    dataError: ' greater than '
  },
  agentForm: {
    accountStatus: 'Account Status',
    bettingStatus: 'Betting Status',
    agentAccount: 'Account',
    agentNickname: 'Nickname',
    fullName: 'Full Name',
    mobilePhone: 'Mobile Phone',
    accountBalance: 'Account Balance',
    rateOfCodeWashing: 'Commission Rate',
    limitRed: 'Bet Limit',
    onLine: 'On Line',
    share: 'Share(%)',
    operation: 'Operation',
    parentAgent: 'Parent Agent',
    createdTime: 'Created Time',
    childAgent: 'Child Agent',
    info: {
      noData: 'No more agent data',
      noParentData: 'No superior agent data',
      noChildData: 'No lower level agent data'
    }
  },
  jetton: {
    chipInfo: 'Chip Info',
    chipNo: 'Chip No',
    chipName: 'Chip Name',
    betLowerLimit: 'Bet Lower Limit',
    betUpperLimit: 'Bet Upper Limit',
    chipOption: 'Chip Option',
    operation: 'Operation',
    validateMsg: {
      jettonNo: 'Chip No is required',
      jettonName: 'Chip Name is required',
      downLimit: 'Bet Lower Limit is required',
      upperLimit: 'Bet Lower Limit is required'
    },
    msg: {
      chipRepeat: 'Chip Repeat Setting',
      setChip: 'Please set the chips first',
      threeChip: 'Please set three chips first',
      deleteData: 'Please select the data you need to delete'
    }
  },
  tableInfo: {
    tableInfo: 'Table Info',
    tableNo: 'Table No',
    tableName: 'Table Name',
    tableNameEn: 'English Name',
    tableType: 'Table Type',
    cebu: 'Cebu',
    manila: 'Manila',
    casino: 'Casino',
    networkTable: 'Network',
    liveTable: 'Live',
    gameType: 'Game Type',
    betMethod: 'Bet Method',
    PCVideoUrl: 'PC Video Url',
    PCVideoUrl1: 'PC Video Url 1',
    PCVideoUrl2: 'PC Video Url 2',
    PCVideoUrl3: 'PC Video Url 3',
    dealerUrl: 'Dealer Url',
    AppVideoUrl: 'App Video Url',
    AppVideoUrl1: 'App Video Url 1',
    AppVideoUrl2: 'App Video Url 2',
    AppVideoUrl3: 'App Video Url 3',
    shoeIp: 'Shoe Ip',
    port: 'Port',
    shoePort: 'Fight Interval',
    serverPort: 'Server Port',
    serverIp: 'Server Ip',
    notChipTimes: 'Not Chip',
    chipInterval: 'Chip Interval',
    totalMax: 'Total Max',
    totalMax2: 'Total Max 2',
    totalMax3: 'Total Max 3',
    betMin: 'Min Bet',
    betMax: 'Max Bet',
    tieMin: 'Min Draw Bet',
    tieMax: 'Max Draw Bet',
    pairMin: 'Min Pair Bet',
    pairMax: 'Max Pair Bet',
    superMin: 'Min Super 6',
    superMax: 'Max Super 6',
    isConfirm: 'Confirm Card',
    isHide: 'Hide Card',
    isJoin: 'Is Join',
    yes: 'Yes',
    no: 'No',
    need: 'Yes',
    noNeed: 'No',
    showCard: 'Show Card',
    hideCard: 'Hide Card',
    freeCommission: 'Is Free',
    currencyCode: 'Currency Code',
    freeYes: 'Yes',
    freeNo: 'No',
    status: 'Status',
    operation: 'Operation',
    validateMsg: {
      tableNo: 'Table No is required',
      tableName: 'Table Name is required',
      tableNameEn: 'English Name is required',
      gameType: 'Game Type is required',
      gameCategory: 'Bet Method is required',
      rtmp: 'PC Vedio Url 1 is required',
      rtmp2: 'PC Vedio Url 2 is required',
      rtmp3: 'PC Vedio Url 3 is required',
      dealerRtmp: 'Dealer Url is required',
      rtsp: 'App Vedio Url 1 is required',
      rtsp2: 'App Vedio Url 2 is required',
      rtsp3: 'App Vedio Url 3 is required',
      betMin: 'Min Bet is required',
      betMax: 'Max Bet is required',
      tieMin: 'Min Tie Bet is required',
      tieMax: 'Max Tie Bet is required',
      pairMin: 'Min Pair Bet is required',
      pairMax: 'Max Pair Bet is required',
      superMin: 'Min Super 6 is required',
      superMax: 'Max Super 6 is required',
      totalMax: 'Max Total is required',
      totalMax2: 'Max Total 2 is required',
      totalMax3: 'Max Total 3 is required',
      serverIp: 'Server Ip is required',
      serverPort: 'Server Port is required',
      shoeIp: 'Shoe Ip is required',
      shoePort: 'Fight Interval is required',
      notChipTimes: 'Not Chip is required'
    }
  },
  gameCategory: {
    netBet: 'Net Bet',
    mobileBet: 'Phone Bet',
    speed: 'Speed',
    proxy: 'Proxy'
  },
  gameTypeEnum: {
    all: 'All',
    baccarat: 'COCK FIGHTING',
    dragonAndTiger: 'DragonAndTiger',
    bullAndBull: 'BullAndBull'
  },
  tableInfoStatusEnum: {
    new: 'New',
    end: 'End',
    betting: 'Betting',
    bet: 'Betted',
    start: 'Start',
    wait: 'Wait',
    break: 'Break',
    pause: 'Pause',
    washing: 'Washing'
  },
  timeUnit: {
    second: 'seconds'
  },
  noticeInfo: {
    notice: 'Notice',
    noticeStatus: 'Notice Status',
    noticeType: 'Notice Type',
    noticeTittle: 'Notice Tittle',
    noticeContent: 'Notice Content',
    noticeContentEn: 'English Content',
    operation: 'Operation',
    placeholder: {
      messageType: 'Please Select Message Type'
    }
  },
  noticeStatus: {
    valid: 'Valid',
    invalid: 'Invalid'
  },
  noticeType: {
    webMessage: 'Web Notice',
    frontMessage: 'Front Notice'
  },
  dealer: {
    account: 'Account',
    nickName: 'Nickname',
    fullName: 'Full Name',
    phone: 'Phone',
    avatar: 'Image',
    onLine: 'on-line',
    createTime: 'Create Time',
    remark: 'Remark',
    operation: 'Operation',
    info: 'Dealer Info',
    clickToAdd: 'Click to Add',
    clickToChange: 'Click to Change',
    validateMsg: {
      account: 'Account is required',
      avatar: 'Iamge is required',
      nickname: 'NickName is required',
      fullName: 'Full Name is required',
      phoneNo: 'Phone is required'
    },
    placeholder: {
      account: 'Query according to account number'
    }
  },
  pointHistory: {
    operateTime: 'Operate Time',
    userType: 'User Type',
    userAccount: 'User Account',
    nickName: 'User Nickname',
    balanceBefore: 'Amount Before',
    operateAmount: 'Operate Amount',
    balanceAfter: 'Amount After',
    operateAccount: 'Operator[Account]',
    operateType: 'Operate Type',
    placeholder: {
      pointType: 'Query by operation type',
      account: 'Query by account'
    }
  },
  dateTemplate: {
    today: 'Today',
    aWeekAgo: 'A Week Ago',
    tomorrow: 'Tomorrow',
    selectDate: 'Please Select Date',
    to: 'To',
    startDate: 'Start Date',
    endDate: 'End Date',
    lastMonth: 'Last Month',
    lastWeek: 'Last Week',
    last3Months: 'Last 3 Months'
  },
  userType: {
    agent: 'Agent',
    member: 'Member'
  },
  betInfo: {
    betNo: 'Bet No',
    tableNo: 'Table No',
    shoeNo: 'Match No',
    gameNo: 'Game No',
    memberAccount: 'Member Account',
    memberName: 'Member Name',
    betAmount: 'Bet Amount',
    betDetail: 'Bet Detail',
    cardType: 'Cards Type',
    cardsView: 'Cards View',
    cardResult: 'Result',
    winAmount: 'Win Amount',
    banlance: 'Banlance',
    washAmount: 'Commission Amount',
    washRate: 'Commission Rate(%)',
    commission: 'Commission',
    betTime: 'Bet Time',
    placeholder: {
      gameType: 'Query by game type',
      betNo: 'Query by bet no',
      gameNo: 'Query by game No',
      matchNo: 'Query by match No',
      tableNo: 'Query by table No',
      userAccount: 'Query by user account'
    }
  },
  macthInfo: {
    tableInfo: 'Table Info',
    shoeNo: 'Match No',
    gameNo: 'Game No',
    cardsResult: 'Result',
    gameStartTime: 'Game Start Time',
    placeholder: {
      table: 'Please Select Table',
      gameNo: 'Please Input Game No.'
    }
  },
  memberSettle: {
    placeholder: {
      gameType: 'Please Select Game Type'
    },
    gameType: 'Game Type',
    memberAccount: 'Member Account',
    memberNickname: 'Nickname',
    currentAmount: 'Balance',
    betTimes: 'Bet Times',
    betAmount: 'Bet Amount',
    winAmount: 'Win/Lost',
    totalWashAmount: 'Total Bet Amount',
    commissionAmount: 'Commission Amount'
  },
  agentSettle: {
    placeholder: {
      gameType: 'Please Select Game Type'
    },
    gameType: 'Game Type',
    agentAccount: 'Agent Account',
    agentNickname: 'Agent Nickname',
    totalWinAmount: 'Total Win Amount',
    totalWashAmount: 'Total Bet Amount',
    washRate: 'Wash Rate(%)',
    commissionAmount: 'Commission Amount',
    washIncome: 'Wash Income',
    share: 'Share(%)',
    shareIncome: 'Share Income',
    totalIncome: 'Total Income',
    superiorIncome: 'Superior Income',
    operation: 'Operation',
    back: 'Back',
    agentReport: 'Report'
  },
  jettonSettle: {
    agentAccount: 'Agent Account',
    agentNickname: 'Agent Nickname',
    memberAccount: 'Member Account',
    memberNickname: 'Nickname',
    currentWashRate: 'Commission Rate',
    totalWashAmount: 'Total Commission Amount',
    commissionAmount: 'Total Commission Amount',
    operation: 'Operation',
    agentTittle: 'Agent Commission Settlement',
    memberTittle: 'Member Commission Settlement'
  },
  header: {
    logout: 'Logout',
    backHome: 'Back Home',
    passChange: 'Password Change',
    appDownload: 'App Download',
    menuNavigation: 'Menu Navigation',
    playNow: 'Play Now',
    copyright: ' 优势玩家 All rights reserved',
    welcome: 'Welcome Login'
  },
  role: {
    roleName: 'Role Name',
    roleNo: 'Role No',
    sortNo: 'Sort No',
    userType: 'User Type',
    status: 'Status',
    remark: 'Remark',
    operation: 'Operation',
    roleInfo: 'Role Info',
    nodeLocation: 'Node Location',
    permissionConfiguration: 'Permission Configuration'
  },
  button: {
    name: 'Button Name',
    no: 'Button No',
    sortNo: 'Sort No',
    buttonInfo: 'Button Info',
    status: 'Status',
    remark: 'Remark',
    operation: 'Operation',
    validateMsg: {
      buttonName: 'Please Input Button Name',
      buttonNo: 'Please Input Button No',
      sortNo: 'Please Input Short No'
    }
  },
  menu: {
    locale: 'Locale',
    componentName: 'Component Name',
    componentPath: 'Component Path',
    menuIcon: 'Menu Icon',
    menuName: 'Menu Name',
    menuUrl: 'Menu Url',
    sortNo: 'Sort No',
    status: 'Status',
    remark: 'Remark',
    operation: 'Operation',
    menuInfo: 'Menu Info',
    menuLocation: 'Menu Location',
    systemMenu: 'System Menu',
    menuType: 'Menu Level',
    ifHidden: 'If Hidden',
    funtionManage: 'Funtion Manage',
    buttonOptions: {
      slected: 'Selected Funtion',
      waiting: 'Waiting Select Funtion'
    },
    levelOptions: {
      level1: 'One Level',
      other: 'Other Level'
    }
  },
  commonStatus: {
    valid: 'Valid',
    invalid: 'Invalid'
  },
  hiddenOptions: {
    hidden: 'Hidden',
    show: 'Show'
  },
  winRate: {
    placeholder: 'Please Slelet Game Type',
    gameType: 'Game Type',
    rateNo: 'Rate No',
    rate: 'Rate',
    status: 'Status',
    remark: 'Remark',
    operation: 'Operation',
    rateInfo: 'Rate Info',
    validateMsg: {
      rate: 'Please Input Rate',
      remark: 'Please Input Remark'
    }
  },
  currencyCode: {
    RMB: 'RMB',
    USD: 'USD'
  },
  router: {
    dashboard: 'Dashboard',
    changePassword: 'Password Change',
    authorityModule: 'Authority Module',
    accountCenter: 'Account Center',
    reportMange: 'Report Manage',
    gameSetting: 'Game Setting',
    menuMange: 'Menu Manage',
    buttonMange: 'Button Manage',
    roleManage: 'Role Manage',
    pointHistory: 'Transaction History',
    betBill: 'Bet History',
    memberDailyReport: 'Member Daily Report',
    agentDailyReport: 'Agent Daily Report',
    gameInTable: 'Games On Table',
    operateHistory: 'Operate History',
    commissionSettlement: 'Commission Settlement',
    agentList: 'Agent List',
    agentAdd: 'Agent Add',
    agentEdit: 'Agent Edit',
    memberList: 'Member List',
    memberAdd: 'Member Add',
    memberEdit: 'Member Edit',
    subAccountList: 'Sub Account List',
    jetton: 'Jetton',
    tableInfo: 'Table Info',
    notice: 'Notice',
    dealer: 'Dealer',
    winRate: 'Win Rate',
    country: 'IP Blocking',
    gameResult: 'Game Result',
    ipWhiteList: 'IP White List',
    otherList: 'Other List',
    otherAdd: 'Other Add',
    otherEdit: 'Other Edit'
  },
  userTypeEnum: {
    superAdmin: 'Super Admin',
    agent: 'Agent',
    member: 'Member',
    subAccount: 'Sub Account',
    dealer: 'Dealer'
  },
  operateTypeEnum: {
    login: 'Login ',
    accountOperate: 'Account Disable/Enable',
    betOperate: 'Bet Disable/Enable',
    recharge: 'Deposit',
    withdraw: 'Withdraw',
    passwordChange: 'Password Change',
    memberEdit: 'Member Edit',
    agentEdit: 'Agent Edit',
    commissionSettle: 'Commission Settle'
  },
  platformEnum: {
    pc: 'PC',
    android: 'Android',
    apple: 'Apple',
    browser: 'Browser'
  },
  operateHistory: {
    placeholder: {
      operateType: 'Select Operate Type',
      userType: 'Select User Type',
      operatePlatform: 'Select Operate Platform',
      operateAccount: 'Input Operate Account'
    },
    operateAccount: 'Operate Account',
    operateType: 'Operate Type',
    operatedAccount: 'Operated Account',
    userType: 'User Type',
    operatePlatform: 'Operate Platform',
    operateIp: 'Operate Ip',
    operateArea: 'Operate Area',
    operateTime: 'Operate Time'
  },
  subAccount: {
    subAccountInfo: 'Sub Account Info',
    placeholder: 'Query by account',
    accountStatus: 'Account Status',
    subordinateAgent: 'Agent',
    account: 'Account',
    nickname: 'Nickname',
    name: 'Full Name',
    phone: 'Phone',
    onLine: 'On Line',
    createdTime: 'Created ime',
    remark: 'Remark',
    operation: 'Operation',
    subAccountAuth: 'Sub Account Auth',
    password: 'Password',
    confirmPassword: 'Confirm'
  },
  blockStatusEnum: {
    all: 'All',
    blocked: 'Blocked',
    unblocked: 'Unblocked'
  },
  countryInfo: {
    info: 'Country Info',
    code: 'Code',
    name: 'Name',
    nameEn: 'English Name ',
    block: 'Block',
    yes: 'Yes',
    no: 'No',
    validateMsg: {
      name: 'Please Input Name',
      code: 'Please Input Code',
      nameEn: 'Please Input English Name'
    },
    msg: {
      blockData: 'Please select the data you need to block',
      unblockData: 'Please select the data you need to unblock',
      editData: 'Please select the data you need to edit'
    },
    placeholder: {
      keywords: 'Query by name/code',
      blockStatus: 'Query by block status'
    }
  },
  ipWhiteListInfo: {
    title: 'IP Info',
    type: 'IP Type',
    ipAddress: 'IP Address',
    blackIp: 'Black IP',
    whiteIp: 'White IP',
    placeholder: {
      type: 'Search by IP Type',
      ipAddress: 'Search by IP Address'
    }
  }
}
