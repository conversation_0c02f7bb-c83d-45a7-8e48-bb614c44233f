import request from '@/utils/request'
export function list(data) {
  return request({
    url: '/gameVerify/list',
    method: 'post',
    data
  })
}
export function list2(data) {
  return request({
    url: '/gameVerify/list2',
    method: 'post',
    data
  })
}
export function add(data) {
  return request({
    url: '/gameVerify/add',
    method: 'post',
    data
  })
}
export function addList(data) {
  return request({
    url: '/gameVerify/addList',
    method: 'post',
    data
  })
}
export function update(data) {
  return request({
    url: '/gameVerify/update',
    method: 'post',
    data
  })
}
export function del(ids) {
  return request({
    url: '/gameVerify/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
export function verify(id) {
  return request({
    url: '/gameVerify/verify',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function stopVerify(id) {
  return request({
    url: '/gameVerify/stop',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function addGame(gameCount, gameType, createBy) {
  return request({
    url: '/gameVerify/addGame',
    method: 'get',
    params: {
      gameCount: gameCount,
      gameType: gameType,
      createBy: createBy
    }
  })
}
