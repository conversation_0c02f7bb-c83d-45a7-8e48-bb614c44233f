import request from '@/utils/request'
export function listGameTransHistInfo(data) {
  return request({
    url: '/game-trans-hist/info/list',
    method: 'post',
    data
  })
}
export function listGameTransHist(data) {
  return request({
    url: '/game-trans-hist/list',
    method: 'post',
    data
  })
}
export function getPersonalWashInfo(data) {
  return request({
    url: '/game-trans-hist/wash-info',
    method: 'post',
    data
  })
}

export function listByVerifyId(verifyId) {
  return request({
    url: '/game-trans-hist/list-by-verify',
    method: 'get',
    params: {
      verifyId: verifyId
    }
  })
}

export function addGameTransHist(data) {
  return request({
    url: '/game-trans-hist/add',
    method: 'post',
    data
  })
}
