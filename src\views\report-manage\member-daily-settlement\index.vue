<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-date-picker
            v-model="memberParam.date"
            type="daterange"
            align="right"
            unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')"
            :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions"
          />
          <el-select v-model="memberParam.gameType" clearable :placeholder="$t('memberSettle.placeholder.gameType')">
            <el-option
              v-for="item in gameTypeEnums"
              :key="item.type"
              :label="$t(item.label)"
              :value="item.type"
            />
          </el-select>
          <el-input v-model="memberParam.userName" style="width:224px" :placeholder="$t('betInfo.placeholder.userAccount')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row show-summary :summary-method="getSummaries">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="gameType" align="center" :label="$t('memberSettle.gameType')" :formatter="gameTypeFilter" />
      <el-table-column prop="user.userName" align="center" :label="$t('memberSettle.memberAccount')" />
      <el-table-column prop="user.nickName" align="center" :label="$t('memberSettle.memberNickname')" />
      <el-table-column prop="member.remainPoint" align="center" :label="$t('memberSettle.currentAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.member ? formatMoney(scope.row.member.remainPoint) : '' }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalBetCount" align="center" :label="$t('memberSettle.betTimes')" />
      <el-table-column prop="totalBetAmount" align="center" :label="$t('memberSettle.betAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.totalBetAmount | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalWinAmount" align="center" :label="$t('memberSettle.winAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.totalWinAmount | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalWashAmount" align="center" :label="$t('memberSettle.totalWashAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.totalWashAmount | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="washRate" align="center" :label="$t('memberSettle.commissionAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.washRate*scope.row.totalWashAmount/100 | numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
import { listMemberDaily } from '@/api/settlement/memberDaily'
import { getGameTypeEnum, GameTypeEnum } from '@/enums/setting.js'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'MemberList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formatMoney,
      formatNumber,
      memberParam: {
        memberType: 3,
        gameType: 0,
        date: ''
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      sums: []
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ]),
    gameTypeEnums() {
      return GameTypeEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      if (this.member.memberType === 4) {
        this.memberParam.parentId = this.member.parentId
      } else {
        this.memberParam.parentId = this.member.id
      }
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      listMemberDaily(this.memberParam).then(r => {
        this.sumTotal(r.data.list)
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    gameTypeFilter: function(row, column) {
      return this.$t(getGameTypeEnum(row.gameType).label)
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        if (this.sums.length) {
          sums[index] = this.sums[index]
        }
      })
      return sums
    },
    sumTotal(objectList) {
      this.sums = ['', '', '', '', '', '', 0, 0, 0, 0, 0]
      this.sums[2] = this.$t('game.totalText')
      for (var i = 0; i < objectList.length; i++) {
        this.sums[6] = this.sums[6] + parseFloat(objectList[i].totalBetCount)
        this.sums[7] = this.sums[7] + parseFloat(objectList[i].totalBetAmount)
        this.sums[8] = this.sums[8] + parseFloat(objectList[i].totalWinAmount)
        this.sums[9] = this.sums[9] + parseFloat(objectList[i].totalWashAmount)
        this.sums[10] = this.sums[10] + parseFloat(objectList[i].washRate) * parseFloat(objectList[i].totalWashAmount) / 100
      }
      for (i = 0; i < this.sums.length; i++) {
        if (i > 2 && this.sums[i]) {
          this.sums[i] = formatMoney(formatNumber(this.sums[i]))
        }
      }
    }
  }
}
</script>
