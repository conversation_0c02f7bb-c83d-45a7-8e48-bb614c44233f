<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="发布信息" name="first">
        <el-form ref="postForm" :model="postForm" :rules="rules" class="form-container">
          <div class="createPost-main-container" style="width:500px">
            <el-form-item label-width="100px" label="文章分类:" class="postInfo-container-item">
              <CategorySelect :one-level-category="postForm.oneCategoryId" :two-level-category="postForm.twoCategoryId" @getSelectedCategory="getSelectedCategory" />
            </el-form-item>
            <el-form-item label-width="100px" label="作者:" class="postInfo-container-item">
              <el-input v-model="postForm.author" />
            </el-form-item>
            <el-form-item label-width="100px" label="文章编号:" class="postInfo-container-item">
              <el-input v-model="postForm.articleNo" />
            </el-form-item>
            <el-form-item label-width="100px" label="发布时间:" class="postInfo-container-item">
              <el-date-picker v-model="displayTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="选择发布时间" />
            </el-form-item>
            <el-form-item label-width="100px" label="排序:" class="postInfo-container-item">
              <el-input-number v-model="postForm.sortNo" />
            </el-form-item>
          </div>
          <el-form-item prop="image_uri" label-width="100px" label="标题图片:" style="margin-bottom: 30px;">
            <Upload v-model="postForm.titleImg" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="文章正文" name="second">
        <el-form ref="postForm" :model="postForm" :rules="rules">
          <el-form-item label-width="100px" prop="title" label="文章标题:">
            <el-input v-model="postForm.title" type="textarea" autosize placeholder="请输入标题" />
          </el-form-item>
          <el-form-item style="margin-bottom: 10px;" prop="brief" label-width="100px" label="文章摘要:">
            <el-input v-model="postForm.brief" placeholder="请输入摘要" />
          </el-form-item>
          <el-form-item prop="content" style="margin-bottom: 30px;">
            <Tinymce ref="editor" v-model="postForm.content" :height="280" />
          </el-form-item>
          <el-form-item>
            <el-row type="flex" justify="center" class="mt-10">
              <el-button :loading="loading" type="primary" @click="submitForm">
                直接发布
              </el-button>
            </el-row>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import CategorySelect from '@/components/CategorySelect/index'
import Tinymce from '@/components/Tinymce'
import Upload from '@/components/Upload/SingleImage3'
import { addArticle, fetchArticle } from '@/api/content/article'
// import { searchUser } from '@/api/remote-search'

const defaultForm = {
  status: 'draft',
  title: '', // 文章题目
  content: '', // 文章内容
  brief: '', // 文章摘要
  source_uri: '', // 文章外链
  image_uri: '', // 文章图片
  publishDt: undefined, // 前台展示时间
  id: undefined,
  platforms: ['a-platform'],
  comment_disabled: false,
  sortNo: 10,
  oneCategoryId: 0,
  twoCategoryId: 0
}

export default {
  name: 'ArticleDetail',
  components: { Tinymce, Upload, CategorySelect },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateRequire = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(rule.field + '为必传项'))
      } else {
        callback()
      }
    }
    return {
      activeName: 'first',
      postForm: Object.assign({}, defaultForm),
      loading: false,
      userListOptions: [],
      rules: {
        brief: [{ validator: validateRequire }],
        title: [{ validator: validateRequire }],
        content: [{ validator: validateRequire }]
      },
      tempRoute: {}
    }
  },
  computed: {
    displayTime: {
      get() {
        return (+new Date(this.postForm.publishDt))
      },
      set(val) {
        this.postForm.publishDt = new Date(val)
      }
    }
  },
  created() {
    if (this.isEdit) {
      const id = this.$route.params && this.$route.params.id
      this.fetchData(id)
    } else {
      this.postForm = Object.assign({}, defaultForm)
    }
    this.tempRoute = Object.assign({}, this.$route)
  },
  methods: {
    getSelectedCategory(oneLevelCategoryId, twoLevelCategoryId) {
      this.postForm.oneCategoryId = oneLevelCategoryId
      this.postForm.twoCategoryId = twoLevelCategoryId
    },
    fetchData(id) {
      fetchArticle(id).then(response => {
        this.postForm = response.data
      }).catch(err => {
        console.log(err)
      })
    },
    submitForm() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          addArticle(this.postForm).then(r => {
            if (this.isEdit) {
              this.$message({
                title: '成功',
                message: '修改文章成功',
                type: 'success',
                duration: 2000
              })
            } else {
              this.$message({
                title: '成功',
                message: '发布文章成功',
                type: 'success',
                duration: 2000
              })
              this.postForm = Object.assign({}, defaultForm)
              this.$refs.editor.setContent('')
              this.postForm.content = ''
              this.$refs.postForm.resetFields()
            }
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.createPost-container {
  position: relative;

  .createPost-main-container {
    padding: 40px 45px 20px 50px;

    .postInfo-container {
      position: relative;
      @include clearfix;
      margin-bottom: 10px;

      .postInfo-container-item {
        float: left;
      }
    }
  }

  .word-counter {
    width: 40px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
}

.article-textarea /deep/ {
  textarea {
    padding-right: 40px;
    resize: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #bfcbd9;
  }
}
</style>
