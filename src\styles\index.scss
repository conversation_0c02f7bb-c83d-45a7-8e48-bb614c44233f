@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-variables.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei, Arial, sans-serif;
  padding: 0px;
  margin: 0px;
  background-color: #f5f7fa;
  background-image: url("../assets/images/earth.png")
}

input {
  outline: none;
}

label {
  font-weight: normal;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-25 {
  padding-left: 25px;
}

.mt-5 {
  margin-top: 5px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.mr-10 {
  margin-right: 10px;
}

.ml-25 {
  margin-left: 25px;
}

/*宽度*/
.w-100p {
  width: 100%;
}

.w-100 {
  width: 100px !important;
}

.w-80 {
  width: 80px;
}

.w-50 {
  width: 50px;
}

.w-150 {
  width: 150px;
}

.w-10p {
  width: 10%;
}

.w-20p {
  width: 20%;
}

.w-50p {
  width: 50%;
}

/*颜色*/
.c-pink {
  color: #f56c6c
}

.c-red {
  color: red;
}

.c-yellow {
  color: yellow;
}

.c-green {
  color: green;
}

.c-blue {
  color: blue;
}

.c-rblue {
  color: #006dfe;
}

.c-grey {
  color: #666666;
}

.c-lgrey {
  color: #999999;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clear {
  clear: both;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.bold {
  font-weight: bold;
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: $--color-primary;
    }
  }
}

//main-container全局样式
.main-container {
  background-color: #f5f7fa;
  background-image: url("../assets/images/earth.png")
}

.app-container {
  margin: 10px;
  padding: 10px;
  background-color: white;
  border: 1px solid #dcdfe6;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.item-label {
  background-color: #F5F7FA;
  color: #1E1E1E;
  vertical-align: middle;
  display: table-cell;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 0 20px;
  line-height: 30px;
  width: 1px;
  white-space: nowrap;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: $--color-primary;
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

//font family
input,
textarea,
select,
button {
  font-family: Microsoft YaHei, Arial, sans-serif;
}

.main-container span,
.main-container label,
.main-container div {
  font-size: 12px !important;
}

.app-container span,
.app-container label,
.app-container div {
  font-size: 12px !important;
}

.el-dialog__header span {
  font-size: 16px !important;
}

.el-dialog__header {
  padding-top: 10px;
}

.el-dialog__footer {
  padding-bottom: 10px;
}

.el-dialog__headerbtn {
  top: 12px;
}

//main box
.main-box {
  width: 1200px;
  margin: 0 auto;
}

.span-title {
  font-weight: bold;
  font-size: 12px;
  border-left: 4px solid $--color-primary;
  padding-left: 5px;
}

.member-autocomplete.el-autocomplete-suggestion {
  width: auto !important;
}

.hide {
  display: none;
}

.fa {
  margin-right: 5px;
}

/*动画无限制循环*/
.fade-in-up {
  -webkit-animation: fadeInUp 1s .2s ease both;
  -moz-animation: fadeInUp 1s .2s ease both;
}

.fade-in-up-infinite {
  animation: fadeInUp 5s infinite;
  animation-direction: alternate;

  -webkit-animation: fadeInUp 5s infinite;
  -webkit-animation-direction: alternate;
}

@keyframes fadeInUp {
  0% {
    opacity: 0.3;
    -webkit-transform: translateY(0)
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0)
  }
}

@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0.3;
    -webkit-transform: translateY(0)
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0)
  }
}

@-moz-keyframes fadeInUp {
  0% {
    opacity: 0.3;
    -moz-transform: translateY(0)
  }

  100% {
    opacity: 1;
    -moz-transform: translateY(0)
  }
}

@-o-keyframes fadeInUp {
  0% {
    opacity: 0.3;
    -webkit-transform: translateY(0)
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0)
  }
}

/* 扑克===================================================================== */
.hcardpicmiddle.typec1 {
  background: url(../assets/images/other/pokercard/hpoker-c_1.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec2 {
  background: url(../assets/images/other/pokercard/hpoker-c_2.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec3 {
  background: url(../assets/images/other/pokercard/hpoker-c_3.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec4 {
  background: url(../assets/images/other/pokercard/hpoker-c_4.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec5 {
  background: url(../assets/images/other/pokercard/hpoker-c_5.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec6 {
  background: url(../assets/images/other/pokercard/hpoker-c_6.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec7 {
  background: url(../assets/images/other/pokercard/hpoker-c_7.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec8 {
  background: url(../assets/images/other/pokercard/hpoker-c_8.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec9 {
  background: url(../assets/images/other/pokercard/hpoker-c_9.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec10 {
  background: url(../assets/images/other/pokercard/hpoker-c_10.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec11 {
  background: url(../assets/images/other/pokercard/hpoker-c_11.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec12 {
  background: url(../assets/images/other/pokercard/hpoker-c_12.svg) no-repeat 0 0;
}

.hcardpicmiddle.typec13 {
  background: url(../assets/images/other/pokercard/hpoker-c_13.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed1 {
  background: url(../assets/images/other/pokercard/hpoker-d_1.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed2 {
  background: url(../assets/images/other/pokercard/hpoker-d_2.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed3 {
  background: url(../assets/images/other/pokercard/hpoker-d_3.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed4 {
  background: url(../assets/images/other/pokercard/hpoker-d_4.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed5 {
  background: url(../assets/images/other/pokercard/hpoker-d_5.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed6 {
  background: url(../assets/images/other/pokercard/hpoker-d_6.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed7 {
  background: url(../assets/images/other/pokercard/hpoker-d_7.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed8 {
  background: url(../assets/images/other/pokercard/hpoker-d_8.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed9 {
  background: url(../assets/images/other/pokercard/hpoker-d_9.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed10 {
  background: url(../assets/images/other/pokercard/hpoker-d_10.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed11 {
  background: url(../assets/images/other/pokercard/hpoker-d_11.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed12 {
  background: url(../assets/images/other/pokercard/hpoker-d_12.svg) no-repeat 0 0;
}

.hcardpicmiddle.typed13 {
  background: url(../assets/images/other/pokercard/hpoker-d_13.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh1 {
  background: url(../assets/images/other/pokercard/hpoker-h_1.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh2 {
  background: url(../assets/images/other/pokercard/hpoker-h_2.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh3 {
  background: url(../assets/images/other/pokercard/hpoker-h_3.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh4 {
  background: url(../assets/images/other/pokercard/hpoker-h_4.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh5 {
  background: url(../assets/images/other/pokercard/hpoker-h_5.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh6 {
  background: url(../assets/images/other/pokercard/hpoker-h_6.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh7 {
  background: url(../assets/images/other/pokercard/hpoker-h_7.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh8 {
  background: url(../assets/images/other/pokercard/hpoker-h_8.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh9 {
  background: url(../assets/images/other/pokercard/hpoker-h_9.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh10 {
  background: url(../assets/images/other/pokercard/hpoker-h_10.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh11 {
  background: url(../assets/images/other/pokercard/hpoker-h_11.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh12 {
  background: url(../assets/images/other/pokercard/hpoker-h_12.svg) no-repeat 0 0;
}

.hcardpicmiddle.typeh13 {
  background: url(../assets/images/other/pokercard/hpoker-h_13.svg) no-repeat 0 0;
}

.hcardpicmiddle.types1 {
  background: url(../assets/images/other/pokercard/hpoker-s_1.svg) no-repeat 0 0;
}

.hcardpicmiddle.types2 {
  background: url(../assets/images/other/pokercard/hpoker-s_2.svg) no-repeat 0 0;
}

.hcardpicmiddle.types3 {
  background: url(../assets/images/other/pokercard/hpoker-s_3.svg) no-repeat 0 0;
}

.hcardpicmiddle.types4 {
  background: url(../assets/images/other/pokercard/hpoker-s_4.svg) no-repeat 0 0;
}

.hcardpicmiddle.types5 {
  background: url(../assets/images/other/pokercard/hpoker-s_5.svg) no-repeat 0 0;
}

.hcardpicmiddle.types6 {
  background: url(../assets/images/other/pokercard/hpoker-s_6.svg) no-repeat 0 0;
}

.hcardpicmiddle.types7 {
  background: url(../assets/images/other/pokercard/hpoker-s_7.svg) no-repeat 0 0;
}

.hcardpicmiddle.types8 {
  background: url(../assets/images/other/pokercard/hpoker-s_8.svg) no-repeat 0 0;
}

.hcardpicmiddle.types9 {
  background: url(../assets/images/other/pokercard/hpoker-s_9.svg) no-repeat 0 0;
}

.hcardpicmiddle.types10 {
  background: url(../assets/images/other/pokercard/hpoker-s_10.svg) no-repeat 0 0;
}

.hcardpicmiddle.types11 {
  background: url(../assets/images/other/pokercard/hpoker-s_11.svg) no-repeat 0 0;
}

.hcardpicmiddle.types12 {
  background: url(../assets/images/other/pokercard/hpoker-s_12.svg) no-repeat 0 0;
}

.hcardpicmiddle.types13 {
  background: url(../assets/images/other/pokercard/hpoker-s_13.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec1 {
  background: url(../assets/images/other/pokercard/vpoker-c_1.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec2 {
  background: url(../assets/images/other/pokercard/vpoker-c_2.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec3 {
  background: url(../assets/images/other/pokercard/vpoker-c_3.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec4 {
  background: url(../assets/images/other/pokercard/vpoker-c_4.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec5 {
  background: url(../assets/images/other/pokercard/vpoker-c_5.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec6 {
  background: url(../assets/images/other/pokercard/vpoker-c_6.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec7 {
  background: url(../assets/images/other/pokercard/vpoker-c_7.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec8 {
  background: url(../assets/images/other/pokercard/vpoker-c_8.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec9 {
  background: url(../assets/images/other/pokercard/vpoker-c_9.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec10 {
  background: url(../assets/images/other/pokercard/vpoker-c_10.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec11 {
  background: url(../assets/images/other/pokercard/vpoker-c_11.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec12 {
  background: url(../assets/images/other/pokercard/vpoker-c_12.svg) no-repeat 0 0;
}

.vcardpicmiddle.typec13 {
  background: url(../assets/images/other/pokercard/vpoker-c_13.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed1 {
  background: url(../assets/images/other/pokercard/vpoker-d_1.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed2 {
  background: url(../assets/images/other/pokercard/vpoker-d_2.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed3 {
  background: url(../assets/images/other/pokercard/vpoker-d_3.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed4 {
  background: url(../assets/images/other/pokercard/vpoker-d_4.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed5 {
  background: url(../assets/images/other/pokercard/vpoker-d_5.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed6 {
  background: url(../assets/images/other/pokercard/vpoker-d_6.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed7 {
  background: url(../assets/images/other/pokercard/vpoker-d_7.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed8 {
  background: url(../assets/images/other/pokercard/vpoker-d_8.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed9 {
  background: url(../assets/images/other/pokercard/vpoker-d_9.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed10 {
  background: url(../assets/images/other/pokercard/vpoker-d_10.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed11 {
  background: url(../assets/images/other/pokercard/vpoker-d_11.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed12 {
  background: url(../assets/images/other/pokercard/vpoker-d_12.svg) no-repeat 0 0;
}

.vcardpicmiddle.typed13 {
  background: url(../assets/images/other/pokercard/vpoker-d_13.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh1 {
  background: url(../assets/images/other/pokercard/vpoker-h_1.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh2 {
  background: url(../assets/images/other/pokercard/vpoker-h_2.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh3 {
  background: url(../assets/images/other/pokercard/vpoker-h_3.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh4 {
  background: url(../assets/images/other/pokercard/vpoker-h_4.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh5 {
  background: url(../assets/images/other/pokercard/vpoker-h_5.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh6 {
  background: url(../assets/images/other/pokercard/vpoker-h_6.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh7 {
  background: url(../assets/images/other/pokercard/vpoker-h_7.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh8 {
  background: url(../assets/images/other/pokercard/vpoker-h_8.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh9 {
  background: url(../assets/images/other/pokercard/vpoker-h_9.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh10 {
  background: url(../assets/images/other/pokercard/vpoker-h_10.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh11 {
  background: url(../assets/images/other/pokercard/vpoker-h_11.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh12 {
  background: url(../assets/images/other/pokercard/vpoker-h_12.svg) no-repeat 0 0;
}

.vcardpicmiddle.typeh13 {
  background: url(../assets/images/other/pokercard/vpoker-h_13.svg) no-repeat 0 0;
}

.vcardpicmiddle.types1 {
  background: url(../assets/images/other/pokercard/vpoker-s_1.svg) no-repeat 0 0;
}

.vcardpicmiddle.types2 {
  background: url(../assets/images/other/pokercard/vpoker-s_2.svg) no-repeat 0 0;
}

.vcardpicmiddle.types3 {
  background: url(../assets/images/other/pokercard/vpoker-s_3.svg) no-repeat 0 0;
}

.vcardpicmiddle.types4 {
  background: url(../assets/images/other/pokercard/vpoker-s_4.svg) no-repeat 0 0;
}

.vcardpicmiddle.types5 {
  background: url(../assets/images/other/pokercard/vpoker-s_5.svg) no-repeat 0 0;
}

.vcardpicmiddle.types6 {
  background: url(../assets/images/other/pokercard/vpoker-s_6.svg) no-repeat 0 0;
}

.vcardpicmiddle.types7 {
  background: url(../assets/images/other/pokercard/vpoker-s_7.svg) no-repeat 0 0;
}

.vcardpicmiddle.types8 {
  background: url(../assets/images/other/pokercard/vpoker-s_8.svg) no-repeat 0 0;
}

.vcardpicmiddle.types9 {
  background: url(../assets/images/other/pokercard/vpoker-s_9.svg) no-repeat 0 0;
}

.vcardpicmiddle.types10 {
  background: url(../assets/images/other/pokercard/vpoker-s_10.svg) no-repeat 0 0;
}

.vcardpicmiddle.types11 {
  background: url(../assets/images/other/pokercard/vpoker-s_11.svg) no-repeat 0 0;
}

.vcardpicmiddle.types12 {
  background: url(../assets/images/other/pokercard/vpoker-s_12.svg) no-repeat 0 0;
}

.vcardpicmiddle.types13 {
  background: url(../assets/images/other/pokercard/vpoker-s_13.svg) no-repeat 0 0;
}

.ex-table-container {
  padding: 10px;
  white-space: pre-wrap;
}

.ex-table {
  width: 100%;
  color: #000000;
  border-collapse: separate;
  border-spacing: 1px;
  background-color: #dcdfe6;
  /* bcbcbc*/
  margin-top: -1px;
}

.ex-table td {
  padding: 0px;
  text-align: center;
  background-color: #ffffff;
}

.ex-table-container .tal {
  text-align: left !important;
}

.ex-table-container .tac {
  text-align: center !important;
}

.ex-table-container .pl {
  padding-left: 10px;
  text-align: left !important;
}

.el-scrollbar .el-scrollbar__bar {
  opacity: 1 !important;
}