<template>
  <div v-if="device === 'mobile'" class="app-container mobile dealer">
    <mobile-header :title="lang.SPD + '*'" @back="handleBack" @click.native="handleLang" />
    <div class="card-box">
      <div v-if="visibleTags.cards" class="ex-table-container">
        <table class="ex-table">
          <tr class="card-name">
            <td>A</td>
            <td>2</td>
            <td>3</td>
            <td>4</td>
            <td>5</td>
            <td>6</td>
            <td>7</td>
            <td>8</td>
            <td>9</td>
            <td>10</td>
            <td>J</td>
            <td>Q</td>
            <td>K</td>
          </tr>
          <tr>
            <td :class="getRemainCardsClass(remainCards.card1)">{{ remainCards.card1 }}</td>
            <td :class="getRemainCardsClass(remainCards.card2)">{{ remainCards.card2 }}</td>
            <td :class="getRemainCardsClass(remainCards.card3)">{{ remainCards.card3 }}</td>
            <td :class="getRemainCardsClass(remainCards.card4)">{{ remainCards.card4 }}</td>
            <td :class="getRemainCardsClass(remainCards.card5)">{{ remainCards.card5 }}</td>
            <td :class="getRemainCardsClass(remainCards.card6)">{{ remainCards.card6 }}</td>
            <td :class="getRemainCardsClass(remainCards.card7)">{{ remainCards.card7 }}</td>
            <td :class="getRemainCardsClass(remainCards.card8)">{{ remainCards.card8 }}</td>
            <td :class="getRemainCardsClass(remainCards.card9)">{{ remainCards.card9 }}</td>
            <td :class="getRemainCardsClass(remainCards.card10)">{{ remainCards.card10 }}</td>
            <td :class="getRemainCardsClass(remainCards.card11)">{{ remainCards.card11 }}</td>
            <td :class="getRemainCardsClass(remainCards.card12)">{{ remainCards.card12 }}</td>
            <td :class="getRemainCardsClass(remainCards.card13)">{{ remainCards.card13 }}</td>
          </tr>
        </table>
      </div>
      <el-form ref="postForm" label-width="80px" :model="gameInfo">
        <el-row>
          <el-col :span=12>
            <el-form-item :label="lang.BJ">
              <el-input v-model="gameInfo.ruleValue13" @change="handleRuleValue" :disabled="visibleTags.seed" />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.FS">
              <el-input v-model="gameInfo.ruleValue14" @change="handleRuleValue" :disabled="visibleTags.seed" />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.JMJE + '*'" @click.native="handleSeed">
              <el-input v-model="gameInfo.ruleValue15" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.LJYL">
              <el-input v-model="gameInfo.ruleValue16" @change="handleRuleValue" :disabled="visibleTags.seed" />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.TH + '*'" @click.native="handleCards">
              <el-select v-model="gameInfo.tableId" filterable placeholder=" " class="input" @change="handleTableId"
                :disabled="visibleTags.verify">
                <el-option v-for="item in tableList" :key="item.id" :label="item.tableNo" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.YZMC + '*'" @click.native="handleVerifyName">
              <el-select v-model="gameInfo.verifyId" filterable placeholder=" " class="input"
                @change="handleChangeVerify" :disabled="visibleTags.verify">
                <el-option v-for="item in verifyList" :key="item.id" :label="item.verifyName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.MXYP">
              <el-select v-model="gameInfo.cardsQty" filterable clearable placeholder=" " @change="handleCardsQty"
                class="input" :disabled="visibleTags.verify">
                <el-option v-for="item in cardsQtyEnum" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item label="" label-width="20px">
              <el-radio-group v-model="gameInfo.isFree" @input="handleIsFree">
                <el-radio :label="0">
                  {{ lang.CY }}
                </el-radio>
                <el-radio :label="1">
                  {{ lang.MY }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="false">
          <el-col :span=12>
            <div class="player-text" style="font-size: 1.2rem !important;">{{ lang.P }} <template
                v-if="gameInfo.playerA && gameInfo.playerB"> ({{ gameInfo.playerPoint }}) </template>
            </div>
          </el-col>
          <el-col :span=12>
            <div class="banker-text" style="font-size: 1.2rem !important;">{{ lang.B }} <template
                v-if="gameInfo.bankerA && gameInfo.bankerB"> ({{ gameInfo.bankerPoint }}) </template></div>
          </el-col>
        </el-row>
        <div class="span-title mb-5" @click="handleInputType">{{ lang.FPJG }}*</div>
        <el-row v-if="gameInfo.inputType === 1" class="mt-5">
          <el-col>
            <div class="player-text" style="font-size: 1.2rem !important;">{{ lang.P }} <template
                v-if="gameInfo.playerA && gameInfo.playerB"> ({{ gameInfo.playerPoint }}) </template>
            </div>
          </el-col>
        </el-row>
        <el-row v-if="gameInfo.inputType === 1">
          <el-col class="mt-5">
            <el-checkbox-group v-model="gameInfo.playerACards" size="mini" @change="handlePlayerCards('playerACard')">
              <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                {{ item.label }}</el-checkbox-button>
            </el-checkbox-group>
          </el-col>
          <el-col class="mt-5">
            <el-checkbox-group v-model="gameInfo.playerBCards" size="mini" @change="handlePlayerCards('playerBCard')">
              <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                {{ item.label }}</el-checkbox-button>
            </el-checkbox-group>
          </el-col>
          <el-col class="mt-5">
            <el-checkbox-group v-model="gameInfo.playerCCards" size="mini" @change="handlePlayerCards('playerCCard')">
              <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                {{ item.label }}</el-checkbox-button>
            </el-checkbox-group>
          </el-col>
        </el-row>
        <el-row v-if="gameInfo.inputType === 1" class="mt-5">
          <el-col>
            <div class="banker-text" style="font-size: 1.2rem !important;">{{ lang.B }} <template
                v-if="gameInfo.bankerA && gameInfo.bankerB"> ({{ gameInfo.bankerPoint }}) </template></div>
          </el-col>
        </el-row>
        <el-row v-if="gameInfo.inputType === 1">
          <el-col class="mt-5">
            <el-checkbox-group v-model="gameInfo.bankerACards" size="mini" @change="handlePlayerCards('bankerACard')">
              <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                {{ item.label }}</el-checkbox-button>
            </el-checkbox-group>
          </el-col>
          <el-col class="mt-5">
            <el-checkbox-group v-model="gameInfo.bankerBCards" size="mini" @change="handlePlayerCards('bankerBCard')">
              <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                {{ item.label }}</el-checkbox-button>
            </el-checkbox-group>
          </el-col>
          <el-col class="mt-5">
            <el-checkbox-group v-model="gameInfo.bankerCCards" size="mini" @change="handlePlayerCards('bankerCCard')">
              <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                {{ item.label }}</el-checkbox-button>
            </el-checkbox-group>
          </el-col>
        </el-row>
        <el-row v-if="gameInfo.inputType === 2" type="flex" justify="center" :class="'color-' + gameInfo.bpResult">
          <el-radio-group v-model="gameInfo.bpResult" size="mini" @change="handleBpResult">
            <el-radio-button v-for="item in bpEnums" :key="item.value" :label="item.value" border>
              <div class="w-50">{{ item.label }}</div>
            </el-radio-button>
          </el-radio-group>
        </el-row>
        <el-row v-if="false">
          <el-col :span=12>
            <el-form-item :label="lang.C1">
              <el-row>
                <!--<el-col :span="12">
                  <el-select v-model="gameInfo.playerAColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>-->
                <el-col>
                  <el-select v-model="gameInfo.playerACard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="lang.C2">
              <el-row>
                <!--<el-col :span="12">
                  <el-select v-model="gameInfo.playerBColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>-->
                <el-col>
                  <el-select v-model="gameInfo.playerBCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="lang.C3">
              <el-row>
                <!--<el-col :span="12">
                  <el-select v-model="gameInfo.playerCColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>-->
                <el-col>
                  <el-select v-model="gameInfo.playerCCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.C1">
              <el-row>
                <!--<el-col :span="12">
                  <el-select v-model="gameInfo.bankerAColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>-->
                <el-col>
                  <el-select v-model="gameInfo.bankerACard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="lang.C2">
              <el-row>
                <!--<el-col :span="12">
                  <el-select v-model="gameInfo.bankerBColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>-->
                <el-col>
                  <el-select v-model="gameInfo.bankerBCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="lang.C3">
              <el-row>
                <!--<el-col :span="12">
                  <el-select v-model="gameInfo.bankerCColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>-->
                <el-col>
                  <el-select v-model="gameInfo.bankerCCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="mt-10">
          <el-button type="primary" :loading="loadingTags.save" class="w-150" @click="handleSaveGame">{{ lang.KP
          }}</el-button>
          <el-button @click="handleShuffleGame">{{ lang.XP }}</el-button>
        </el-row>
        <div class="span-title mt-20 mb-5" @click="handleType">{{ lang.XZTS + '*' }}</div>
        <!--<el-form-item label="台号">
          <el-select v-model="gameInfo.tableId" filterable clearable placeholder=" " class="input"
            @change="handleTableId">
            <el-option v-for="item in tableList" :key="item.id" :label="item.tableNo" :value="item.id" />
          </el-select>
        </el-form-item> -->
        <el-form-item :label="lang.XZFX" v-if="visibleTags.type">
          <el-radio-group v-model="gameInfo.type">
            <el-radio :label="1">{{ lang.Z }}</el-radio>
            <el-radio :label="2">{{ lang.F }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-col :span=12>
            <el-form-item :label="lang.JMJE">
              <el-input v-model="gameInfo.baseAmount" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.XZJE">
              <el-input v-model="gameInfo.amount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mt-10">
          <el-col>
            <div v-if="gameInfo.betResult === 'B'" class="banker-text" style="font-size: 1.2rem !important;">
              {{ lang.B }} {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
            <div v-if="gameInfo.betResult === 'P'" class="player-text" style="font-size: 1.2rem !important;">
              {{ lang.P }} {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="span-title mb-5" @click="handleChipAmount">{{ lang.SJXZ }}*</div>
        <el-row>
          <el-col :span=12>
            <el-form-item :label="lang.XZJE">
              <el-input v-model="gameInfo.chipAmount" :disabled="visibleTags.chip" />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.BP" label-width="40px">
              <el-radio-group v-model="gameInfo.chipType">
                <el-radio label="P">{{ lang.P }}</el-radio>
                <el-radio label="B">{{ lang.B }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="mt-10">
          <el-button class="w-150" :loading="loadingTags.bet" @click="handleSaveGameBet" type="primary"
            :disabled="gameInfo.flying === 1 || gameInfo.autoBet === 1">{{
              lang.XZ
            }}</el-button>
          <el-checkbox class="mt-5 ml-10" v-model="gameInfo.flying" :true-label="1" :false-label="0">{{ lang.FP
          }}</el-checkbox>
          <el-checkbox class="mt-5 ml-10" v-model="gameInfo.autoBet" :true-label="1" :false-label="0" disabled>{{
            lang.ZDXZ
          }}</el-checkbox>
        </el-row>
        <div class="span-title mb-5" @click="handleGameList">{{ lang.XZTJ + '*' }}</div>
        <el-row>
          <el-col :span=12>
            <el-form-item :label="lang.BJXZJM" label-width="100px">
              <el-input v-model="gameTransHist.currentBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.BJXZJE" label-width="100px">
              <el-input v-model="gameTransHist.currentAmount" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.LJXZJM" label-width="100px">
              <el-input v-model="gameTransHist.chipBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.LJXZJE" label-width="100px">
              <el-input v-model="gameTransHist.chipAmount" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.LJSYJM" label-width="100px">
              <el-input v-model="gameTransHist.winBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.LJSYJE" label-width="100px">
              <el-input v-model="gameTransHist.winAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="data-box mt-20" v-if="visibleTags.game">
      <el-table ref="multipleTable" :data="objectList" border highlight-current-row>
        <el-table-column type="index" align="center" width="42" />
        <el-table-column prop="tableInfo.tableNo" align="center" :label="lang.TH" width="80">
          <template slot-scope="scope">
            <div> {{ scope.row.tableInfo.tableNo }} </div>
          </template>
        </el-table-column>
        <el-table-column prop="gameMatch.matchNo" align="center" :label="lang.XH" width="80" />
        <el-table-column prop="gameNo" align="center" :label="lang.JH" width="80" />
        <el-table-column prop="gameResult" align="center" :label="lang.PX" width="100">
          <template slot-scope="scope">
            <el-popover trigger="click" placement="right">
              <div class="container">
                <div v-if="scope.row.gameType == 1 || scope.row.gameType == 2" class="mipai-box">
                  <div class="xian-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.playerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.playerB, 1) + ' on'" />
                    <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.playerC, 2) + ' on'" />
                    <div class="total-box on">{{ scope.row.playerPoint }}</div>
                    <div class="title-box on ">
                      {{ $t('game.playerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="zhuang-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.bankerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.bankerB, 1) + ' on'" />
                    <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.bankerC, 2) + ' on'" />
                    <div class="total-box on">{{ scope.row.bankerPoint }}</div>
                    <div class="title-box on">
                      {{ $t('game.bankerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="clear" />
                </div>
              </div>
              <div slot="reference" class="name-wrapper">
                <el-tag size="medium" class="pointer">{{ lang.PX }}</el-tag>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="playerPoint" align="center" :label="lang.P" width="100" />
        <el-table-column prop="bankerPoint" align="center" :label="lang.B" width="100" />
        <el-table-column prop="gameResult" align="center" :label="lang.JG" :formatter="gameResultFormat" width="120" />
        <!--<el-table-column align="center" label="正向" width="140">
          <el-table-column prop="player2B" align="center" label="B/P" width="60" />
          <el-table-column prop="player2PairAmount" align="center" label="基码" width="80">
            <template slot-scope="scope">
              {{ scope.row.player2PairAmount ? scope.row.player2PairAmount : '' }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="反向" width="140">
          <el-table-column prop="player3B" align="center" label="B/P" width="60" />
          <el-table-column prop="player3PairAmount" align="center" label="基码" width="80">
            <template slot-scope="scope">
              {{ scope.row.player3PairAmount ? scope.row.player3PairAmount : '' }}
            </template>
          </el-table-column>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
  <div v-else class="app-container dealer">
    <div class="card-box">
      <div v-if="visibleTags.cards" class="ex-table-container">
        <table class="ex-table">
          <tr class="card-name">
            <td>A</td>
            <td>2</td>
            <td>3</td>
            <td>4</td>
            <td>5</td>
            <td>6</td>
            <td>7</td>
            <td>8</td>
            <td>9</td>
            <td>10</td>
            <td>J</td>
            <td>Q</td>
            <td>K</td>
          </tr>
          <tr>
            <td :class="getRemainCardsClass(remainCards.card1)">{{ remainCards.card1 }}</td>
            <td :class="getRemainCardsClass(remainCards.card2)">{{ remainCards.card2 }}</td>
            <td :class="getRemainCardsClass(remainCards.card3)">{{ remainCards.card3 }}</td>
            <td :class="getRemainCardsClass(remainCards.card4)">{{ remainCards.card4 }}</td>
            <td :class="getRemainCardsClass(remainCards.card5)">{{ remainCards.card5 }}</td>
            <td :class="getRemainCardsClass(remainCards.card6)">{{ remainCards.card6 }}</td>
            <td :class="getRemainCardsClass(remainCards.card7)">{{ remainCards.card7 }}</td>
            <td :class="getRemainCardsClass(remainCards.card8)">{{ remainCards.card8 }}</td>
            <td :class="getRemainCardsClass(remainCards.card9)">{{ remainCards.card9 }}</td>
            <td :class="getRemainCardsClass(remainCards.card10)">{{ remainCards.card10 }}</td>
            <td :class="getRemainCardsClass(remainCards.card11)">{{ remainCards.card11 }}</td>
            <td :class="getRemainCardsClass(remainCards.card12)">{{ remainCards.card12 }}</td>
            <td :class="getRemainCardsClass(remainCards.card13)">{{ remainCards.card13 }}</td>
          </tr>
        </table>
      </div>
      <el-form ref="postForm" label-width="180px" :model="gameInfo">
        <div class="span-title mb-5" @dblclick="handleLang">{{ lang.THSZ + '*' }}</div>
        <el-row>
          <el-col :span=12>
            <el-form-item :label="lang.BJ">
              <el-input v-model="gameInfo.ruleValue13" @change="handleRuleValue" :disabled="visibleTags.seed" />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.FS">
              <el-input v-model="gameInfo.ruleValue14" @change="handleRuleValue" :disabled="visibleTags.seed" />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.JMJE + '*'" @dblclick.native="handleSeed">
              <el-input v-model="gameInfo.ruleValue15" disabled />
            </el-form-item>
          </el-col>
          <el-col :span=12>
            <el-form-item :label="lang.LJYL">
              <el-input v-model="gameInfo.ruleValue16" @change="handleRuleValue" :disabled="visibleTags.seed" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.TH + '*'" @dblclick.native="handleCards">
              <el-select v-model="gameInfo.tableId" filterable placeholder=" " class="input" @change="handleTableId"
                :disabled="visibleTags.verify">
                <el-option v-for="item in tableList" :key="item.id" :label="item.tableNo" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.YZMC + '*'" @dblclick.native="handleVerifyName">
              <el-select v-model="gameInfo.verifyId" filterable placeholder=" " class="input"
                @change="handleChangeVerify" :disabled="visibleTags.verify">
                <el-option v-for="item in verifyList" :key="item.id" :label="item.verifyName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.MXYP">
              <el-select v-model="gameInfo.cardsQty" filterable clearable placeholder=" " @change="handleCardsQty"
                :disabled="visibleTags.verify">
                <el-option v-for="item in cardsQtyEnum" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="">
              <el-radio-group v-model="gameInfo.isFree" @input="handleIsFree">
                <el-radio :label="0">
                  {{ lang.CY }}
                </el-radio>
                <el-radio :label="1">
                  {{ lang.MY }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="span-title mb-5" @dblclick="handleInputType">{{ lang.FPJG }}*</div>
        <el-row v-if="gameInfo.inputType == 1" class="mb-5 mt-5">
          <el-col :span="12">
            <el-form-item>
              <div class="player-text" style="font-size: 1.8rem !important;">
                {{ lang.P }}
                <template v-if="gameInfo.playerA && gameInfo.playerB"> ({{ gameInfo.playerPoint }}) </template>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <div class="banker-text" style="font-size: 1.8rem !important;">
                {{ lang.B }}
                <template v-if="gameInfo.bankerA && gameInfo.bankerB"> ({{ gameInfo.bankerPoint }}) </template>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="gameInfo.inputType == 1">
          <el-col :span="12">
            <el-row type="flex" justify="end">
              <el-checkbox-group v-model="gameInfo.playerACards" size="mini" @change="handlePlayerCards('playerACard')">
                <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                  {{ item.label }}</el-checkbox-button>
              </el-checkbox-group>
            </el-row>
            <el-row type="flex" justify="end" class="mt-5">
              <el-checkbox-group v-model="gameInfo.playerBCards" size="mini" @change="handlePlayerCards('playerBCard')">
                <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                  {{ item.label }}</el-checkbox-button>
              </el-checkbox-group>
            </el-row>
            <el-row type="flex" justify="end" class="mt-5">
              <el-checkbox-group v-model="gameInfo.playerCCards" size="mini" @change="handlePlayerCards('playerCCard')">
                <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                  {{ item.label }}</el-checkbox-button>
              </el-checkbox-group>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-row type="flex" justify="end">
              <el-checkbox-group v-model="gameInfo.bankerACards" size="mini" @change="handlePlayerCards('bankerACard')">
                <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                  {{ item.label }}</el-checkbox-button>
              </el-checkbox-group>
            </el-row>
            <el-row type="flex" justify="end" class="mt-5">
              <el-checkbox-group v-model="gameInfo.bankerBCards" size="mini" @change="handlePlayerCards('bankerBCard')">
                <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                  {{ item.label }}</el-checkbox-button>
              </el-checkbox-group>
            </el-row>
            <el-row type="flex" justify="end" class="mt-5">
              <el-checkbox-group v-model="gameInfo.bankerCCards" size="mini" @change="handlePlayerCards('bankerCCard')">
                <el-checkbox-button v-for="item in cardsEnums" :key="item.value" :label="item.value" border>
                  {{ item.label }}</el-checkbox-button>
              </el-checkbox-group>
            </el-row>
          </el-col>
        </el-row>
        <el-row v-if="gameInfo.inputType === 2" type="flex" justify="center" :class="'color-' + gameInfo.bpResult">
          <el-radio-group v-model="gameInfo.bpResult" size="mini" @change="handleBpResult">
            <el-radio-button v-for="item in bpEnums" :key="item.value" :label="item.value" border>
              <div class="w-80">{{ item.label }}</div>
            </el-radio-button>
          </el-radio-group>
        </el-row>
        <!--<el-row>
          <el-col :span="12">
            <el-form-item :label="lang.C1">
              <el-row>
                <el-col :span="12" v-if="false">
                  <el-select v-model="gameInfo.playerAColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="gameInfo.playerACard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.C1">
              <el-row>
                <el-col :span="12" v-if="false">
                  <el-select v-model="gameInfo.bankerAColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="gameInfo.bankerACard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="lang.C2">
              <el-row>
                <el-col :span="12" v-if="false">
                  <el-select v-model="gameInfo.playerBColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="gameInfo.playerBCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.C2">
              <el-row>
                <el-col :span="12" v-if="false">
                  <el-select v-model="gameInfo.bankerBColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="gameInfo.bankerBCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="lang.C3">
              <el-row>
                <el-col :span="12" v-if="false">
                  <el-select v-model="gameInfo.playerCColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="gameInfo.playerCCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.C3">
              <el-row>
                <el-col :span="12" v-if="false">
                  <el-select v-model="gameInfo.bankerCColor" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in colorEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="gameInfo.bankerCCard" filterable clearable placeholder=" " class="input">
                    <el-option v-for="item in cardsEnums" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row type="flex" justify="center" class="mt-10">
          <el-button type="primary" class="w-20p" :loading="loadingTags.save" @click="handleSaveGame">{{ lang.KP
          }}</el-button>
          <el-button @click="handleShuffleGame">{{ lang.XP }}</el-button>
        </el-row>
        <!-- 下注端 -->
        <div class="span-title mb-5" @dblclick="handleType">{{ lang.XZTS + '*' }}</div>
        <el-row>
          <!--<el-col :span="12">
            <el-form-item label="台号">
              <el-select v-model="gameInfo.tableId" filterable clearable placeholder=" " class="input"
                @change="handleTableId">
                <el-option v-for="item in tableList" :key="item.id" :label="item.tableNo" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12" v-if="visibleTags.type">
            <el-form-item :label="lang.XZFX">
              <el-radio-group v-model="gameInfo.type">
                <el-radio :label="1">{{ lang.Z }}</el-radio>
                <el-radio :label="2">{{ lang.F }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mb-10">
          <el-col :span="12">
            <el-form-item :label="lang.JMJE">
              <el-input v-model="gameInfo.baseAmount" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.XZJE">
              <el-input v-model="gameInfo.amount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mb-10">
          <el-col>
            <div v-if="gameInfo.betResult === 'B'" class="banker-text" style="font-size: 1.8rem !important;">
              {{ lang.B }} {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
            <div v-if="gameInfo.betResult === 'P'" class="player-text" style="font-size: 1.8rem !important;">
              {{ lang.P }} {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="span-title mb-5" @dblclick="handleChipAmount">{{ lang.SJXZ }}*</div>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="lang.XZJE">
              <el-input v-model="gameInfo.chipAmount" :disabled="visibleTags.chip" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.BP">
              <el-radio-group v-model="gameInfo.chipType">
                <el-radio label="P">{{ lang.P }}</el-radio>
                <el-radio label="B">{{ lang.B }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="mt-10">
          <el-button class="w-20p" :loading="loadingTags.bet" @click="handleSaveGameBet" type="primary"
            :disabled="gameInfo.flying === 1 || gameInfo.autoBet === 1">{{
              lang.XZ }}</el-button>
          <el-checkbox class="mt-5 ml-10" v-model="gameInfo.flying" :true-label="1" :false-label="0">{{ lang.FP
          }}</el-checkbox>
          <el-checkbox class="mt-5 ml-10" v-model="gameInfo.autoBet" :true-label="1" :false-label="0" disabled>{{
            lang.ZDXZ
          }}</el-checkbox>
        </el-row>
        <div class="span-title mb-5" @dblclick="handleGameList">{{ lang.XZTJ + '*' }}</div>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="lang.BJXZJM">
              <el-input v-model="gameTransHist.currentBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.BJXZJE">
              <el-input v-model="gameTransHist.currentAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="lang.LJXZJM">
              <el-input v-model="gameTransHist.chipBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.LJXZJE">
              <el-input v-model="gameTransHist.chipAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="lang.LJSYJM">
              <el-input v-model="gameTransHist.winBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="lang.LJSYJE">
              <el-input v-model="gameTransHist.winAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="data-box mt-20" v-if="visibleTags.game">
      <div class="span-title mb-5">{{ lang.MJJL }}</div>
      <el-table ref="multipleTable" :data="objectList" border highlight-current-row>
        <el-table-column type="index" align="center" width="42" />
        <el-table-column prop="tableInfo.tableNo" align="center" :label="lang.TH" width="80">
          <template slot-scope="scope">
            <div> {{ scope.row.tableInfo.tableNo }} </div>
          </template>
        </el-table-column>
        <el-table-column prop="gameMatch.matchNo" align="center" :label="lang.XH" width="80" />
        <el-table-column prop="gameNo" align="center" :label="lang.JH" width="80" />
        <el-table-column prop="gameResult" align="center" :label="lang.PX" width="100">
          <template slot-scope="scope">
            <el-popover trigger="click" placement="right">
              <div class="container">
                <div v-if="scope.row.gameType == 1 || scope.row.gameType == 2" class="mipai-box">
                  <div class="xian-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.playerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.playerB, 1) + ' on'" />
                    <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.playerC, 2) + ' on'" />
                    <div class="total-box on">{{ scope.row.playerPoint }}</div>
                    <div class="title-box on ">
                      {{ $t('game.playerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="zhuang-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.bankerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.bankerB, 1) + ' on'" />
                    <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.bankerC, 2) + ' on'" />
                    <div class="total-box on">{{ scope.row.bankerPoint }}</div>
                    <div class="title-box on">
                      {{ $t('game.bankerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="clear" />
                </div>
              </div>
              <div slot="reference" class="name-wrapper">
                <el-tag size="medium" class="pointer">{{ lang.PX }}</el-tag>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="playerPoint" align="center" :label="lang.P" width="100" />
        <el-table-column prop="bankerPoint" align="center" :label="lang.B" width="100" />
        <el-table-column prop="gameResult" align="center" :label="lang.JG" :formatter="gameResultFormat" width="120" />
        <!--<el-table-column align="center" label="正向" width="140">
          <el-table-column prop="player2B" align="center" label="B/P" width="60" />
          <el-table-column prop="player2PairAmount" align="center" label="基码" width="80">
            <template slot-scope="scope">
              {{ scope.row.player2PairAmount ? scope.row.player2PairAmount : '' }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" label="反向" width="140">
          <el-table-column prop="player3B" align="center" label="B/P" width="60" />
          <el-table-column prop="player3PairAmount" align="center" label="基码" width="80">
            <template slot-scope="scope">
              {{ scope.row.player3PairAmount ? scope.row.player3PairAmount : '' }}
            </template>
          </el-table-column>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
</template>

<script>
  import { formatNumeric } from '@/utils/formatter'
  import { mapGetters } from 'vuex'
  import { listTableInfo, updateTableInfo, getTableInfo } from '@/api/setting/tableInfo'
  import { chipGame, listGameResult } from '@/api/game/game'
  import { updateGameMatch } from '@/api/game/gameMatch'
  import { gameResultFilter } from '@/filters/gameResult'
  import { getGameTypeEnum } from '@/enums/setting.js'
  import { list2, update } from '@/api/game/gameVerify'
  import { updateMember } from '@/api/account/member'
  import { addGameTransHist, listGameTransHist } from '@/api/game/gameTransHist'
  import moment from 'moment'
  export default {
    name: 'BetDealer',
    data() {
      return {
        socketInfo: null,
        objectList: [],
        tableList: [],
        verifyList: [],
        tableInfo: { id: null, tableNo: '' },
        matchInfo: { id: null, matchNo: '' },
        gameInfo: {
          id: null, gameNo: '', gameType: 1, gameCategory: 1, tableId: null, matchId: null, verifyId: null, isFree: 0, cardsQty: 8,
          playerA: '', playerB: '', playerC: '', bankerA: '', bankerB: '', bankerC: '',
          bankerPoint: 0, playerPoint: 0, gameResult: '',
          playerACard: '', playerBCard: '', playerCCard: '', bankerACard: '', bankerBCard: '', bankerCCard: '',
          playerAColor: 1, playerBColor: 1, playerCColor: 1, bankerAColor: 1, bankerBColor: 1, bankerCColor: 1,
          roadBill: '', allGamePoint: '', status: 0,
          type: 1, baseAmount: 50, amount: null, betAmount: null, betResult: null, chipType: null, chipAmount: null,
          playerACards: [], playerBCards: [], playerCCards: [], bankerACards: [], bankerBCards: [], bankerCCards: [],
          flying: 0,
          ruleValue13: 100, ruleValue14: 100, ruleValue15: 1, ruleValue16: '',
          profitResult: '', autoBet: 1,
          bpResult: 0, inputType: 1
        },
        colorEnums: [{ label: '黑桃', value: 1 }, { label: '红桃', value: 2 }, { label: '梅花', value: 4 }, { label: '方块', value: 3 }],
        cardsEnums: [{ label: 'A', value: 1 }, { label: '2', value: 2 }, { label: '3', value: 3 }, { label: '4', value: 4 },
        { label: '5', value: 5 }, { label: '6', value: 6 }, { label: '7', value: 7 }, { label: '8', value: 8 },
        { label: '9', value: 9 }, { label: '10', value: 10 }, { label: '11', value: 11 }, { label: '12', value: 12 },
        { label: '13', value: 13 }],
        cardsQtyEnum: [{ label: '8', value: 8 }, { label: '10', value: 10 }],
        remainCards: { card1: 0, card2: 0, card3: 0, card4: 0, card5: 0, card6: 0, card7: 0, card8: 0, card9: 0, card10: 0, card11: 0, card12: 0, card13: 0 },
        gameTransHist: { currentBase: 0, currentAmount: 0, winAmount: 0, winBase: 0, chipAmount: 0, chipBase: 0 },
        visibleTags: { cards: false, type: false, lang: true, game: false, chip: true, verify: true, seed: false },
        lang: {},
        gameTransHistList: [],
        loadingTags: { bet: false, save: false },
        gameVerifyInfo: {},
        bpEnums: [{ label: '左', value: 2 }, { label: '中', value: 3 }, { label: '右', value: 1 }],
      }
    },
    computed: {
      socketInfoOrginal: {
        get() {
          return this.$store.state.app.socketInfo
        }
      },
      ...mapGetters(['member', 'userName', 'userType', 'device'])
    },
    watch: {
      'gameInfo.bankerACard': 'getAllCardPoint',
      'gameInfo.bankerBCard': 'getAllCardPoint',
      'gameInfo.bankerCCard': 'getAllCardPoint',
      'gameInfo.playerACard': 'getAllCardPoint',
      'gameInfo.playerBCard': 'getAllCardPoint',
      'gameInfo.playerCCard': 'getAllCardPoint',
      'gameInfo.bankerAColor': 'getAllCardPoint',
      'gameInfo.bankerBColor': 'getAllCardPoint',
      'gameInfo.bankerCColor': 'getAllCardPoint',
      'gameInfo.playerAColor': 'getAllCardPoint',
      'gameInfo.playerBColor': 'getAllCardPoint',
      'gameInfo.playerCColor': 'getAllCardPoint',
      socketInfoOrginal(val) {
        if (val) {
          this.socketInfo = val
          this.handleSocketInfo()
        }
      }
    },
    created() {
      this.handleLang()
      this.getTableList()
      this.getTable()
    },
    methods: {
      handleInputType() {
        if (this.gameInfo.inputType === 1) {
          this.gameInfo.inputType = 2
        } else {
          this.gameInfo.inputType = 1
        }
      },
      handleVerifyName() {
        this.visibleTags.verify = !this.visibleTags.verify
      },
      handleSeed() {
        this.visibleTags.seed = !this.visibleTags.seed
      },
      handleRuleValue() {
        this.gameInfo.ruleValue15 = ''
        if (this.gameInfo.ruleValue13 && this.gameInfo.ruleValue14) {
          this.gameInfo.ruleValue15 = parseFloat(this.gameInfo.ruleValue13) / parseFloat(this.gameInfo.ruleValue14)
          this.gameInfo.ruleValue15 = formatNumeric(this.gameInfo.ruleValue15)
          if (this.gameInfo.ruleValue15) {
            this.updateVerify()
          }
        }
        this.gameInfo.baseAmount = this.gameInfo.ruleValue15
      },
      handleChipAmount() {
        this.visibleTags.chip = !this.visibleTags.chip
      },
      handleGameList() {
        this.visibleTags.game = !this.visibleTags.game
      },
      handleBpResult() {
        if (this.gameInfo.bpResult === 1) {
          this.gameInfo.bankerACard = '1'
          this.gameInfo.bankerBCard = '8'
          this.gameInfo.playerACard = '2'
          this.gameInfo.playerBCard = '1'

        } else if (this.gameInfo.bpResult === 2) {
          this.gameInfo.bankerACard = '2'
          this.gameInfo.bankerBCard = '1'
          this.gameInfo.playerACard = '8'
          this.gameInfo.playerBCard = '1'
        } else if (this.gameInfo.bpResult === 3) {
          this.gameInfo.bankerACard = '5'
          this.gameInfo.bankerBCard = '1'
          this.gameInfo.playerACard = '5'
          this.gameInfo.playerBCard = '1'
        }
      },
      handlePlayerCards(c) {
        this.gameInfo[c] = ''
        if (this.gameInfo[c + 's'].length > 1) {
          this.gameInfo[c + 's'].shift()
          // this.$message.error('最多不能超过3张')
        }
        if (this.gameInfo[c + 's'].length)
          this.gameInfo[c] = this.gameInfo[c + 's'][0]

        console.log(this.gameInfo[c + 's'])
        console.log('gameInfo.' + c + '.....' + this.gameInfo[c])
        console.log('this.gameInfo...................', this.gameInfo)
      },
      handleBankerCards() {
        if (this.bankerCards.length > 3) {
          this.bankerCards.splice(this.bankerCards.length - 1, 1)
          // this.$message.error('最多不能超过3张')
        }
        this.gameInfo.bankerACard = ''
        this.gameInfo.bankerBCard = ''
        this.gameInfo.bankerCCard = ''
        for (var i = 0; i < this.bankerCards.length; i++) {
          if (i === 0) {
            this.gameInfo.bankerACard = this.bankerCards[i]
          } else if (i === 1) {
            this.gameInfo.bankerBCard = this.bankerCards[i]
          } else if (i === 2) {
            this.gameInfo.bankerCCard = this.bankerCards[i]
          }
        }
        console.log(this.bankerCards)
        console.log('this.gameInfo...................', this.gameInfo)
      },
      handleLang() {
        this.visibleTags.lang = !this.visibleTags.lang
        if (this.visibleTags.lang) {
          this.lang = {
            SPD: '算牌端',
            THSZ: '台号设置',
            TH: '台号', YZMC: '验证名称',
            CY: '抽佣',
            MY: '免佣', MXYP: '每靴用牌',
            FPJG: '发牌结果',
            C1: '第一张', C2: '第二张', C3: '第三张',
            P: '左', B: '右', XZFX: '下注方向',
            KP: '开牌', XP: '洗牌',
            XZTS: '下注提示',
            JMJE: '基码金额', XZJE: '下注金额',
            SJXZ: '实际下注',
            XZJE: '下注金额', BP: '左/右',
            XZ: '下注',
            XZTJ: '下注统计',
            BJXZJM: '本局下注基码', BJXZJE: '本局下注金额',
            LJXZJM: '累计下注基码', LJXZJE: '累计下注金额',
            LJSYJM: '累计输赢基码', LJSYJE: '累计输赢金额',
            MJJL: '每局记录',
            XH: '靴号', JH: '局号', PX: '牌型', JG: '结果',
            Z: '主', F: '副',
            FP: '飞牌',
            BJ: '本金', FS: '份数', LJYL: '累计盈利',
            ZDXZ: '自动下注'
          }
        } else {
          this.lang = {
            SPD: 'SPD',
            THSZ: 'THSZ',
            TH: 'TH', YZMC: 'YZMC',
            CY: 'CY',
            MY: 'MY', MXYP: 'MXYP',
            FPJG: 'FPJG',
            C1: '1', C2: '2', C3: '3',
            P: '左', B: '右', XZFX: 'XZFX',
            KP: 'KP', XP: 'XP',
            XZTS: 'XZTS',
            JMJE: 'JMJE', XZJE: 'XZJE',
            SJXZ: 'SJXZ',
            XZJE: 'XZJE', BP: '左/右',
            XZ: 'XZ',
            XZTJ: 'XZTJ',
            BJXZJM: 'BJXZJM', BJXZJE: 'BJXZJE',
            LJXZJM: 'LJXZJM', LJXZJE: 'LJXZJE',
            LJSYJM: 'LJSYJM', LJSYJE: 'LJSYJE',
            MJJL: 'MJJL',
            XH: 'XH', JH: 'JH', PX: 'PX', JG: 'JG',
            Z: 'Z', F: 'F',
            FP: 'FP',
            BJ: 'BJ', FS: 'FS', LJYL: 'LJYL',
            ZDXZ: 'ZDXZ'
          }
        }
      },
      getRemainCardsClass(qty) {
        if (qty === this.remainCards.card0) {
          return 'remain-all'
        } else if (qty === 0) {
          return 'remain-zero'
        } else {
          return 'remain-part'
        }
      },
      handleRemainCards() {
        if (!this.gameInfo.cardsQty) {
          this.gameInfo.cardsQty = 8
        }
        var cardsQty = parseInt(this.gameInfo.cardsQty) * 4
        this.remainCards.card0 = cardsQty
        for (var i = 1; i <= 13; i++) {
          this.remainCards['card' + i] = cardsQty
        }
        var value = 1
        for (var item of this.objectList) {
          this.getCardValue(item.playerA)
          this.getCardValue(item.playerB)
          this.getCardValue(item.playerC)
          this.getCardValue(item.bankerA)
          this.getCardValue(item.bankerB)
          this.getCardValue(item.bankerC)
        }
      },
      getCardValue(card) {
        if (!card) {
          return ''
        }
        var cardPoint = Number(card.substr(2, 2))
        var point = cardPoint % 13
        this.remainCards['card' + point] = this.remainCards['card' + point] - 1
      },
      handleBack() {
        this.$router.push({ path: '/dashboard/index' })
      },
      resetGameInfo() {
        this.gameInfo.playerA = ''
        this.gameInfo.playerB = ''
        this.gameInfo.playerC = ''
        this.gameInfo.bankerA = ''
        this.gameInfo.bankerB = ''
        this.gameInfo.bankerC = ''
        this.gameInfo.bankerPoint = 0
        this.gameInfo.playerPoint = 0
        this.gameInfo.gameResult = ''
        this.gameInfo.playerACard = ''
        this.gameInfo.playerBCard = ''
        this.gameInfo.playerCCard = ''
        this.gameInfo.bankerACard = ''
        this.gameInfo.bankerBCard = ''
        this.gameInfo.bankerCCard = ''
        this.gameInfo.playerAColor = 1
        this.gameInfo.playerBColor = 1
        this.gameInfo.playerCColor = 1
        this.gameInfo.bankerAColor = 1
        this.gameInfo.bankerBColor = 1
        this.gameInfo.bankerCColor = 1
        this.gameInfo.roadBill = ''
        this.gameInfo.allGamePoint = ''
        this.gameInfo.playerACards = []
        this.gameInfo.playerBCards = []
        this.gameInfo.playerCCards = []
        this.gameInfo.bankerACards = []
        this.gameInfo.bankerBCards = []
        this.gameInfo.bankerCCards = []
        this.gameInfo.bpResult = 0
      },
      handleSaveGame() {
        if (this.loadingTags.bet) {
          this.$message.error('下注中，请稍后')
          return
        }
        if (!this.gameInfo.tableId) {
          this.$message.error('请选择台号')
          return
        }
        if (!this.gameInfo.verifyId) {
          this.$message.error('请选择验证名称')
          return
        }
        this.gameInfo.bankerA = this.getCard('bankerA')
        this.gameInfo.bankerB = this.getCard('bankerB')
        this.gameInfo.bankerC = this.getCard('bankerC')

        this.gameInfo.playerA = this.getCard('playerA')
        this.gameInfo.playerB = this.getCard('playerB')
        this.gameInfo.playerC = this.getCard('playerC')
        console.log('handleSaveGame..................1')
        console.log(this.gameInfo)
        var result = this.checkCard()
        if (!result) {
          this.$message.error('牌型错误')
          return
        }
        this.getWinResult()
        this.getGameMatch()
        var game = JSON.parse(JSON.stringify(this.gameInfo))
        game.id = null
        game.lastGameResult = this.gameInfo.flying ? '3' : ''
        game.profitResult = null
        var gameMatch = JSON.parse(JSON.stringify(this.matchInfo))
        if (game.matchId) {
          gameMatch.id = game.matchId
        }
        var tableInfo = { id: this.gameInfo.tableId, gameId: this.gameInfo.id, matchId: this.gameInfo.matchId, dealerId: this.gameInfo.verifyId }
        var gameInfo = {}
        gameInfo.game = game
        gameInfo.gameMatch = gameMatch
        gameInfo.tableInfo = tableInfo
        console.log('handleSaveGame.................................')
        console.log(gameInfo)
        this.loadingTags.save = true
        chipGame(gameInfo).then(res => {
          console.log('chipGame...................', res)
          this.loadingTags.save = false
          this.setSocketInfo(res.data.game)
          this.handleAutoBet()
          this.$message.success('操作成功')
          this.resetGameInfo()
          this.getTable()
        }).catch(() => {
          this.loadingTags.save = false
          this.$message.success('操作失败')
        })
      },
      getWinResult() {
        console.log('getWinResult.................................1')
        var win = '3'
        if (this.gameInfo.bankerPoint > this.gameInfo.playerPoint) {
          win = '1'
        } else if (this.gameInfo.bankerPoint < this.gameInfo.playerPoint) {
          win = '2'
        }
        console.log('getWinResult.................................2 win=' + win)
        var pair = '4'
        var bankerA = this.getCardPoint(this.gameInfo.bankerA)
        var bankerB = this.getCardPoint(this.gameInfo.bankerB)
        var playerA = this.getCardPoint(this.gameInfo.playerA)
        var playerB = this.getCardPoint(this.gameInfo.playerB)
        if (bankerA === bankerB && playerA === playerB) {
          pair = '3'
        } else if (playerA === playerB) {
          pair = '2'
        } else if (bankerA === bankerB) {
          pair = '1'
        }
        console.log('getWinResult.................................2 win=' + win)
        this.gameInfo.gameResult = win + '|' + pair
        /* var roadBill = ''
        var allGamePoint = ''
        for (var game of this.objectList) {
          if (!roadBill) {
            allGamePoint = game.bankerPoint + '|' + game.playerPoint + '|0|0'
            roadBill = game.gameResult
          } else {
            roadBill = game.gameResult + ';' + roadBill
            allGamePoint = game.bankerPoint + '|' + game.playerPoint + '|0|0' + ';' + allGamePoint
          }
        }
        console.log('getWinResult.................................3 gameResult=' + this.gameInfo.gameResult)
        roadBill = roadBill ? roadBill + ';' + this.gameInfo.gameResult : this.gameInfo.gameResult
        allGamePoint = allGamePoint ? allGamePoint + ';' + this.gameInfo.bankerPoint + '|' + this.gameInfo.playerPoint + '|0|0' : this.gameInfo.bankerPoint + '|' + this.gameInfo.playerPoint + '|0|0'
        console.log('getWinResult.................................4')
        console.log(roadBill)
        console.log(allGamePoint)
        this.gameInfo.roadBill = roadBill
        this.gameInfo.allGamePoint = allGamePoint */
      },
      getGameMatch() {
        var gameMatch = { id: null, tableId: this.gameInfo.tableId }
        var roadBill = ''
        var allGamePoint = ''
        var lastGameResult = ''
        var bankerWinCount = 0
        var bankerPairWinCount = 0
        var tieWinCount = 0
        var playerWinCount = 0
        var playerPairWinCount = 0
        var tempObjectList = JSON.parse(JSON.stringify(this.objectList))
        tempObjectList.unshift(this.gameInfo)
        for (var game of tempObjectList) {
          if (!roadBill) {
            allGamePoint = game.bankerPoint + '|' + game.playerPoint + '|0|0'
            roadBill = game.gameResult
          } else {
            roadBill = game.gameResult + ';' + roadBill
            allGamePoint = game.bankerPoint + '|' + game.playerPoint + '|0|0' + ';' + allGamePoint
          }
          if (!lastGameResult) {
            lastGameResult = game.gameResult
          }
          var result = game.gameResult.split('|')
          if (result[0] === '1') {
            bankerWinCount++
          } else if (result[0] === '2') {
            playerWinCount++
          } else if (result[0] === '3') {
            tieWinCount++
          }
          if (result[1] === '1' || result[1] === '3') {
            bankerPairWinCount++
          }
          if (result[1] === '2' || result[1] === '3') {
            playerPairWinCount++
          }
        }
        gameMatch.gameCount = this.objectList.length
        gameMatch.gameStatus = 0
        gameMatch.gameResult = lastGameResult
        gameMatch.bankerWinCount = bankerWinCount
        gameMatch.bankerPairWinCount = bankerPairWinCount
        gameMatch.tieWinCount = tieWinCount
        gameMatch.playerWinCount = playerWinCount
        gameMatch.playePairWinCount = playerPairWinCount
        gameMatch.status = 2
        gameMatch.roadBill = roadBill
        gameMatch.allGamePoint = allGamePoint
        gameMatch.allGameResult = ''
        this.matchInfo = gameMatch
      },
      getCard(play) {
        if (!this.gameInfo[play + 'Color'] || !this.gameInfo[play + 'Card']) {
          return ''
        }
        var color = parseInt(this.gameInfo[play + 'Color'])
        var card = parseInt(this.gameInfo[play + 'Card'])
        console.log('getCard................' + color + ' / ' + card + ' / ' + this.gameInfo[play + 'Color'] + ' / ' + this.gameInfo[play + 'Card'])
        color = (color - 1) * 13 + card
        if (color <= 9) {
          color = '0' + color
        }
        return 'GP' + color
      },
      checkCard() {
        var bankerA = this.getCardPoint(this.gameInfo.bankerA)
        var bankerB = this.getCardPoint(this.gameInfo.bankerB)
        var bankerC = this.gameInfo.bankerC
        if (!this.gameInfo.bankerA || !this.gameInfo.bankerB) {
          return false
        }
        var playerA = this.getCardPoint(this.gameInfo.playerA)
        var playerB = this.getCardPoint(this.gameInfo.playerB)
        var playerC = this.gameInfo.playerC
        if (!this.gameInfo.playerA || !this.gameInfo.playerB) {
          return false
        }
        var banker = (bankerA + bankerB) % 10
        var player = (playerA + playerB) % 10
        this.gameInfo.playerPoint = player
        this.gameInfo.bankerPoint = banker

        if (playerC) {
          this.gameInfo.playerPoint = (playerA + playerB + this.getCardPoint(this.gameInfo.playerC)) % 10
        }
        if (bankerC) {
          this.gameInfo.bankerPoint = (bankerA + bankerB + this.getCardPoint(this.gameInfo.bankerC)) % 10
        }

        console.log('checkCard..........................1')
        if (banker >= 8 || player >= 8) {
          if (bankerC) {
            return false
          }
          if (playerC) {
            return false
          }
          return true
        }
        console.log('checkCard..........................2')
        /* 0/1/2/3/4/5 补一张牌
        6/7/8/9不需补牌*/
        if (player <= 5) {
          if (!playerC) {
            return false
          }
        } else {
          if (playerC) {
            return false
          }
        }
        console.log('checkCard..........................3')
        /* 0/1/2 补一张牌
        3 如果闲家第三张牌是8不须补牌
        4 如果闲家第三张牌是1,8,9,0不须补牌
        5 如果闲家第三张牌是1,2,3,8,9,0不须补牌
        6 如果闲家第三张牌是6,7补一张牌
        7 不须补牌
        8/9 例牌，不须补牌
        当闲家的前两张牌为６点或７点时，庄家的前两张牌小于等于５点时，庄家需补牌一张*/
        if (banker <= 2) {
          if (!bankerC) {
            return false
          }
        }
        if (player <= 5) {
          playerC = this.getCardPoint(this.gameInfo.playerC)
          console.log('checkCard..........................4')
          if (banker === 3) {
            if (playerC === 8) {
              if (bankerC) {
                return false
              }
            } else {
              if (!bankerC) {
                return false
              }
            }
          }
          console.log('checkCard..........................5')
          if (banker === 4) {
            if (playerC === 1 || playerC === 8 || playerC === 9 || playerC === 0) {
              if (bankerC) {
                return false
              }
            } else {
              if (!bankerC) {
                return false
              }
            }
          }
          console.log('checkCard..........................6')
          if (banker === 5) {
            if (playerC === 4 || playerC === 5 || playerC === 6 || playerC === 7) {
              if (!bankerC) {
                return false
              }
            } else {
              if (bankerC) {
                return false
              }
            }
          }
          console.log('checkCard..........................7')
          if (banker === 6) {
            if (playerC === 6 || playerC === 7) {
              if (!bankerC) {
                return false
              }
            } else {
              if (bankerC) {
                return false
              }
            }
          }
        }
        if (player === 6 || player === 7) {
          if (banker === 3 || banker === 4 || banker === 5) {
            if (!bankerC) {
              return false
            }
          }
        }

        console.log('checkCard..........................8')
        return true
      },
      getAllCardPoint() {
        console.log('getAllCardPoint..........................8')
        this.gameInfo.bankerA = this.getCard('bankerA')
        this.gameInfo.bankerB = this.getCard('bankerB')
        this.gameInfo.bankerC = this.getCard('bankerC')

        this.gameInfo.playerA = this.getCard('playerA')
        this.gameInfo.playerB = this.getCard('playerB')
        this.gameInfo.playerC = this.getCard('playerC')

        var bankerA = this.getCardPoint(this.gameInfo.bankerA)
        var bankerB = this.getCardPoint(this.gameInfo.bankerB)
        console.log('getAllCardPoint..........................9' + bankerA + '//' + bankerB)
        var bankerC = this.gameInfo.bankerC
        if (this.gameInfo.bankerA && this.gameInfo.bankerB) {
          this.gameInfo.bankerPoint = (bankerA + bankerB) % 10
          if (bankerC) {
            this.gameInfo.bankerPoint = (bankerA + bankerB + this.getCardPoint(this.gameInfo.bankerC)) % 10
          }
        }
        var playerA = this.getCardPoint(this.gameInfo.playerA)
        var playerB = this.getCardPoint(this.gameInfo.playerB)
        var playerC = this.gameInfo.playerC
        if (this.gameInfo.playerA && this.gameInfo.playerB) {
          this.gameInfo.playerPoint = (playerA + playerB) % 10
          if (playerC) {
            this.gameInfo.playerPoint = (playerA + playerB + this.getCardPoint(this.gameInfo.playerC)) % 10
          }
        }
      },
      getCardPoint(card) {
        if (!card) {
          return ''
        }
        var cardPoint = Number(card.substr(2, 2))
        var point = cardPoint % 13
        if (point >= 10) {
          point = 0
        }
        return point
      },
      handleShuffleGame() {
        this.$confirm('确定洗牌，继续吗', {
          confirmButtonText: this.$t('operate.confirm'),
          cancelButtonText: this.$t('operate.cancel'),
          type: 'warning'
        }).then(() => {
          this.updateTableInfo()
          this.resetGameInfo()
          this.resetChipInfo()
        })
      },
      getTableList() {
        this.gameInfo.tableId = this.member.tableId
        console.log('getTableList.................' + this.gameInfo.tableId)
        var agentParam = { ifPage: false }
        listTableInfo(agentParam).then(r => {
          this.tableList = r.data.list
          console.log(this.objectList)
        })
      },
      handleVerifyList() {
        console.log('handleVerifyList.................................1')
        var searchParam = { verifyNo: 'FIVE_BASE_SETTING', ifPage: false }
        list2(searchParam).then(r => {
          // this.formatList(r.data.list)
          this.verifyList = r.data.list
          this.setVerifyInfo()
          console.log('handleVerifyList.................................2')
          console.log(this.gameVerifyInfo)
        })
      },
      setVerifyInfo() {
        if (this.verifyList.length) {
          for (var item of this.verifyList) {
            if (item.id === this.gameInfo.verifyId) {
              this.gameVerifyInfo = item
            }
          }
          if (this.gameVerifyInfo.ruleParam) {
            var ruleParam = JSON.parse(this.gameVerifyInfo.ruleParam)
            this.gameInfo.ruleValue13 = ruleParam.ruleValue13
            this.gameInfo.ruleValue14 = ruleParam.ruleValue14
            this.gameInfo.ruleValue15 = ruleParam.ruleValue15
            this.gameInfo.ruleValue16 = ruleParam.ruleValue16
            this.gameInfo.baseAmount = this.gameInfo.ruleValue15
          }
        }
      },
      updateVerify() {
        var verifyInfo = { id: this.gameInfo.verifyId }
        list2(verifyInfo).then(r => {
          if (r.data.list && r.data.list.length) {
            this.gameVerifyInfo = r.data.list[0]
            if (this.gameVerifyInfo.ruleParam) {
              var ruleParam = JSON.parse(this.gameVerifyInfo.ruleParam)
              ruleParam.ruleValue13 = this.gameInfo.ruleValue13
              ruleParam.ruleValue14 = this.gameInfo.ruleValue14
              ruleParam.ruleValue15 = this.gameInfo.ruleValue15
              ruleParam.ruleValue16 = parseFloat(this.gameInfo.ruleValue16)
              this.gameVerifyInfo.ruleParam = JSON.stringify(ruleParam)
            }
            console.log('updateVerify................1 ', this.gameVerifyInfo)
            update(this.gameVerifyInfo).then(res => {
              console.log('updateVerify................2 ', res)
            })
          }
        })
      },
      handleChangeVerify() {
        if (!this.gameInfo.tableId) {
          this.$message.error('请选择台号')
          return
        }
        if (!this.gameInfo.verifyId) {
          this.$message.error('请选择验证编号')
          return
        }
        var tableInfo = { id: this.gameInfo.tableId, dealerId: this.gameInfo.verifyId }
        updateTableInfo(tableInfo).then(res => {
          // this.$message.success('操作成功')
          this.setVerifyInfo()
        })
      },
      handleTableId() {
        var member = { id: this.member.id, tableId: this.gameInfo.tableId }
        updateMember(member).then(r => {
          this.getTable()
        })
      },
      getTable() {
        if (!this.gameInfo.tableId) {
          this.gameInfo.tableId = 0
        }
        getTableInfo(this.gameInfo.tableId).then(res => {
          if (res.data) {
            this.tableInfo = res.data
            this.gameInfo.matchId = this.tableInfo.matchId
            this.gameInfo.gameId = this.tableInfo.gameId
            this.gameInfo.verifyId = this.tableInfo.dealerId
            if (!this.tableInfo.dealerId) {
              this.gameInfo.verifyId = null
            }
            this.gameInfo.isFree = this.tableInfo.isFree
            this.gameInfo.cardsQty = this.tableInfo.superMin
            this.getGameResult()
            this.handleVerifyList()
          }
        })
      },
      getGameResult() {
        var gameInfo = { tableId: this.gameInfo.tableId, matchIdList: [this.gameInfo.matchId] }
        listGameResult(gameInfo).then(res => {
          console.log('getGameResult..................1')
          console.log(res)
          this.objectList = res.data.list
          this.handleGameTransHist()
          this.handleRemainCards()
        })
      },
      updateTableInfo() {
        var tableInfo = { id: this.gameInfo.tableId, gameId: 0, matchId: 0 }
        updateTableInfo(tableInfo).then(res => {
          this.$message.success('操作成功')
          this.updateMatchInfo()
          this.getTable()
        })
      },
      updateMatchInfo() {
        var matchInfo = { id: this.gameInfo.matchId, status: 0 }
        updateGameMatch(matchInfo).then(res => {
          console.log(res)
        })
      },
      handleIsFree() {
        if (!this.gameInfo.tableId) {
          return
        }
        var tableInfo = { id: this.gameInfo.tableId, isFree: this.gameInfo.isFree }
        updateTableInfo(tableInfo).then(res => {
          console.log('操作成功')
        })
      },
      handleCardsQty() {
        if (!this.gameInfo.tableId || !this.gameInfo.cardsQty) {
          return
        }
        var tableInfo = { id: this.gameInfo.tableId, superMin: this.gameInfo.cardsQty }
        updateTableInfo(tableInfo).then(res => {
          console.log('操作成功')
        })
      },
      gameResultFormat: function (row, column) {
        const items = gameResultFilter(row.gameType, row.gameResult)
        var tempItems = []
        for (var i = 0; i < items.length; i++) {
          tempItems.push(this.$t(items[i]))
        }
        return tempItems.join(',')
      },
      getCardType(card, index) {
        if (!card) {
          return ''
        }
        var cardPoint = Number(card.substr(2, 2))
        var point = cardPoint % 13
        if (point === 0) {
          point = 13
        }
        var suitStr = ''
        var suit = Math.ceil(cardPoint / 13)

        if (suit === 1) suitStr = 's'
        else if (suit === 2) suitStr = 'h'
        else if (suit === 3) suitStr = 'd'
        else if (suit === 4) suitStr = 'c'
        return 'type' + suitStr + point
      },
      gameChipFormat(row, column) {
        return this.gameChipFiter(row)
      },
      gameTypeFilter(row, column) {
        return this.$t(getGameTypeEnum(row.gameType).label)
      },
      dateTimeFormat: function (row, column) {
        var date = row[column.property]
        if (!date) {
          return ''
        }
        return moment(date).format('YYYY-MM-DD HH:mm:ss')
      },
      gameChipFiter(gameTrans) {
        var gameResultDesc = ''
        var chipItems = []
        switch (Number(gameTrans.gameType)) {
          case 1:
            if (gameTrans.bankerAmout) {
              chipItems.push(this.$t('game.banker') + ': ' + gameTrans.bankerAmout)
            }
            if (gameTrans.bankerPairAmout) {
              chipItems.push(this.$t('game.bankerPair') + ': ' + gameTrans.bankerPairAmout)
            }
            if (gameTrans.bankerSuperAmout) {
              chipItems.push(this.$t('game.luckSix') + ': ' + gameTrans.bankerSuperAmout)
            }
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.player') + ': ' + gameTrans.playerAmout)
            }
            if (gameTrans.playerPairAmout) {
              chipItems.push(this.$t('game.playerPair') + ': ' + gameTrans.playerPairAmout)
            }
            if (gameTrans.tieAmout) {
              chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
          case 2:
            if (gameTrans.bankerAmout) {
              chipItems.push(this.$t('game.tiger') + ': ' + gameTrans.bankerAmout)
            }
            if (gameTrans.bankerPairAmout) {
              chipItems.push(this.$t('game.pair') + ': ' + gameTrans.bankerPairAmout)
            }
            if (gameTrans.tieAmout) {
              chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
            }
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.dragon') + ': ' + gameTrans.playerAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
          case 3:
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.player1Equal') + ': ' + gameTrans.playerAmout)
            }
            if (gameTrans.playerPairAmout) {
              chipItems.push(this.$t('game.player1Double') + ': ' + gameTrans.playerPairAmout)
            }
            if (gameTrans.playerSuperAmout) {
              chipItems.push(this.$t('game.player1Super') + ': ' + gameTrans.playerSuperAmout)
            }
            if (gameTrans.player2Amout) {
              chipItems.push(this.$t('game.player2Equal') + ': ' + gameTrans.player2Amout)
            }
            if (gameTrans.player2PairAmout) {
              chipItems.push(this.$t('game.player2Double') + ': ' + gameTrans.player2PairAmout)
            }
            if (gameTrans.player2SuperAmout) {
              chipItems.push(this.$t('game.player2Super') + ': ' + gameTrans.player2SuperAmout)
            }
            if (gameTrans.player3Amout) {
              chipItems.push(this.$t('game.player3Equal') + ': ' + gameTrans.player3Amout)
            }
            if (gameTrans.player3PairAmout) {
              chipItems.push(this.$t('game.player3Double') + ': ' + gameTrans.player3PairAmout)
            }
            if (gameTrans.player3SuperAmout) {
              chipItems.push(this.$t('game.player3Super') + ': ' + gameTrans.player3SuperAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
        }
        return gameResultDesc
      },
      handleSaveGameBet() {
        if (this.gameTransHist.currentAmount) {
          this.$message.error('已下注，等待开牌')
          return
        }
        if (this.loadingTags.save) {
          this.$message.error('开牌中，请稍后')
          return
        }
        if (this.loadingTags.bet) {
          this.$message.error('下注中，请稍后')
          return
        }
        if (!this.gameInfo.tableId) {
          this.$message.error('请选择台号')
          return
        }
        if (!this.gameInfo.matchId) {
          this.$message.error('暂无下注的局')
          return
        }
        if (!this.gameInfo.baseAmount) {
          this.$message.error('请输入基码金额')
          return
        }
        if (!this.gameInfo.chipAmount) {
          // this.$message.error('请输入下注金额')
          return
        }
        if (!this.gameInfo.chipType) {
          this.$message.error('请选择B/P')
          return
        }
        var bankerAmout = 0
        var playerAmout = 0
        var bankerRate = 0
        var playerRate = 0
        if (this.gameInfo.chipType === 'B') {
          bankerAmout = parseFloat(this.gameInfo.chipAmount)
          if (this.gameInfo.baseAmount) {
            bankerRate = parseFloat(this.gameInfo.chipAmount) / parseFloat(this.gameInfo.baseAmount)
          }
        } else {
          playerAmout = parseFloat(this.gameInfo.chipAmount)
          if (this.gameInfo.baseAmount) {
            playerRate = parseFloat(this.gameInfo.chipAmount) / parseFloat(this.gameInfo.baseAmount)
          }
        }
        this.gameTransHist.currentAmount = this.gameTransHist.currentAmount + bankerAmout + playerAmout
        this.gameTransHist.currentBase = this.gameTransHist.currentBase + bankerRate + playerRate

        var gameTransHist = {
          id: null, parentId: 0, verifyId: 0, chipInNo: moment().format('YYYYMMDDHHmmss'), chipInDt: null,
          memberId: this.member.id, tableId: this.gameInfo.tableId, matchId: this.gameInfo.matchId, gameId: 0,
          gameType: 1, gameCategory: 1,
          bankerRate: bankerRate, bankerAmout: bankerAmout, playerRate: playerRate, playerAmout: playerAmout,
          gameResult: '', chipInResult: this.gameInfo.chipType, chipInAmount: bankerAmout + playerAmout, winAmount: 0, balance: 0, washAmount: 0, status: 0
        }

        this.loadingTags.bet = true
        addGameTransHist(gameTransHist).then(res => {
          /* this.gameInfo.chipAmount = null
          if (!this.gameTransHist.currentAmount && this.gameInfo.amount) {
            this.gameInfo.chipAmount = this.gameInfo.amount
          } */
          this.loadingTags.bet = false
          this.$message.success('下注成功')
          console.log(res)
        }).catch(() => {
          this.$message.success('下注失败')
          this.loadingTags.bet = false
        })
        // 基码金额	100
        // 当前下注基码个数	3		当前下注金额	300
        // 累计下注基码个数	25		累计金额	2500
        // 输赢情况基码个数	2		输赢金额	200
      },
      handleGameTransHist() {
        console.log('handleGameTransHist..............................88' + this.gameInfo.matchId)
        if (!this.gameInfo.matchId) {
          this.gameInfo.matchId = 0
        }
        var params = { memberId: this.member.id, matchId: this.gameInfo.matchId }
        listGameTransHist(params).then(res => {
          console.log('handleGameTransHist..............................')
          console.log(res.data.list)
          this.gameTransHistList = res.data.list
          this.sumGameTransHist(res.data.list)
        })
      },
      sumGameTransHist(list) {
        var winAmount = 0
        var winBase = 0
        var chipAmount = 0
        var chipBase = 0
        for (var item of list) {
          /* winAmount = winAmount + item.winAmount
          winBase = winBase + item.washAmout
          chipAmount = chipAmount + Math.abs(item.winAmount)
          chipBase = chipBase + Math.abs(item.washAmout)*/

          winAmount = winAmount + item.winAmount
          if (item.winAmount > 0) {
            winBase = winBase + item.bankerRate + item.playerRate
          } else if (item.winAmount < 0) {
            winBase = winBase - item.bankerRate - item.playerRate
          }
          chipAmount = chipAmount + item.chipInAmount
          chipBase = chipBase + item.bankerRate + item.playerRate
        }
        this.gameTransHist.currentAmount = 0
        this.gameTransHist.currentBase = 0

        this.gameTransHist.winAmount = formatNumeric(winAmount)
        this.gameTransHist.winBase = formatNumeric(winBase)
        this.gameTransHist.chipAmount = formatNumeric(chipAmount)
        this.gameTransHist.chipBase = formatNumeric(chipBase)
      },
      handleSocketInfo() {
        console.log('play1................................1')
        console.log(this.socketInfo)
        if (this.socketInfo.tableId === this.gameInfo.tableId) {
          /*console.log('play1................................2')
          this.gameInfo.profitResult = ''
          if (this.gameInfo.type === 1) {
            this.gameInfo.betResult = this.socketInfo.player2A
            this.gameInfo.betAmount = this.socketInfo.player2Amount
            if (this.socketInfo.profitResult) {
              this.gameInfo.profitResult = this.socketInfo.profitResult
            }
          } else {
            this.gameInfo.betResult = this.socketInfo.player3A
            this.gameInfo.betAmount = this.socketInfo.player3Amount
            if (this.socketInfo.profitResult2) {
              this.gameInfo.profitResult = this.socketInfo.profitResult2
            }
          }
          console.log('play1................................3' + this.gameInfo.profitResult)
          if (this.gameInfo.baseAmount) {
            this.gameInfo.amount = formatNumeric(this.gameInfo.baseAmount * this.gameInfo.betAmount)
          }
          this.gameInfo.matchId = this.socketInfo.matchId
          console.log('play1................................4')
          this.gameInfo.chipType = this.gameInfo.betResult
          this.gameInfo.chipAmount = this.gameInfo.amount */
          // this.setSocketInfo(socketInfo)
          this.handleGameTransHist()
        }
        console.log(this.gameInfo)
      },
      setSocketInfo(socketInfo) {
        console.log('play1................................2')
        this.gameInfo.profitResult = ''
        if (this.gameInfo.type === 1) {
          this.gameInfo.betResult = socketInfo.player2A
          this.gameInfo.betAmount = socketInfo.player2Amount
          if (socketInfo.profitResult) {
            this.gameInfo.profitResult = socketInfo.profitResult
          }
        } else {
          this.gameInfo.betResult = socketInfo.player3A
          this.gameInfo.betAmount = socketInfo.player3Amount
          if (socketInfo.profitResult2) {
            this.gameInfo.profitResult = socketInfo.profitResult2
          }
        }
        console.log('play1................................3' + this.gameInfo.profitResult)
        if (this.gameInfo.baseAmount) {
          this.gameInfo.amount = formatNumeric(this.gameInfo.baseAmount * this.gameInfo.betAmount)
        }
        this.gameInfo.matchId = socketInfo.matchId
        console.log('play1................................4')
        this.gameInfo.chipType = this.gameInfo.betResult
        this.gameInfo.chipAmount = this.gameInfo.amount
      },
      handleAutoBet() {
        setTimeout(() => {
          if (!this.gameInfo.flying && this.gameInfo.autoBet) {
            this.handleSaveGameBet()
          }
        }, 500)
      },
      handleType() {
        this.visibleTags.type = !this.visibleTags.type
        console.log('handleType...................')
      },
      handleCards() {
        this.visibleTags.cards = !this.visibleTags.cards
      },
      resetChipInfo() {
        this.gameTransHist.currentAmount = 0
        this.gameTransHist.currentBase = 0

        this.gameTransHist.winAmount = 0
        this.gameTransHist.winBase = 0
        this.gameTransHist.chipAmount = 0
        this.gameTransHist.chipBase = 0

        this.gameInfo.chipType = null
        // this.gameInfo.chipAmount = null
        this.gameInfo.betResult = null
        this.gameInfo.betAmount = 0
        this.gameInfo.amount = null
      }
    }
  }
</script>

<style scoped>
  .ex-table-container {
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
  }

  .ex-table {
    font-weight: bold;
  }

  .ex-table td {
    height: 30px;
    line-height: 30px;
    width: 5%;
  }

  .card-name {
    color: #643c29;
  }

  .remain-all {
    color: #c40000;
  }

  .remain-part {
    color: #e97024;
  }

  .remain-zero {
    color: #b2b2b2;
  }

  .card-box {
    margin-top: 0;
    padding-left: 10%;
    padding-right: 10%;
  }

  .data-box {
    padding-left: 10%;
    padding-right: 10%;
  }

  .banker-text,
  .player-text {
    font-size: 1.8rem !important;
    text-align: center;
    margin-top: -1px;
  }

  .banker-text {
    color: red;
  }

  .player-text {
    color: blue;
  }

  /*开牌 */
  .container .mipai-box {
    top: 0%;
    left: 0%;
    width: 175px;
    height: 125px;
    position: absolute;
    flex-direction: column;
    border-radius: .5rem;
    z-index: 999;
    padding-top: 1%;
    background: #131313;
    opacity: 0.9;
    border-radius: 0.5rem;
  }

  .container .mipai-box2 {
    width: 250px;
    height: 250px;
  }

  .container .mipai-box.active {
    display: block;
    animation: bounce02 1.5s;
  }

  .container .mipai-box.active-a {
    display: block !important;
    animation: bounce03 1.5s;
  }

  .container .mipai-box.inactive {
    display: none;
  }

  .container .mipai-box .xian-box,
  .container .mipai-box .zhuang-box {
    height: 60px;
    position: relative;
    display: flex;
    border-bottom: 1px solid #222222;
  }

  .container .mipai-box .total-box {
    font-size: 16px;
    position: absolute;
    left: 5px;
    top: 30px;
    padding: 0 5px 0 5px;
    text-align: center;
    border-radius: 50%;
  }

  .container .mipai-box .title-box {
    position: absolute;
    font-size: 16px;
    top: 5px;
    left: 5px;
    text-align: center;
  }

  .container .mipai-box .zhuang-box .total-box,
  .container .mipai-box .long-box .total-box {
    color: #ffffff;
    background-color: #c1182e;
  }

  .container .mipai-box .xian-box .total-box,
  .container .mipai-box .hu-box .total-box {
    color: #ffffff;
    background-color: #2b4be9;
  }

  .container .mipai-box .zhuang-box .title-box,
  .container .mipai-box .long-box .title-box {
    color: #c1182e;
  }

  .container .mipai-box .xian-box .title-box,
  .container .mipai-box .hu-box .title-box,
  .container .mipai-box .feng-box .title-box {
    color: #2b4be9;
  }

  .container .mipai-box .smallcard {
    position: absolute;
  }

  .container .mipai-box .smallcard.on {
    display: block;
  }

  .container .mipai-box .smallcard.card1 {
    top: 10px;
    height: 40px;
    left: 40px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card2 {
    top: 10px;
    height: 40px;
    left: 80px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card3 {
    top: 20px;
    height: 30px;
    left: 120px;
    width: 40px;
  }

  .container .mipai-box2 .smallcard.card3 {
    top: 10px;
    height: 40px;
    left: 120px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card4 {
    top: 10px;
    height: 40px;
    left: 160px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card5 {
    top: 10px;
    height: 40px;
    left: 200px;
    width: 30px;
  }

  .mobile .card-box {
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .mobile.app-container {
    margin: 5px;
    margin-top: 40px;
    padding-top: 0px;
    padding-left: 0;
    padding-right: 0;
    background-color: white;
  }

  .mobile .data-box {
    padding-left: 0;
    padding-right: 0;
  }

  .win-tips {
    font-size: 14px !important;
    color: #67c23a;
    margin-left: 10px;
  }
</style>

<style>
  .color-1 .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    color: #fff;
    background-color: #ff0000;
    border-color: #ff0000;
    box-shadow: -1px 0 0 0 #ff0000;
  }

  .color-2 .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    color: #fff;
    background-color: #0000ff;
    border-color: #0000ff;
    box-shadow: -1px 0 0 0 #0000ff;
  }

  .color-3 .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    color: #fff;
    background-color: #00ff00;
    border-color: #00ff00;
    box-shadow: -1px 0 0 0 #00ff00;
  }

  .dealer .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 11px !important;
  }

  .dealer .el-checkbox-button--mini .el-checkbox-button__inner {
    padding: 7px 11px !important;
  }

  .mobile.dealer .el-checkbox-button--mini .el-checkbox-button__inner {
    padding: 7px 8px !important;
    padding: 3px 6px !important;
    font-size: 16px !important;
  }

  .mobile.dealer .el-radio {
    margin-right: 10px
  }

  .app-container.mobile.dealer span,
  .app-container.mobile.dealer label,
  .app-container.mobile.dealer div {
    font-size: 14px !important;
  }

  .app-container.mobile .el-input--small .el-input__inner {
    font-size: 18px !important;
    padding: 0 7px;
  }

  .app-container.mobile .el-input.is-disabled .el-input__inner {
    background-color: #fbfbfb;
    color: #000 !important;
  }

  .dealer .el-radio-button--mini .el-radio-button__inner {
    padding: 10px 5px !important;
    font-size: 18px !important;
  }
</style>