<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="12">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">{{ $t('operate.add') }}</el-button>
          <el-button v-show="this.delete" size="mini" type="danger" icon="el-icon-delete" @click="handleDeleteClick">{{ $t('operate.delete') }}</el-button>
        </el-button-group>
      </el-col>
      <el-col :span="12">
        <el-row type="flex" justify="end">
          <el-input v-model="param.ip" style="width:184px" :placeholder="$t('ipWhiteListInfo.placeholder.ipAddress')" clearable />
          <el-select v-model="param.type" style="width:184px" :placeholder="$t('ipWhiteListInfo.placeholder.type')" clearable>
            <el-option
              v-for="item in ipTypeEnums"
              :key="item.type"
              :label="$t(item.value)"
              :value="item.type"
            />
          </el-select>
          <el-button icon="fa fa-search" type="primary" @click="filterData" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="type" align="center" :label="$t('ipWhiteListInfo.type')">
        <template slot-scope="{row}">
          <el-tag v-if="row.type == 2" type="danger">
            {{ $t('ipWhiteListInfo.blackIp') }}
          </el-tag>
          <el-tag v-else type="success">
            {{ $t('ipWhiteListInfo.whiteIp') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="ipAddress" align="center" :label="$t('ipWhiteListInfo.ipAddress')" />
      <el-table-column prop="createDt" :label="$t('member.createdTime')" align="center" :formatter="dateTimeFormat" />
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('ipWhiteListInfo.title')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="25%">
      <el-form ref="dataForm" :model="object" label-width="80px" :disabled="ifView" :rules="buttonRules">
        <el-form-item :label="$t('ipWhiteListInfo.type')">
          <el-radio-group v-model="object.type">
            <el-radio :label="1"> {{ $t('ipWhiteListInfo.whiteIp') }}</el-radio>
            <el-radio :label="2"> {{ $t('ipWhiteListInfo.blackIp') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('ipWhiteListInfo.ipAddress')" prop="ipAddress">
          <el-input v-model="object.ipAddress" auto-complete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="loadingTags.add" @click="createData">{{ $t('operate.save') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listIpWhiteList, addIpWhiteList, deleteIpWhiteList } from '@/api/setting/ipWhiteList'
import { deepClone } from '@/utils/transferUtil'
import { IpTypeEnum } from '@/enums/ipTypeEnum'
import moment from 'moment'
export default {
  name: 'Country',
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    unblock: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      objectList: [],
      multipleSelection: [],
      systemOptions: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {
        type: '',
        ipAddress: ''
      },
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      },
      buttonRules: {
        ipAddress: [{ required: true, trigger: 'blur', message: this.$t('countryInfo.validateMsg.nameEn') }]
      },
      param: {}
    }
  },
  computed: {
    ipTypeEnums() {
      return IpTypeEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    filterData() {
      this.currentPage = 1
      this.getData()
    },
    getData() {
      this.param.pageNo = this.currentPage
      this.param.pageSize = this.pageSize
      listIpWhiteList(this.param).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogFormVisible = true
      this.object = { type: 2 }
      this.ifView = false
      this.dialogStatus = 'create'
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.add = true
          addIpWhiteList(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.save') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    handleEditClick() {
      if (this.multipleSelection.length > 0) {
        this.loadingTags.edit = false
        this.dialogFormVisible = true
        this.object = deepClone(this.multipleSelection[0])
        this.ifView = false
        this.dialogStatus = 'edit'
      } else {
        this.$message({
          message: this.$t('countryInfo.msg.editData'),
          type: 'warning'
        })
      }
    },
    removeData() {
      if (this.ids.length > 0) {
        deleteIpWhiteList(this.ids).then(() => {
          this.getData()
          this.$message({
            message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
            type: 'success'
          })
        })
      } else {
        this.$message({
          message: this.$t('jetton.msg.deleteData'),
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleBlockClick() {
      this.$confirm(this.$t('operate.block') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.blockData()
      })
    },
    handleUnblockClick() {
      this.$confirm(this.$t('operate.unblock') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.unblockData()
      })
    },
    handleDeleteClick() {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.removeData()
      })
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
