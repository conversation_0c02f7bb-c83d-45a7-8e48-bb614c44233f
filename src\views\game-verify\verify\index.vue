<template>
  <div class="app-container game-setting">
    //
  </div>
</template>

<script>
import { getByRuleNo, add } from '@/api/setting/gameRule'
export default {
  name: 'GameSetting',
  data() {
    return {
      tabName: 'first',
      agentParam: {},
      ruleNo: 'FIVE_BASE_SETTING',
      form: {
        id: null,
        ruleNo: '',
        ruleName: '',
        ruleType: '',
        ruleValue: '',
        ruleValue2: '',
        ruleValue3: '',
        ruleValue4: '',
        ruleValue5: '',
        ruleValue6: '',
        ruleValue7: '',
        ruleValue8: '',
        ruleValue9: '',
        ruleValue10: '',
        ruleValue11: '',
        ruleValue12: '',
        ruleValue13: '',
        ruleValue14: '',
        ruleValue15: '',
        ruleValue16: '',
        ruleValue17: '',
        ruleValue18: '',
        ruleValue19: '',
        ruleValue20: '',
        createBy: this.$store.getters.userId,
        modifiedBy: this.$store.getters.userId
      },
      cardQtyEnums: [{ label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
      roadTypeEnums: [{ label: '大路', value: '1' }, { label: '大眼仔', value: '2' }, { label: '小路', value: '3' }, { label: '蟑螂路', value: '4' }],
      equalEnums: [{ label: '大于', value: '1' }, { label: '等于', value: '2' }, { label: '小于', value: '3' }],
      cardsEnums: ['A', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'],
      bpData: [],
      chipData: []
    }
  },
  /*
  系统设置：SYS_SETTING
  系统生路：SYS_GEN_ROAD
  形态设置：SHAPE
  比例设置：RATE
  点数设置：POINT
  牌数设置：CARD_POINT
  牌型设置：CARD_TYPE
  胜负设置：WIN_LOSE
  验证选项：VALID_OPTION
  */
  created() {
    this.form.ruleNo = this.ruleNo
    this.handleRuleNo()
    this.handleBpData()
    this.handleChipData()
  },
  methods: {
    handleBpData() {
      var item
      item = { value1: 'B', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'P', value2: 'P', value3: 'P', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'B', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'P', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'B', value2: 'P', value3: 'P', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'P', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'B', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'P', value2: 'B', value3: 'P', vaPlue4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'B', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
      item = { value1: 'P', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
      this.bpData.push(item)
    },
    handleChipData() {
      var item
      item = { value1: '1', value2: '1' }
      this.chipData.push(item)
      item = { value1: '3', value2: '1' }
      this.chipData.push(item)
      item = { value1: '7', value2: '1' }
      this.chipData.push(item)
      item = { value1: '5', value2: '10' }
      this.chipData.push(item)
      item = { value1: '6', value2: '12' }
      this.chipData.push(item)
      item = { value1: '8', value2: '16' }
      this.chipData.push(item)
      item = { value1: '10', value2: '20' }
      this.chipData.push(item)

      item = { value1: '8', value2: '8' }
      this.chipData.push(item)
      item = { value1: '24', value2: '8' }
      this.chipData.push(item)
      item = { value1: '56', value2: '8' }
      this.chipData.push(item)
      item = { value1: '40', value2: '80' }
      this.chipData.push(item)
      item = { value1: '48', value2: '96' }
      this.chipData.push(item)
      item = { value1: '64', value2: '128' }
      this.chipData.push(item)
      item = { value1: '80', value2: '160' }
      this.chipData.push(item)
    },
    handleRuleNo() {
      getByRuleNo(this.ruleNo, this.$store.getters.userId).then(r => {
        if (r.data) {
          this.form = r.data
          if (this.form.ruleValue19) {
            this.bpData = JSON.parse(this.form.ruleValue19)
          }
          if (this.form.ruleValue20) {
            this.chipData = JSON.parse(this.form.ruleValue20)
          }
        }
      })
    },
    handleSave() {
      this.form.createBy = this.$store.getters.userId
      this.form.modifiedBy = this.$store.getters.userId
      if (this.bpData.length) {
        this.form.ruleValue19 = JSON.stringify(this.bpData)
      }
      if (this.chipData.length) {
        this.form.ruleValue20 = JSON.stringify(this.chipData)
      }
      add(this.form).then(r => {
        if (r.code === 20000) {
          this.handleRuleNo()
          this.$message.success('保存成功')
        } else {
          this.$message.success('保存失败')
        }
      })
    }
  }
}
</script>

<style>
.game-setting .el-table--small th, .game-setting .el-table--small td {
  padding: 0;
}
.game-setting .el-table--small td .el-input__inner {
  border-radius: 0;
  border: 0 solid #DCDFE6;
  text-align: center;
}
.game-setting .el-table--small th, .game-setting .el-table--small td {
  padding: 0;
}
.game-setting .el-table .cell {
  padding: 0;
}
.game-setting th, .game-setting td {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.game-setting .el-row {
  margin-bottom: 10px;
}
.game-setting .input {
  width: 100px;
}
.game-setting .save {
  margin-left: 25px;
  width: 100px;
}
.game-setting .text {
  margin-right: 10px;
}
.game-setting .el-checkbox {
  margin-right: 10px;
}
</style>
