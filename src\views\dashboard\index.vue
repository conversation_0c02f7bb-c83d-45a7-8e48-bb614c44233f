<template>
  <div v-if="device === 'mobile'">
    <mobile-home />
  </div>
  <div v-else class="dashboard-editor-container">
    <panel-group />
    <div class="bg">
      <el-row>
        <el-col class="border-lrt border-r-t" :span="12">
          <div class="member-label">
            {{ $t('home.table.loginAccount') }}
          </div>
        </el-col>
        <el-col class="border-rt border-r-r" :span="12">
          <div class="member-value">
            {{ userName }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="border-lrt" :span="12">
          <div class="member-label">
            {{ $t('agentForm.fullName') }}
          </div>
        </el-col>
        <el-col class="border-rt" :span="12">
          <div class="member-value">
            {{ member.fullName }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="border-lrt" :span="12">
          <div class="member-label">
            {{ $t('home.table.loginTime') }}
          </div>
        </el-col>
        <el-col class="border-rt" :span="12">
          <div class="member-value">
            {{ member.lastLoginDt | dateTimeFilter }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="border-lrt" :span="12">
          <div class="member-label">
            {{ $t('home.table.loginIp') }}
          </div>
        </el-col>
        <el-col class="border-rt" :span="12">
          <div class="member-value">
            {{ member.lastLoginIp }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="border-lrt" :span="12">
          <div class="member-label">
            {{ $t('home.table.loginAddress') }}
          </div>
        </el-col>
        <el-col class="border-rt" :span="12">
          <div class="member-value">
            {{ member.lastLoginIpAddr }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="border-lrt" :span="12">
          <div v-if="member.memberType !== 4" class="member-label">
            {{ $t('home.table.availableBalance') }}
          </div>
          <div v-else class="member-label">
            {{ $t('home.table.availableBalance') }} <!-- 主账号可用余额 -->
          </div>
        </el-col>
        <el-col class="border-rt" :span="12">
          <div v-if="member.memberType !== 4" class="member-value">
            {{ member.remainPoint }}
          </div>
          <div v-else class="member-value">
            {{ mainAccountRemainPoint }}
          </div>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col class="border-lrt" :span="12">
          <div class="member-label">
            {{ $t('home.table.share') }}
          </div>
        </el-col>
        <el-col class="border-rt" :span="12">
          <div class="member-value">
            {{ member.percentAgent }}%
          </div>
        </el-col>
      </el-row>-->
      <el-row>
        <el-col class="border-lrt border-r-l" :span="12">
          <div class="member-label">
            {{ $t('member.rateOfCodeWashing') }}
          </div>
        </el-col>
        <el-col class="border-rt border-r-b" :span="12">
          <div class="member-value">
            <span v-if="memberWashList.length===1"> {{ memberWashList[0].percentWash }}%</span>
            <span v-else>
              <span v-for="member in memberWashList" :key="member.id">
                {{ member.washFrom }} {{ member.washTo > 0 ? '-' + member.washTo: $t('home.table.above') }} {{ member.percentWash }}%  <span v-if="memberWashList.length>1">|</span>
              </span>
            </span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import PanelGroup from './components/PanelGroup'
import { mapGetters } from 'vuex'
import moment from 'moment'
import { getMemberInfo } from '@/api/account/member'
import MobileHome from '@/components/Mobile/home'

export default {
  name: 'DashboardAdmin',
  filters: {
    dateTimeFilter(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  components: {
    PanelGroup,
    MobileHome
  },
  data() {
    return {
      mainAccountRemainPoint: 0
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'nickName',
      'userName',
      'memberWashList',
      'device'
    ])
  },
  created() {
    if (this.member.memberType === 4) {
      getMemberInfo(this.member.parentId).then(r => {
        this.mainAccountRemainPoint = r.data.remainPoint
      })
    }
    this.$store.dispatch('GetInfo')
  }
}
</script>

<style lang="scss" scoped>
.bg {
  background-color: #ffffff;
  border-radius: 5px;
}
.border {
  border:1px solid #dcdfe6;
}
.border-lrt {
  border-left:1px solid #dcdfe6;
  border-right:1px solid #dcdfe6;
  border-top:1px solid #dcdfe6;
}
.border-rt {
  border-right:1px solid #dcdfe6;
  border-top:1px solid #dcdfe6;
}
.border-b {
  border-bottom:1px solid #dcdfe6;
}
.border-r-t{
  border-radius:5px 0 0 0;
}
.border-r-r{
  border-radius:0 5px 0 0;
}
.border-r-l{
  border-radius:0 0 5px 0;
}
.border-r-b{
  border-radius:0 0 0 5px;
}
.member-label {
  padding-top: 6px;
  padding-left: 10px;
  font-size: 16px;
  line-height: 32px;
  height: 44px;
}
.member-value {
  padding-top: 6px;
  padding-left: 10px;
  font-size: 16px;
  line-height: 32px;
  height: 44px;
}
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
