
// 操作类型;0/10/20/30/40/50/60/70/80/90/100
// 操作类型;草稿/开单/装车/发车/到达/卸车/派送/签收/移库/终止/作废
export const transportBillStatusEnum = [{
  value: 0,
  label: '草稿'
},
{
  value: 10,
  label: '开单'
},
{
  value: 15,
  label: '部分装车'
},
{
  value: 20,
  label: '装车'
},
{
  value: 30,
  label: '发车'
},
{
  value: 40,
  label: '到达'
},
{
  value: 50,
  label: '卸车'
},
{
  value: 55,
  label: '中转'
},
{
  value: 60,
  label: '派送'
},
{
  value: 70,
  label: '签收'
},
{
  value: 80,
  label: '移库'
},
{
  value: 90,
  label: '终止'
},
{
  value: 100,
  label: '作废'
}]

// 托运单状态
export function getTransportBillStatusEnum(value) {
  for (var i = 0; i < transportBillStatusEnum.length; i++) {
    if (transportBillStatusEnum[i].value === value) {
      return transportBillStatusEnum[i].label
    }
  }
  return ''
}

export const addressTypeEnum = [{
  value: 0,
  label: '收货地址'
},
{
  value: 1,
  label: '寄货地址'
}]

export function getAddressTypeEnum(value) {
  return addressTypeEnum[value].label
}

// 订单状态
export const transportOrderStatusEnum = [{
  value: 10,
  label: '待受理'
},
{
  value: 20,
  label: '已受理'
},
{
  value: 30,
  label: '拒绝受理'
}]

export function getTransportOrderStatusEnum(value) {
  for (var i = 0; i < transportOrderStatusEnum.length; i++) {
    if (transportOrderStatusEnum[i].value === value) {
      return transportOrderStatusEnum[i].label
    }
  }
  return ''
}

export const catalogLevelEnum = [{
  value: 1,
  label: '一级'
},
{
  value: 2,
  label: '二级'
},
{
  value: 3,
  label: '三级'
}]

export function getCatalogLevelEnum(value) {
  return catalogLevelEnum[value - 1].label
}

export const replyStatusEnum = [{
  value: 0,
  label: '全部'
},
{
  value: 1,
  label: '未回复'
},
{
  value: 2,
  label: '已回复'
}]

export function getReplyStatusEnum(value) {
  return replyStatusEnum[value].label
}

export const loadBillStatusEnum = [{
  value: 0,
  label: '全部'
},
{
  value: 10,
  label: '待装车'
},
{
  value: 20,
  label: '已装车'
},
{
  value: 30,
  label: '已发车'
},
{
  value: 40,
  label: '已到车'
},
{
  value: 50,
  label: '卸车中'
},
{
  value: 60,
  label: '卸车完成'
}]

export function getLoadBillStatusEnum(value) {
  for (var i = 0; i < loadBillStatusEnum.length; i++) {
    if (loadBillStatusEnum[i].value === value) {
      return loadBillStatusEnum[i].label
    }
  }
  return ''
}

export const headlessStatusEnum = [{
  value: 0,
  label: '全部'
},
{
  value: 1,
  label: '待认领'
},
{
  value: 2,
  label: '认领中'
},
{
  value: 3,
  label: '已认领'
},
{
  value: 4,
  label: '作废'
}]

export function getHeadlessStatusEnum(value) {
  return headlessStatusEnum[value].label
}

export const jettisonTypeEnum = [{
  value: 1,
  label: '违禁品'
},
{
  value: 2,
  label: '延误'
},
{
  value: 3,
  label: '货损'
},
{
  value: 4,
  label: '客户原因'
},
{
  value: 5,
  label: '其他'
}]

export function getJettisonTypeEnum(value) {
  if (value) {
    return jettisonTypeEnum[value - 1].label
  } else {
    return ''
  }
}

// 支付方式
export const paymentMethodEnum = [{
  value: 1, label: '现金'
}, {
  value: 2, label: '支付宝'
}, {
  value: 3, label: '微信'
}, {
  value: 4, label: '银联'
}, {
  value: 5, label: '其他'
}]

export function getPaymentMethodEnum(value) {
  return paymentMethodEnum[value - 1].label
}

// 结算类型
export const paymentTypeEnum = [{
  value: 1,
  code: 'XIANFU',
  label: '现付'
}, {
  value: 2,
  code: 'TIFU',
  label: '提付'
}, {
  value: 3,
  code: 'YUEJIE',
  label: '月结'
}, {
  value: 4,
  code: 'HUIDAN',
  label: '回单付'
}, {
  value: 5,
  code: 'QIANFU',
  label: '欠付'
}]

export function getPaymentTypeEnum(value) {
  return paymentTypeEnum[value - 1].label
}

export function getPaymentTypeCode(value) {
  return paymentTypeEnum[value - 1].code
}

export const remittanceModeEnum = [{
  value: 1,
  label: '银行转账'
}]

export function getRemittanceModeEnum(value) {
  return remittanceModeEnum[value - 1].label
}

export const accountStatusEnum = [{
  value: 0,
  label: '全部'
}, {
  value: 1,
  label: '正常'
}, {
  value: 2,
  label: '低于警戒金额'
}, {
  value: 3,
  label: '低于关闭金额'
}]

export function getAccountStatusEnum(value) {
  return accountStatusEnum[value].label
}

// settlementModeEnum
export const settlementModeEnum = [{
  value: 1,
  label: '现金'
}, {
  value: 2,
  label: '支付宝支付'
}, {
  value: 3,
  label: '微信支付'
}, {
  value: 4,
  label: '银联支付'
}, {
  value: 5,
  label: '其他'
}]

export function getSettlementModeEnum(value) {
  return settlementModeEnum[value - 1].label
}

export const verifyDirectionEnum = [{
  value: 1,
  label: '现金'
}, {
  value: 2,
  label: '银行卡'
}, {
  value: 3,
  label: '支付宝'
}, {
  value: 4,
  label: '微信'
}]

export function getVerifyDirectionEnum(value) {
  return verifyDirectionEnum[value - 1].label
}

export const claimStatusEnum = [{
  value: 1,
  label: '已提交'
}, {
  value: 2,
  label: '撤销'
}, {
  value: 3,
  label: '理赔驳回'
}]

export function getClaimStatusEnum(value) {
  return claimStatusEnum[value - 1].label
}

export const settlementPeriodEnum = [{
  value: 0,
  days: 0,
  label: '现金'
},
{
  value: 1,
  days: 3,
  label: '短欠'
},
{
  value: 2,
  days: 30,
  label: '月结30天'
}, {
  value: 3,
  days: 45,
  label: '月结45天'
}, {
  value: 4,
  days: 60,
  label: '月结60天'
}, {
  value: 5,
  days: 90,
  label: '月结90天'
}, {
  value: 6,
  days: 120,
  label: '月结120天'
}]

export function getSettlementPeriodEnum(value) {
  return settlementPeriodEnum[value].label
}

export const loadBillTypeEnum = [{
  value: 1,
  label: '正常配载'
}, {
  value: 2,
  label: '直到网点'
}, {
  value: 3,
  label: '直达客户'
}]

export function getLoadBillTypeEnum(value) {
  return loadBillTypeEnum[value - 1].label
}

export const applyStatusEnum = [{
  value: 1,
  label: '待审核'
}, {
  value: 2,
  label: '通过'
}, {
  value: 3,
  label: '驳回'
}]

export function getApplyStatusEnum(value) {
  return applyStatusEnum[value - 1].label
}

export const reviewStatusEnum = [
  {
    value: 2,
    label: '通过'
  }, {
    value: 3,
    label: '驳回'
  }
]

export const stationStatusEnum = [{
  value: 1,
  label: '生效'
}, {
  value: 2,
  label: '暂停'
}, {
  value: 3,
  label: '失效'
}]

export function getStationStatusEnum(value) {
  for (var i = 0; i < stationStatusEnum.length; i++) {
    if (stationStatusEnum[i].value === value) {
      return stationStatusEnum[i].label
    }
  }
  return ''
}

export const stationVisibilityEnum = [{
  value: 1,
  label: '平台可见'
}, {
  value: 2,
  label: '公司可见'
}, {
  value: 3,
  label: '不可见'
}]

export function getStationVisibilityEnum(value) {
  for (var i = 0; i < stationVisibilityEnum.length; i++) {
    if (stationVisibilityEnum[i].value === value) {
      return stationVisibilityEnum[i].label
    }
  }
  return ''
}

export const stationTypeEnum = [{
  value: 1, label: '平台'
}, {
  value: 2, label: '公司总部'
}, {
  value: 3, label: '一级网点'
}, {
  value: 4, label: '二级网点'
}, {
  value: 5, label: '一级分拨'
}, {
  value: 6, label: '二级分拨'
}, {
  value: 7, label: '转寄网点'
}]

export function getStationTypeEnum(value) {
  return stationTypeEnum[value - 1].label
}
