// export const baseUrl = 'http://*************:7060'
// export const socketUrl = 'http://*************:7066'

export const baseUrl = 'http://*************:6060'
export const socketUrl = 'http://*************:6066'

// export const baseUrl = 'http://localhost:6060'
// export const socketUrl = 'http://localhost:6066'

// export const baseUrl = 'http://************:6060'
// export const socketUrl = 'http://************:6066'

// export const baseUrl = 'http://*************:7060'
// export const socketUrl = 'http://*************:7066'

// export const baseUrl = 'http://**************:6060' // prod
// export const socketUrl = 'http://**************:6066' // prod

// export const baseUrl = 'http://*************:6060' // beta
// export const socketUrl = 'http://*************:6066' // beta

// export const baseUrl = 'http://g.huobs.com:6060' // beta
// export const socketUrl = 'http://g.huobs.com:6066' // beta

// export const baseUrl = (process.env.NODE_ENV === 'development' ? 'http://localhost:6060' : 'http://*************:6060')
// export const socketUrl = (process.env.NODE_ENV === 'development' ? 'http://localhost:6066' : 'http://*************:6066')

// export const baseUrl = (process.env.NODE_ENV === 'development' ? 'http://localhost:7060' : 'http://*************:7060')
// export const socketUrl = (process.env.NODE_ENV === 'development' ? 'http://localhost:7066' : 'http://*************:7066')

// export const baseUrl = (process.env.NODE_ENV === 'development' ? 'http://localhost:6060' : 'http://*************:6060')
// export const socketUrl = (process.env.NODE_ENV === 'development' ? 'http://localhost:6066' : 'http://*************:6066')

export const fileUploadUrl = baseUrl + '/file/upload'

export const dealerUploadUrl = baseUrl + '/file/upload/dealer'

export const excelUploadUrl = baseUrl + '/excel/upload'

export const excelDownloadUrl = baseUrl + '/excel/download'

export const qrCodeUrl = baseUrl + '/root/qrcode/gen?url='
