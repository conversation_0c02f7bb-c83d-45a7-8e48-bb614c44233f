<template>
  <div class="app-container">
    <el-form ref="postForm" label-width="110px" :model="postForm" label-suffix=":" :rules="rules"
      class="form-container">
      <div class="createPost-main-container">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-collapse v-model="activeNames" class="mt-10">
              <el-collapse-item :title="$t('member.accountInfo')" name="3">
                <el-input v-model="postForm.user.password" type="password" style="position:fixed;bottom:-9999px;" />
                <el-row>
                  <el-col :span="12">
                    <el-form-item :label="$t('member.account')" prop="user.userName" required>
                      <el-input v-model="postForm.user.userName" class="input-with-select" :disabled="isEdit" clearable>
                        <el-button slot="append" :disabled="isEdit" :loading="remoteLoading" type="text"
                          @click="genUserName">{{ $t('operate.sysgen') }}</el-button>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('member.memberNickname')" class="postInfo-container-item">
                      <el-input v-model="postForm.user.nickName" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item :label="$t('member.passForm.password')" prop="user.password"
                      class="postInfo-container-item" required>
                      <el-input v-model="postForm.user.password" :disabled="isEdit" type="password" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('member.passForm.passwordConfirm')" prop="user.repeatPassword"
                      class="postInfo-container-item" required>
                      <el-input v-model="postForm.user.repeatPassword" :disabled="isEdit" type="password" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item :label="$t('member.fullName')" class="postInfo-container-item">
                      <el-input v-model="postForm.member.fullName" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('member.mobilePhone')" class="postInfo-container-item">
                      <el-input v-model="postForm.member.phoneNo" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item :label="$t('member.role')" class="postInfo-container-item">
                      <el-radio-group v-model="postForm.member.memberType">
                        <el-radio-button :label="7">{{ $t('member.avatar') }}</el-radio-button>
                        <el-radio-button :label="8">{{ $t('member.pagcor') }}</el-radio-button>
                        <el-radio-button :label="9">{{ $t('member.cashier') }}</el-radio-button>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="mt-10">
          <el-col :offset="6" :span="12">
            <el-form-item>
              <el-button :loading="loading" type="primary" @click="submitForm">
                {{ $t('operate.save') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>
<script>
import { addMember, fetchMember, genAccount } from '@/api/account/member'
import { mapGetters } from 'vuex'
import { checkAccountExist } from '@/api/account/accountCheck'
import { isValidPassword } from '@/utils/validate'
import { deepClone } from '@/utils/transferUtil'
const defaultForm = {
  user: {
    id: 0,
    userName: '',
    nickName: ''
  }, // 前台展示时间
  member: {
    percentAgent: 0,
    memberType: 7
  }
}

export default {
  name: 'MemberDetail',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('memberAddForm.validateMsg.account')))
      } else {
        checkAccountExist(
          value,
          3,
          this.postForm.user.id
        ).then(r => {
          if (r.data) {
            callback(new Error(this.$t('memberAddForm.validateMsg.accountExist')))
          } else {
            callback()
          }
        })
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.password')))
      } else if (!isValidPassword(value)) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordFormate')))
      } else {
        callback()
      }
    }
    const validateCheckPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordConfirm')))
      } else if (value !== this.postForm.user.password) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordDifferent')))
      } else {
        callback()
      }
    }
    return {
      multipleSelection: [],
      ids: [],
      activeName: 'first',
      postForm: Object.assign({}, defaultForm),
      remoteLoading: false,
      loading: false,
      userListOptions: [],
      rules: {
        'user.userName': [{ validator: validateUsername }],
        'user.password': [{ validator: validatePassword }],
        'user.repeatPassword': [{ validator: validateCheckPassword }]
      },
      tempRoute: {},
      activeNames: ['1', '2', '3'],
      jettonList: []
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ])
  },
  watch: {
    multipleSelection: function () {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
      this.postForm.jettonIdList = arr
    }
  },
  created() {
    if (this.isEdit) {
      const id = this.$route.params && this.$route.params.id
      this.fetchData(id)
    } else {
      this.postForm = deepClone(defaultForm)
      this.initEmbedTable(1)
    }
    this.tempRoute = Object.assign({}, this.$route)
  },
  methods: {
    genUserName() {
      this.remoteLoading = true
      genAccount(3).then(r => {
        this.postForm.user.userName = r.data
        this.remoteLoading = false
      })
    },
    callToggleSelection(data) {
      for (var i = 0; i < data.length; i++) {
        for (var j = 0; j < this.jettonList.length; j++) {
          if (data[i].id === this.jettonList[j].id) {
            this.toggleSelection([this.jettonList[j]])
          }
        }
      }
    },
    toggleSelection(rows) {
      console.log(rows)
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    fetchData(id) {
      fetchMember(id).then(response => {
        this.postForm.member = response.data
        this.postForm.user = response.data.user
        this.postForm.user.repeatPassword = this.postForm.user.password
        this.postForm.washItems = response.data.memberWashList
        this.postForm.jettonList = response.data.jettonList
        this.initSelectedJettonData(response.data.jettonList)
      }).catch(err => {
        console.log(err)
      })
    },
    submitForm() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          addMember(this.postForm).then(r => {
            if (this.isEdit) {
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$message({
                message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
                type: 'success',
                duration: 2000
              })
              this.postForm = deepClone(defaultForm)
              this.$refs.postForm.resetFields()
            }
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style>
.input-with-select .el-input-group__append {
  background-color: #fff;
  color: #006dfe;
}

.jetton .has-gutter .el-checkbox {
  display: none
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.createPost-container {
  position: relative;

  .createPost-main-container {
    padding: 40px 45px 20px 50px;

    .postInfo-container {
      position: relative;
      @include clearfix;
      margin-bottom: 10px;

      .postInfo-container-item {
        float: left;
      }
    }
  }

  .word-counter {
    width: 40px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
}

.article-textarea /deep/ {
  textarea {
    padding-right: 40px;
    resize: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #bfcbd9;
  }
}
</style>
