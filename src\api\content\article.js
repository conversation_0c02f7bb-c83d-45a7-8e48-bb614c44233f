import request from '@/utils/request'

export function addArticle(data) {
  return request({
    url: '/article/add',
    method: 'post',
    data
  })
}

export function updateArticle(data) {
  return request({
    url: '/article/update',
    method: 'post',
    data
  })
}

export function listArticle(data) {
  return request({
    url: '/article/open/list',
    method: 'post',
    data
  })
}

export function fetchArticle(id) {
  return request({
    url: '/article/open/get/' + id,
    method: 'get'
  })
}
