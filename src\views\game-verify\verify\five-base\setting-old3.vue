<template>
  <div class="app-container game-setting">
    <el-form>
      <el-tabs v-model="tabName">
        <el-tab-pane label="庄闲规则" name="first">
          <el-row>
            <el-col>
              <span class="text">MACO</span>
              <el-radio-group v-model="form.ruleValue">
                <el-radio label="1">M</el-radio>
                <el-radio label="2">A</el-radio>
                <el-radio label="3">C</el-radio>
                <el-radio label="4">O</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">每靴从第</span>
              <el-select v-model="form.ruleValue2" clearable placeholder=" " class="input">
                <el-option v-for="index in 60" :key="index" :label="index + ''" :value="index + ''" />
              </el-select>
              <span class="text">口</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">长龙</span>
              <el-select v-model="form.ruleValue3" clearable placeholder=" " class="input">
                <el-option v-for="index in 10" :key="index" :label="index + ''" :value="index + ''" />
              </el-select>
              <span class="text">口牌</span>
              <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">长庄</el-checkbox>
              <el-checkbox v-model="form.ruleValue5" true-label="1" false-label="2">长闲</el-checkbox>
              <el-checkbox v-model="form.ruleValue6" true-label="1" false-label="2">长单跳</el-checkbox>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">验证靴数</span>
              <el-input v-model="form.ruleValue7" class="input" />
              <span class="text">靴</span>

              <span class="text ml-10">从</span>
              <el-input v-model="form.ruleValue9" class="input" />
              <span class="text">靴开始验证</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text mr-10">MBP</span>
              <el-radio-group v-model="form.ruleValue12">
                <el-radio label="1">GO</el-radio>
                <el-radio label="2">BACK</el-radio>
              </el-radio-group>
              <span class="text mr-10"></span>
              <el-checkbox v-model="form.ruleValue10" true-label="2" false-label="1"
                class="text ml-10">JUB</el-checkbox>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-table ref="bpData" :data="bpData" tooltip-effect="dark" border style="width:815px">
                <el-table-column type="index" align="center" width="42" />
                <el-table-column prop="value1" label="第一局" align="center" width="110">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.value1" clearable placeholder=" ">
                      <el-option label="B" value="B" />
                      <el-option label="P" value="P" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="value2" label="第二局" align="center" width="110">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.value2" clearable placeholder=" ">
                      <el-option label="B" value="B" />
                      <el-option label="P" value="P" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="value3" label="第三局" align="center" width="110">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.value3" clearable placeholder=" ">
                      <el-option label="B" value="B" />
                      <el-option label="P" value="P" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="value4" label="第四局" align="center" width="110">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.value4" clearable placeholder=" ">
                      <el-option label="B" value="B" />
                      <el-option label="P" value="P" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="value5" label="第五局" align="center" width="110">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.value5" clearable placeholder=" ">
                      <el-option label="B" value="B" />
                      <el-option label="P" value="P" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="value6" label="第六局" align="center" width="110">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.value6" clearable placeholder=" ">
                      <el-option label="B" value="B" />
                      <el-option label="P" value="P" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="value1" label="操作" align="center" width="110">
                  <template slot-scope="scope">
                    <el-button icon="el-icon-plus" type="text" class="ml-5 mr-5"
                      @click="handleAddBpData(scope.$index)" />
                    <el-button icon="el-icon-delete" type="text" class="ml-5 mr-5"
                      @click="handleDeleteBpData(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="注码公式" name="second">
          <!--<el-row>
            <el-col>
              <span class="text">基码</span>
              <el-input v-model="form.ruleValue8" class="input" />
              <span class="text ml-10">使用</span>
              <el-radio-group v-model="form.ruleValue10">
                <el-radio label="1">一层公式</el-radio>
                <el-radio label="2">二层公式</el-radio>
                <el-radio label="3">三层公式</el-radio>
              </el-radio-group>
            </el-col>
          </el-row> -->
          <el-row>
            <el-col>
              <span class="text">公式连续下注(不按靴)</span>
              <el-radio-group v-model="form.ruleValue11">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
              <!--<span class="text">首次下注</span>
              <el-input v-model="form.ruleValue9" class="input" />
              <span class="text">基码</span> -->
              <span class="text ml-20">横向第</span>
              <el-select v-model="form.ruleValue8" clearable placeholder=" " class="input">
                <el-option v-for="index in 9" :key="index" :label="(index - 1) + ''" :value="(index - 1) + ''" />
              </el-select>
              <span class="text">口输了回头</span>
              <span class="text ml-20"></span>
              <el-checkbox v-model="form.ruleValue29" true-label="1" false-label="2">始终不回头</el-checkbox>
              <el-checkbox v-model="form.ruleValue30" true-label="1" false-label="2">三局赢两局回头</el-checkbox>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-table ref="chipData" :data="chipData" tooltip-effect="dark" border style="width:915px">
                <el-table-column type="index" align="center" width="42" />
                <el-table-column prop="value1" label="输下(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value1" />
                  </template>
                </el-table-column>
                <el-table-column prop="value2" label="赢下1(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value2" />
                  </template>
                </el-table-column>
                <el-table-column prop="value3" label="赢下2(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value3" />
                  </template>
                </el-table-column>
                <el-table-column prop="value4" label="赢下3(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value4" />
                  </template>
                </el-table-column>
                <el-table-column prop="value5" label="赢下4(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value5" />
                  </template>
                </el-table-column>
                <el-table-column prop="value6" label="赢下5(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value6" />
                  </template>
                </el-table-column>
                <el-table-column prop="value7" label="赢下6(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value7" />
                  </template>
                </el-table-column>
                <el-table-column prop="value8" label="赢下7(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value8" />
                  </template>
                </el-table-column>
                <el-table-column prop="value9" label="赢下8(基码)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value9" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="110">
                  <template slot-scope="scope">
                    <el-button icon="el-icon-plus" type="text" class="ml-5 mr-5"
                      @click="handleAddChipData(scope.$index)" />
                    <el-button icon="el-icon-delete" type="text" class="ml-5 mr-5"
                      @click="handleDeleteChipData(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="盈利设置" name="third">
          <el-row>
            <el-col>
              <span class="text ml-20">本金</span>
              <el-input v-model="form.ruleValue13" class="input" @change="handleRuleValue" />
              <span class="text ml-10">份数</span>
              <el-input v-model="form.ruleValue14" class="input" @change="handleRuleValue" />
              <span class="text ml-10">基码金额</span>
              <el-input v-model="form.ruleValue15" class="input" disabled />
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">累计盈利</span>
              <el-input v-model="form.ruleValue16" class="input" />
              <span class="text ml-20">净赢/净输</span>
              <el-radio-group v-model="form.ruleValue17">
                <el-radio label="1">累计</el-radio>
                <el-radio label="2">连续</el-radio>
                <el-radio label="3">不间断</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <span class="text">注码递增</span>
              <el-radio-group v-model="form.ruleValue21">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
              <span class="text ml-20">累计每输</span>
              <el-input v-model="form.ruleValue22" class="input" />
              <span class="text">局 注码增加</span>
              <el-input v-model="form.ruleValue23" class="input" />
              <span class="text">%</span>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <span class="text">止盈</span>
              <el-input v-model="form.ruleValue24" class="input" />
              <span class="text">局 </span>
              <el-input v-model="form.ruleValue25" class="input" />
              <span class="text">%</span>
              <span class="text ml-20">止损</span>
              <el-input v-model="form.ruleValue26" class="input" />
              <span class="text">局 </span>
              <el-input v-model="form.ruleValue27" class="input" />
              <span class="text">%</span>
              <el-checkbox v-model="form.ruleValue28" true-label="2" false-label="1"
                class="text ml-10">跳出本靴</el-checkbox>
            </el-col>
          </el-row>

          <el-row>
            <el-col>
              <el-table ref="layerData" :data="layerData" tooltip-effect="dark" border style="width:915px">
                <el-table-column type="index" align="center" width="42" />
                <el-table-column prop="value1" label="基码" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value1" />
                  </template>
                </el-table-column>
                <el-table-column prop="value2" label="净输(口)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value2" />
                  </template>
                </el-table-column>
                <el-table-column prop="value3" label="目标利润率(%)" align="center" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value3" />
                  </template>
                </el-table-column>
                <el-table-column prop="value4" label="净赢(口)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value4" />
                  </template>
                </el-table-column>
                <el-table-column prop="value5" label="只打(口)" align="center" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value5" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="110">
                  <template slot-scope="scope">
                    <el-button icon="el-icon-plus" type="text" class="ml-5 mr-5"
                      @click="handleAddLayerData(scope.$index)" />
                    <el-button icon="el-icon-delete" type="text" class="ml-5 mr-5"
                      @click="handleDeleteLayerData(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <!--保存 -->
      <div>
        <el-row>
          <el-col>
            <el-button class="save" type="primary" @click="handleSave">保存</el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>

<script>
  import { getByRuleNo, add } from '@/api/setting/gameRule'
  export default {
    name: 'GameSetting',
    data() {
      return {
        tabName: 'first',
        agentParam: {},
        ruleNo: 'FIVE_BASE_SETTING',
        form: {
          id: null,
          ruleNo: 'FIVE_BASE_SETTING',
          ruleName: '五码回头设置',
          ruleType: '',
          ruleValue: '',
          ruleValue2: '',
          ruleValue3: '',
          ruleValue4: '',
          ruleValue5: '',
          ruleValue6: '',
          ruleValue7: '',
          ruleValue8: '0',
          ruleValue9: '1',
          ruleValue10: '2',
          ruleValue11: '2',
          ruleValue12: '1',
          ruleValue13: '1',
          ruleValue14: '1',
          ruleValue15: '1',
          ruleValue16: '',
          ruleValue17: '1',
          ruleValue18: '',
          ruleValue19: '',
          ruleValue20: '',
          ruleValue21: '2',
          ruleValue22: '',
          ruleValue23: '',
          ruleValue24: '',
          ruleValue25: '',
          ruleValue26: '',
          ruleValue27: '',
          ruleValue28: '',
          ruleValue29: '2',
          createBy: this.$store.getters.userId,
          modifiedBy: this.$store.getters.userId
        },
        cardQtyEnums: [{ label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
        roadTypeEnums: [{ label: '大路', value: '1' }, { label: '大眼仔', value: '2' }, { label: '小路', value: '3' }, { label: '蟑螂路', value: '4' }],
        equalEnums: [{ label: '大于', value: '1' }, { label: '等于', value: '2' }, { label: '小于', value: '3' }],
        cardsEnums: ['A', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'],
        bpData: [],
        chipData: [],
        tempChipData: [],
        onlyUpdate: false,
        layerData: [],
      }
    },
    /*
    系统设置：SYS_SETTING
    系统生路：SYS_GEN_ROAD
    形态设置：SHAPE
    比例设置：RATE
    点数设置：POINT
    牌数设置：CARD_POINT
    牌型设置：CARD_TYPE
    胜负设置：WIN_LOSE
    验证选项：VALID_OPTION
    */
    watch: {
      // 'form.ruleValue10': 'handleChipData',
      'form.ruleValue12': 'handleBpData'
    },
    created() {
      this.form.ruleNo = this.ruleNo
      this.handleBpData()
      // this.handleChipData()
      this.handleRuleNo()
      this.handleAddLayerData()
    },
    methods: {
      handleRuleValue() {
        // this.form.ruleValue15 = ''
        if (this.form.ruleValue13 && this.form.ruleValue14) {
          this.form.ruleValue15 = (parseFloat(this.form.ruleValue13) / parseFloat(this.form.ruleValue14)).toFixed(0)
        }
      },
      handleAddChipData(index) {
        console.log('index.........................' + index)
        var item = { value1: '1', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        this.chipData.splice(index + 1, 0, item)
      },
      handleDeleteChipData(index) {
        if (this.chipData.length > 1) {
          this.chipData.splice(index, 1)
        }
      },
      handleAddLayerData(index) {
        console.log('index.........................' + index)
        var item = { value1: '1', value2: '', value3: '', value4: '' }
        this.layerData.splice(index + 1, 0, item)
      },
      handleDeleteLayerData(index) {
        if (this.layerData.length > 1) {
          this.layerData.splice(index, 1)
        }
      },
      handleAddBpData(index) {
        console.log('index.........................' + index)
        var item = { value1: 'B', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
        this.bpData.splice(index + 1, 0, item)
      },
      handleDeleteBpData(index) {
        if (this.chipData.length > 1) {
          this.bpData.splice(index, 1)
        }
      },
      handleBpData() {
        this.bpData = []
        var item
        if (this.form.ruleValue12 === '1') {
          item = { value1: 'B', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'P', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'P', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
        } else {
          item = { value1: 'P', value2: 'P', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'B', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'P', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'P', value2: 'P', value3: 'B', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
          item = { value1: 'B', value2: 'B', value3: 'P', value4: '', value5: '', value6: '' }
          this.bpData.push(item)
        }
      },
      handleChipData() {
        if (this.chipData) {
          this.tempChipData = JSON.parse(JSON.stringify(this.chipData))
        }
        this.chipData = this.handleChipLevel()
        var length = this.chipData.length
        if (length > this.tempChipData.length) {
          length = this.tempChipData.length
        }
        for (var index = 0; index < length; index++) {
          this.chipData[index].value1 = this.tempChipData[index].value1
          this.chipData[index].value2 = this.tempChipData[index].value2
        }
      },
      handleChipLevel() {
        var item
        var chipData = []
        item = { value1: '1', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        item = { value1: '3', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        item = { value1: '7', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        item = { value1: '5', value2: '10', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        item = { value1: '6', value2: '12', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        item = { value1: '8', value2: '16', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        item = { value1: '10', value2: '20', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
        chipData.push(item)
        if (this.form.ruleValue10 === '2' || this.form.ruleValue10 === '3') {
          item = { value1: '8', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '24', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '56', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '40', value2: '80', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '48', value2: '96', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '64', value2: '128', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '80', value2: '160', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
        }
        if (this.form.ruleValue10 === '3') {
          item = { value1: '64', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '192', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '448', value2: '', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '320', value2: '640', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '384', value2: '768', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '512', value2: '1024', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
          item = { value1: '640', value2: '1280', value3: '', value4: '', value5: '', value6: '', value7: '', value8: '', value9: '', value10: '' }
          chipData.push(item)
        }
        return chipData
      },
      handleRuleNo() {
        getByRuleNo(this.ruleNo, this.$store.getters.userId).then(r => {
          if (r.data && !this.onlyUpdate) {
            this.form = r.data
            setTimeout(() => {
              if (this.form.ruleValue18) {
                this.layerData = JSON.parse(this.form.ruleValue18)
              }
              if (this.form.ruleValue19) {
                this.bpData = JSON.parse(this.form.ruleValue19)
              }
              if (this.form.ruleValue20) {
                this.chipData = JSON.parse(this.form.ruleValue20)
              }
            }, 100)
          }
        })
      },
      handleSave() {
        this.form.createBy = this.$store.getters.userId
        this.form.modifiedBy = this.$store.getters.userId
        if (this.layerData.length) {
          this.form.ruleValue18 = JSON.stringify(this.layerData)
        }
        if (this.bpData.length) {
          this.form.ruleValue19 = JSON.stringify(this.bpData)
        }
        if (this.chipData.length) {
          this.form.ruleValue20 = JSON.stringify(this.chipData)
        }
        if (this.onlyUpdate) {
          this.onlyUpdate = false
          this.$emit('submit', this.form)
        } else {
          add(this.form).then(r => {
            if (r.code === 20000) {
              this.handleRuleNo()
              this.$message.success('保存成功')
            } else {
              this.$message.success('保存失败')
            }
          })
        }
      },
      setData(data) {
        this.form = data
        this.onlyUpdate = true
        setTimeout(() => {
          if (this.form.ruleValue18) {
            this.layerData = JSON.parse(this.form.ruleValue18)
          }
          if (this.form.ruleValue19) {
            this.bpData = JSON.parse(this.form.ruleValue19)
          }
          if (this.form.ruleValue20) {
            this.chipData = JSON.parse(this.form.ruleValue20)
          }
        }, 100)
      }
    }
  }
</script>

<style>

  .game-setting .el-table--small th,
  .game-setting .el-table--small td {
    padding: 0;
  }

  .game-setting .el-table--small td .el-input__inner {
    border-radius: 0;
    border: 0 solid #DCDFE6;
    text-align: center;
  }

  .game-setting .el-table--small th,
  .game-setting .el-table--small td {
    padding: 0;
  }

  .game-setting .el-table .cell {
    padding: 0;
  }

  .game-setting th,
  .game-setting td {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
  }

  .game-setting .el-row {
    margin-bottom: 10px;
  }

  .game-setting .input {
    width: 100px;
  }

  .game-setting .save {
    margin-left: 25px;
    width: 100px;
  }

  .game-setting .text {
    margin-right: 10px;
  }

  .game-setting .el-checkbox {
    margin-right: 10px;
  }
</style>
