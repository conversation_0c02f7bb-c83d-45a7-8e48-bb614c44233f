<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="12">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">{{ $t('operate.add') }}</el-button>
          <el-button v-show="this.delete" size="mini" type="danger" icon="el-icon-delete" @click="handleDeleteClick">{{ $t('operate.delete') }}</el-button>
          <el-button v-show="edit" size="mini" type="primary" icon="el-icon-edit" @click="handleEditClick">{{ $t('operate.edit') }}</el-button>
          <!--   -->
          <el-button v-show="block" size="mini" type="primary" icon="el-icon-video-pause" @click="handleBlockClick">{{ $t('operate.block') }}</el-button>
          <!-- -->
          <el-button v-show="unblock" size="mini" type="primary" icon="el-icon-video-play" @click="handleUnblockClick">{{ $t('operate.unblock') }}</el-button>
        </el-button-group>
      </el-col>
      <el-col :span="12">
        <el-row type="flex" justify="end">
          <el-input v-model="param.keywords" style="width:184px" :placeholder="$t('countryInfo.placeholder.keywords')" clearable />
          <el-select v-model="param.blockStatus" style="width:184px" :placeholder="$t('countryInfo.placeholder.blockStatus')" clearable>
            <el-option
              v-for="item in blockStatusEnums"
              :key="item.type"
              :label="$t(item.value)"
              :value="item.type"
            />
          </el-select>
          <el-button icon="fa fa-search" type="primary" @click="filterData" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="code" align="center" :label="$t('countryInfo.code')" />
      <el-table-column prop="name" align="center" :label="$t('countryInfo.name')" />
      <el-table-column prop="nameEn" align="center" :label="$t('countryInfo.nameEn')" />
      <el-table-column align="center" :label="$t('countryInfo.block')">
        <template slot-scope="{row}">
          <el-tag v-if="row.isBlocked" type="danger">
            {{ $t('blockStatusEnum.blocked') }}
          </el-tag>
          <el-tag v-else type="success">
            {{ $t('blockStatusEnum.unblocked') }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('countryInfo.info')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="25%">
      <el-form ref="dataForm" :model="object" label-width="110px" :disabled="ifView" :rules="buttonRules">
        <el-form-item :label="$t('countryInfo.code')" prop="code">
          <el-input v-model="object.code" auto-complete="off" />
        </el-form-item>
        <el-form-item :label="$t('countryInfo.name')" prop="name">
          <el-input v-model="object.name" auto-complete="off" />
        </el-form-item>
        <el-form-item :label="$t('countryInfo.nameEn')" prop="nameEn">
          <el-input v-model="object.nameEn" auto-complete="off" />
        </el-form-item>
        <el-form-item :label="$t('countryInfo.block')" required>
          <el-switch v-model="object.isBlocked" :active-text="$t('countryInfo.yes')" :inactive-text="$t('countryInfo.no')" :active-value="0" :inactive-value="1" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" :loading="loadingTags.add" @click="createData">{{ $t('operate.save') }}</el-button>
        <el-button v-else type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.edit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listCountry, addCountry, deleteCountry, updateCountry, blockCountry, unblockCountry } from '@/api/setting/country'
import { deepClone } from '@/utils/transferUtil'
import { BlockStatusEnum } from '@/enums/blockEnum'
export default {
  name: 'Country',
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    },
    block: {
      type: Boolean,
      default: false
    },
    unblock: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      objectList: [],
      multipleSelection: [],
      systemOptions: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {
        buttonNo: '',
        buttonName: ''
      },
      formLabelWidth: '80px',
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      },
      buttonRules: {
        code: [{ required: true, trigger: 'blur', message: this.$t('countryInfo.validateMsg.code') }],
        name: [{ required: true, trigger: 'blur', message: this.$t('countryInfo.validateMsg.name') }],
        nameEn: [{ required: true, trigger: 'blur', message: this.$t('countryInfo.validateMsg.nameEn') }]
      },
      param: {}
    }
  },
  computed: {
    blockStatusEnums() {
      return BlockStatusEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    filterData() {
      this.currentPage = 1
      this.getData()
    },
    getData() {
      this.param.pageNo = this.currentPage
      this.param.pageSize = this.pageSize
      listCountry(this.param).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogFormVisible = true
      this.object = { status: 1, sortNo: 10 }
      this.ifView = false
      this.dialogStatus = 'create'
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.add = true
          addCountry(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.save') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    handleEditClick() {
      if (this.multipleSelection.length > 0) {
        this.loadingTags.edit = false
        this.dialogFormVisible = true
        this.object = deepClone(this.multipleSelection[0])
        this.ifView = false
        this.dialogStatus = 'edit'
      } else {
        this.$message({
          message: this.$t('countryInfo.msg.editData'),
          type: 'warning'
        })
      }
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.edit = true
          updateCountry(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    removeData() {
      if (this.ids.length > 0) {
        deleteCountry(this.ids).then(() => {
          this.getData()
          this.$message({
            message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
            type: 'success'
          })
        })
      } else {
        this.$message({
          message: this.$t('jetton.msg.deleteData'),
          type: 'warning'
        })
      }
    },
    blockData() {
      if (this.ids.length > 0) {
        blockCountry(this.ids).then(() => {
          this.getData()
          this.$message({
            message: this.$t('operate.block') + ' ' + this.$t('operate.message.success'),
            type: 'success'
          })
        })
      } else {
        this.$message({
          message: this.$t('countryInfo.msg.blockData'),
          type: 'warning'
        })
      }
    },
    unblockData() {
      if (this.ids.length > 0) {
        unblockCountry(this.ids).then(() => {
          this.getData()
          this.$message({
            message: this.$t('operate.unblock') + ' ' + this.$t('operate.message.success'),
            type: 'success'
          })
        })
      } else {
        this.$message({
          message: this.$t('countryInfo.msg.unblockData'),
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleBlockClick() {
      this.$confirm(this.$t('operate.block') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.blockData()
      })
    },
    handleUnblockClick() {
      this.$confirm(this.$t('operate.unblock') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.unblockData()
      })
    },
    handleDeleteClick() {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.removeData()
      })
    }
  }
}
</script>
