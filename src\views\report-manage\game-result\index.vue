<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-date-picker
            v-model="memberParam.date"
            type="daterange"
            align="right"
            unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')"
            :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions"
          />
          <!-- <el-select v-model="memberParam.gameType" clearable :placeholder="$t('betInfo.placeholder.gameType')">
            <el-option
              v-for="item in gameTypeEnums"
              :key="item.type"
              :label="$t(item.label)"
              :value="item.type"
            />
          </el-select> -->
          <el-select v-model="memberParam.tableId" clearable :placeholder="$t('macthInfo.placeholder.table')">
            <el-option
              v-for="item in tableList"
              :key="item.id"
              :label="item.tableNo"
              :value="item.id"
            >
              <span style="float: left">{{ item.tableNo }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.tableName }}</span>
            </el-option>
          </el-select>
          <el-input v-model="memberParam.gameNo" style="width:184px" :placeholder="$t('betInfo.placeholder.gameNo')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="gameType" align="center" :label="$t('memberSettle.gameType')" :formatter="gameTypeFilter" />
      <el-table-column prop="tableInfo.tableNo" align="center" :label="$t('betInfo.tableNo')">
        <template slot-scope="scope">
          <div> {{ scope.row.tableInfo.tableNo }}({{ scope.row.tableInfo.tableName }}) </div>
        </template>
      </el-table-column>
      <el-table-column prop="gameMatch.matchNo" align="center" :label="$t('betInfo.shoeNo')" />
      <el-table-column prop="gameNo" align="center" :label="$t('betInfo.gameNo')" />
      <el-table-column prop="gameResult" align="center" :label="$t('betInfo.cardType')">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="right">
            <div class="container">
              <div v-if="scope.row.gameType == 1 || scope.row.gameType == 2" class="mipai-box">
                <div class="zhuang-box">
                  <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.bankerA, 0) + ' on'" />
                  <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.bankerB, 1) + ' on'" />
                  <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.bankerC, 2) + ' on'" />
                  <div class="total-box on">{{ scope.row.bankerPoint }}</div>
                  <div class="title-box on">
                    {{ $t('game.bankerAbbr') }}
                  </div>
                  <div class="notice-box" />
                </div>
                <div class="xian-box">
                  <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.playerA, 0) + ' on'" />
                  <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.playerB, 1) + ' on'" />
                  <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.playerC, 2) + ' on'" />
                  <div class="total-box on">{{ scope.row.playerPoint }}</div>
                  <div class="title-box on ">
                    {{ $t('game.playerAbbr') }}
                  </div>
                  <div class="notice-box" />
                </div>
                <div class="clear" />
              </div>
              <div v-else class="mipai-box mipai-box2">
                <div class="zhuang-box">
                  <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.bankerA, 0) + ' on'" />
                  <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.bankerB, 1) + ' on'" />
                  <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.bankerC, 2) + ' on'" />
                  <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.bankerD, 3) + ' on'" />
                  <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.bankerE, 4) + ' on'" />
                  <div class="total-box on">{{ getBull(scope.row.bankerPoint) }}</div>
                  <div class="title-box on">
                    {{ $t('game.bankerAbbr') }}
                  </div>
                  <div class="notice-box" />
                </div>
                <div class="xian-box">
                  <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.playerA, 0) + ' on'" />
                  <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.playerB, 1) + ' on'" />
                  <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.playerC, 2) + ' on'" />
                  <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.playerD, 3) + ' on'" />
                  <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.playerE, 4) + ' on'" />
                  <div class="total-box on">{{ getBull(scope.row.playerPoint) }}</div>
                  <div class="title-box on ">
                    {{ $t('game.playerAbbr') }}
                  </div>
                  <div class="notice-box" />
                </div>
                <div class="xian-box">
                  <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.player2A, 0) + ' on'" />
                  <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.player2B, 1) + ' on'" />
                  <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.player2C, 2) + ' on'" />
                  <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.player2D, 3) + ' on'" />
                  <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.player2E, 4) + ' on'" />
                  <div class="total-box on">{{ getBull(scope.row.player2Point) }}</div>
                  <div class="title-box on ">
                    {{ $t('game.playerAbbr') }}
                  </div>
                  <div class="notice-box" />
                </div>
                <div class="xian-box">
                  <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.player3A, 0) + ' on'" />
                  <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.player3B, 1) + ' on'" />
                  <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.player3C, 2) + ' on'" />
                  <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.player3D, 3) + ' on'" />
                  <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.player3E, 4) + ' on'" />
                  <div class="total-box on">{{ getBull(scope.row.player3Point) }}</div>
                  <div class="title-box on ">
                    {{ $t('game.playerAbbr') }}
                  </div>
                  <div class="notice-box" />
                </div>
                <div class="clear" />
              </div>
            </div>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium">{{ $t('betInfo.cardsView') }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="gameResult" align="center" :label="$t('betInfo.cardResult')" :formatter="gameResultFormat" />
      <el-table-column prop="createDt" align="center" :label="$t('macthInfo.gameStartTime')" :formatter="dateTimeFormat" width="164" />
      <el-table-column v-if="this.delete && edit" fixed="right" align="center" :label="$t('agentForm.operation')" width="244">
        <template slot-scope="scope">
          <el-button-group>
            <el-button plain size="mini" @click="handleEditClick(scope.row)">
              {{ $t('operate.modifyResult') }}
            </el-button>
            <el-button plain size="mini" @click="handleDeleteClick(scope.row)">
              {{ $t('operate.deleteOne') }}
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('operate.modifyResult')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="40%">
      <el-form ref="dataForm" :model="object" label-width="80px">
        <el-form-item :label="$t('betInfo.tableNo')">
          <el-input v-model="object.tableNo" readonly />
        </el-form-item>
        <el-form-item :label="$t('betInfo.gameNo')">
          <el-input v-model="object.gameNo" readonly />
        </el-form-item>
        <el-form-item :label="$t('game.winner')">
          <el-radio-group v-model="object.result1">
            <el-radio :label="1"> {{ $t('game.banker') }}</el-radio>
            <el-radio :label="2"> {{ $t('game.player') }}</el-radio>
            <el-radio :label="3"> {{ $t('game.tie') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('game.pair')">
          <el-radio-group v-model="object.result2">
            <el-radio :label="1"> {{ $t('game.bankerPair') }}</el-radio>
            <el-radio :label="2"> {{ $t('game.playerPair') }}</el-radio>
            <el-radio :label="3"> {{ $t('game.allPair') }}</el-radio>
            <el-radio :label="4"> {{ $t('game.noPair') }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.save') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listGameResult, deleteGame, updateGameResult } from '@/api/game/game'
import { getGameTypeEnum, GameTypeEnum } from '@/enums/setting.js'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber } from '@/utils/formatter'
import { gameResultFilter } from '@/filters/gameResult'
import { listAllTableInfo } from '@/api/setting/tableInfo'
// import { deepClone } from '@/utils/transferUtil'
import { BaccaratResult1Enum, BaccaratResult2Enum } from '@/enums/gameResult'
export default {
  name: 'MemberList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      object: {
        result1: 1,
        result2: 1,
        tableInfo: {}
      },
      loadingTags: {
        edit: false
      },
      memberParam: {
        memberType: 3
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      tableList: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ]),
    gameTypeEnums() {
      return GameTypeEnum
    },
    baccaratResult1Enums() {
      return BaccaratResult1Enum
    },
    baccaratResult2Enums() {
      return BaccaratResult2Enum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.innitAllTableInfo()
    this.listTableData()
  },
  methods: {
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.object = { result1: '', result2: '', gameNo: '', tableNo: '' }
      const results = val.gameResult.split('|')
      this.object.id = val.id
      this.object.result1 = Number(results[0])
      this.object.result2 = Number(results[1])
      this.object.gameNo = val.gameNo
      this.object.tableNo = val.tableInfo.tableNo
      this.object.matchId = val.matchId
      this.dialogStatus = 'edit'
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.edit = true
          this.object.gameResult = this.object.result1 + '|' + this.object.result2
          updateGameResult(this.object).then(r => {
            if (r.code === 20000) {
              this.dialogFormVisible = false
              this.loadingTags.edit = false
              this.listTableData()
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
                type: 'success'
              })
            } else {
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.fail'),
                type: 'error'
              })
            }
          })
        }
      })
    },
    handleDeleteClick(val) {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.removeData(val.id)
      })
    },
    removeData(id) {
      deleteGame(id).then(() => {
        this.listTableData()
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    getBull(point) {
      if (point === 0) {
        return this.$t('bull.noBull')
      } else if (point >= 10) {
        return this.$t('bull.bull')
      } else {
        return point
      }
    },
    innitAllTableInfo() {
      listAllTableInfo().then(r => {
        this.tableList = r.data
      })
    },
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      listGameResult(this.memberParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    gameResultFormat: function(row, column) {
      const items = gameResultFilter(row.gameType, row.gameResult)
      var tempItems = []
      for (var i = 0; i < items.length; i++) {
        tempItems.push(this.$t(items[i]))
      }
      return tempItems.join(',')
    },
    cardsFormat: function(row, column) {
      return gameResultFilter(row.gameType, row.gameResult)
    },
    gameCardsFiter(gameTrans) {
      var chipItems = []
      var bankerCardDesc = ''
      if (gameTrans.bankerA) {
        chipItems.push(gameTrans.bankerA)
      }
      if (gameTrans.bankerB) {
        chipItems.push(gameTrans.bankerB)
      }
      if (gameTrans.bankerC) {
        chipItems.push(gameTrans.bankerC)
      }
      if (gameTrans.bankerD) {
        chipItems.push(gameTrans.bankerD)
      }
      if (gameTrans.bankerE) {
        chipItems.push(gameTrans.bankerE)
      }
      bankerCardDesc = chipItems.join(',')
      return bankerCardDesc
    },
    getCardType(card, index) {
      if (!card) {
        return ''
      }
      var cardPoint = Number(card.substr(2, 2))
      var point = cardPoint % 13
      if (point === 0) {
        point = 13
      }
      var suitStr = ''
      var suit = Math.ceil(cardPoint / 13)

      if (suit === 1) suitStr = 's'
      else if (suit === 2) suitStr = 'h'
      else if (suit === 3) suitStr = 'd'
      else if (suit === 4) suitStr = 'c'
      return 'type' + suitStr + point
    },
    gameChipFormat(row, column) {
      return this.gameChipFiter(row)
    },
    gameTypeFilter(row, column) {
      return this.$t(getGameTypeEnum(row.gameType).label)
    },
    gameChipFiter(gameTrans) {
      var gameResultDesc = ''
      var chipItems = []
      switch (Number(gameTrans.gameType)) {
        case 1:
          if (gameTrans.bankerAmout) {
            chipItems.push(this.$t('game.banker') + ': ' + gameTrans.bankerAmout)
          }
          if (gameTrans.bankerPairAmout) {
            chipItems.push(this.$t('game.bankerPair') + ': ' + gameTrans.bankerPairAmout)
          }
          if (gameTrans.bankerSuperAmout) {
            chipItems.push(this.$t('game.luckSix') + ': ' + gameTrans.bankerSuperAmout)
          }
          if (gameTrans.playerAmout) {
            chipItems.push(this.$t('game.player') + ': ' + gameTrans.playerAmout)
          }
          if (gameTrans.playerPairAmout) {
            chipItems.push(this.$t('game.playerPair') + ': ' + gameTrans.playerPairAmout)
          }
          if (gameTrans.tieAmout) {
            chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
          }
          gameResultDesc = chipItems.join(',')
          break
        case 2:
          if (gameTrans.bankerAmout) {
            chipItems.push(this.$t('game.tiger') + ': ' + gameTrans.bankerAmout)
          }
          if (gameTrans.bankerPairAmout) {
            chipItems.push(this.$t('game.pair') + ': ' + gameTrans.bankerPairAmout)
          }
          if (gameTrans.tieAmout) {
            chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
          }
          if (gameTrans.playerAmout) {
            chipItems.push(this.$t('game.dragon') + ': ' + gameTrans.playerAmout)
          }
          gameResultDesc = chipItems.join(',')
          break
        case 3:
          if (gameTrans.playerAmout) {
            chipItems.push(this.$t('game.player1Equal') + ': ' + gameTrans.playerAmout)
          }
          if (gameTrans.playerPairAmout) {
            chipItems.push(this.$t('game.player1Double') + ': ' + gameTrans.playerPairAmout)
          }
          if (gameTrans.playerSuperAmout) {
            chipItems.push(this.$t('game.player1Super') + ': ' + gameTrans.playerSuperAmout)
          }
          if (gameTrans.player2Amout) {
            chipItems.push(this.$t('game.player2Equal') + ': ' + gameTrans.player2Amout)
          }
          if (gameTrans.player2PairAmout) {
            chipItems.push(this.$t('game.player2Double') + ': ' + gameTrans.player2PairAmout)
          }
          if (gameTrans.player2SuperAmout) {
            chipItems.push(this.$t('game.player2Super') + ': ' + gameTrans.player2SuperAmout)
          }
          if (gameTrans.player3Amout) {
            chipItems.push(this.$t('game.player3Equal') + ': ' + gameTrans.player3Amout)
          }
          if (gameTrans.player3PairAmout) {
            chipItems.push(this.$t('game.player3Double') + ': ' + gameTrans.player3PairAmout)
          }
          if (gameTrans.player3SuperAmout) {
            chipItems.push(this.$t('game.player3Super') + ': ' + gameTrans.player3SuperAmout)
          }
          gameResultDesc = chipItems.join(',')
          break
      }
      return gameResultDesc
    }
  }
}
</script>
<style lang="scss" scoped>
  /*开牌 */
  .container .mipai-box {
    top: 0%;
    left: 0%;
    width: 175px;
    height: 125px;
    position: absolute;
    flex-direction: column;
    border-radius: .5rem;
    z-index: 999;
    padding-top:1%;
    background: #131313;
    opacity: 0.9;
    border-radius: 0.5rem;
  }
  .container .mipai-box2 {
    width: 250px;
    height: 250px;
  }
  .container .mipai-box.active {
    display: block;
    animation: bounce02 1.5s;
  }
  .container .mipai-box.active-a {
    display: block !important;
    animation: bounce03 1.5s;
  }
  .container .mipai-box.inactive {
    display: none;
  }
  .container .mipai-box .xian-box, .container .mipai-box .zhuang-box{
    height: 60px;
    position: relative;
    display: flex;
    border-bottom: 1px solid #222222;
  }
  .container .mipai-box .total-box {
    font-size: 16px;
    position: absolute;
    left: 5px;
    top: 30px;
    padding: 0 5px 0 5px;
    text-align: center;
    border-radius: 50%;
  }
 .container .mipai-box .title-box {
    position: absolute;
    font-size: 16px;
    top: 5px;
    left: 5px;
    text-align: center;
  }
  .container .mipai-box .zhuang-box .total-box, .container .mipai-box .long-box .total-box {
    color: #ffffff;
    background-color: #c1182e;
  }
  .container .mipai-box .xian-box .total-box, .container .mipai-box .hu-box .total-box {
    color: #ffffff;
    background-color: #2b4be9;
  }
  .container .mipai-box .zhuang-box .title-box, .container .mipai-box .long-box .title-box {
    color: #c1182e;
  }

  .container .mipai-box .xian-box .title-box, .container .mipai-box .hu-box .title-box, .container .mipai-box .feng-box .title-box {
    color: #2b4be9;
  }
  .container .mipai-box .smallcard {
    position: absolute;
  }
  .container .mipai-box .smallcard.on {
    display: block;
  }
  .container .mipai-box .smallcard.card1 {
    top: 10px;
    height: 40px;
    left: 40px;
    width: 30px;
  }
  .container .mipai-box .smallcard.card2 {
    top: 10px;
    height: 40px;
    left: 80px;
    width: 30px;
  }
  .container .mipai-box .smallcard.card3 {
    top: 20px;
    height: 30px;
    left: 120px;
    width: 40px;
  }
  .container .mipai-box2 .smallcard.card3 {
    top: 10px;
    height: 40px;
    left: 120px;
    width: 30px;
  }
  .container .mipai-box .smallcard.card4 {
    top: 10px;
    height: 40px;
    left: 160px;
    width: 30px;
  }
  .container .mipai-box .smallcard.card5 {
    top: 10px;
    height: 40px;
    left: 200px;
    width: 30px;
  }
</style>
