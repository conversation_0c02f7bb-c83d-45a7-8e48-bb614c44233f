<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-date-picker
            v-model="memberParam.date"
            type="daterange"
            align="right"
            unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')"
            :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions"
          />
          <el-select v-model="memberParam.tableId" clearable :placeholder="$t('macthInfo.placeholder.table')">
            <el-option
              v-for="item in tableList"
              :key="item.id"
              :label="item.tableNo"
              :value="item.id"
            >
              <span style="float: left">{{ item.tableNo }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.tableName }}</span>
            </el-option>
          </el-select>
          <el-input v-model="memberParam.matchNo" style="width:184px;margin-left:5px" :placeholder="$t('macthInfo.placeholder.gameNo')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="tableInfo.tableNo" align="center" :label="$t('macthInfo.tableInfo')">
        <template slot-scope="scope">
          <div> {{ scope.row.tableInfo.tableNo }}({{ scope.row.tableInfo.tableName }}) </div>
        </template>
      </el-table-column>
      <el-table-column prop="gameMatch.matchNo" align="center" :label="$t('macthInfo.shoeNo')" />
      <el-table-column prop="gameNo" align="center" :label="$t('macthInfo.gameNo')" />
      <el-table-column prop="createDt" align="center" :label="$t('macthInfo.gameStartTime')" :formatter="dateTimeFormat" />
      <el-table-column prop="gameResult" align="center" :label="$t('macthInfo.cardsResult')" :formatter="gameResultFormat" />
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
import { listGameResult } from '@/api/game/game'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { gameResultFilter } from '@/filters/gameResult'
import { listAllTableInfo } from '@/api/setting/tableInfo'
export default {
  name: 'GameList',
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        memberType: 3,
        matchNo: '',
        tableId: '',
        date: ''
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      tableList: [],
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ])
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.innitAllTableInfo()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      listGameResult(this.memberParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    innitAllTableInfo() {
      listAllTableInfo().then(r => {
        this.tableList = r.data
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    gameResultFormat: function(row, column) {
      const items = gameResultFilter(row.gameType, row.gameResult)
      var tempItems = []
      for (var i = 0; i < items.length; i++) {
        tempItems.push(this.$t(items[i]))
      }
      return tempItems.join(',')
    }
  }
}
</script>
