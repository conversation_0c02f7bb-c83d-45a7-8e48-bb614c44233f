<template>
  <el-row>
    <el-col :span="12">
      <el-select v-model="oneLevelCategoryId" :disabled="canEdit" placeholder="一级分类" filterable @change="getTwoLevelCategory">
        <el-option
          v-for="item in oneLevelCategoryList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
    </el-col>
    <el-col :span="12">
      <el-select v-model="twoLevelCategoryId" :disabled="canEdit" placeholder="二级分类" filterable @change="selectCategory">
        <el-option
          v-for="item in twoLevelCategoryList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
    </el-col>
  </el-row>
</template>
<script>
import { listCategory, listOneLevelCategory } from '@/api/content/articleCategory'
export default {
  props: {
    oneLevelCategory: {
      type: Number,
      default: 0,
      required: false
    },
    twoLevelCategory: {
      type: Number,
      default: 0,
      required: false
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      oneLevelCategoryId: this.oneLevelCategory,
      twoLevelCategoryId: this.twoLevelCategory,
      canEdit: this.disabled,
      oneLevelCategoryList: [],
      twoLevelCategoryList: []
    }
  },
  watch: {
    oneLevelCategory() {
      this.oneLevelCategoryId = this.oneLevelCategory
      this.setOneLevelCategory()
    },
    twoLevelCategory() {
      this.twoLevelCategoryId = this.twoLevelCategory
      this.setTwoLevelCategory(this.oneLevelCategory)
    },
    disabled() {
      this.canEdit = this.disabled
    }
  },
  mounted() {
    this.setOneLevelCategory()
    this.setTwoLevelCategory(this.oneLevelCategory)
  },
  methods: {
    setOneLevelCategory() {
      this.oneLevelCategoryList = []
      if (this.oneLevelCategoryId === 0) {
        this.oneLevelCategoryId = ''
      }
      listOneLevelCategory().then(r => {
        this.oneLevelCategoryList = r.data
      })
    },
    setTwoLevelCategory(val) {
      this.twoLevelCategoryList = []
      if (this.twoLevelCategory === 0) {
        this.twoLevelCategoryId = ''
      } else {
        const param = {}
        param.parentId = val
        listCategory(param).then(r => {
          this.twoLevelCategoryList = r.data
        })
      }
    },
    getTwoLevelCategory(val) {
      this.twoLevelCategoryList = []
      this.twoLevelCategoryId = ''
      const param = {}
      param.parentId = val
      listCategory(param).then(r => {
        this.twoLevelCategoryList = r.data
      })
      this.selectCategory()
    },
    selectCategory() {
      if (!this.twoLevelCategoryId) {
        this.$emit('getSelectedCategory', this.oneLevelCategoryId, 0)
      } else {
        this.$emit('getSelectedCategory', this.oneLevelCategoryId, this.twoLevelCategoryId)
      }
    }
  }
}
</script>
