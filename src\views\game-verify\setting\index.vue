<template>
  <div class="app-container game-setting">
    <el-form>
      <!--系统设置 -->
      <div v-if="ruleNo==='SYS_SETTING'">
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue" true-label="1" false-label="2">显示发牌过程</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue2" true-label="1" false-label="2">每靴开牌时削牌</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue3" true-label="1" false-label="2">每口牌前要削1张牌</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">每靴牌切尾牌(50-60)</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue5" true-label="1" false-label="2">牌路不显示和局</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue6" true-label="1" false-label="2">
              每靴使用牌数
              <el-select v-model="form.ruleValue7" clearable placeholder=" " class="input" :disabled="form.ruleValue6!='1'">
                <el-option
                  v-for="item in cardQtyEnums"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> 副牌
            </el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue8" true-label="1" false-label="2">
              投注基码
              <el-input
                v-model="form.ruleValue9"
                class="input"
                :disabled="form.ruleValue8!='1'"
              />
            </el-checkbox>
          </el-col>
        </el-row>
      </div>
      <!--系统生路 -->
      <div v-if="ruleNo==='SYS_GEN_ROAD'">
        <el-row>
          <el-col>
            <span class="text">自动生成牌路数</span>
            <el-input
              v-model="form.ruleValue"
              class="input"
            /> <span class="text">靴</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <span class="text">每靴使用牌数</span>
            <el-select v-model="form.ruleValue2" clearable placeholder=" " class="input">
              <el-option
                v-for="item in cardQtyEnums"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> <span class="text">副牌</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue3" true-label="1" false-label="2">每靴开牌时削牌</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">每口牌前要削1张牌</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue5" true-label="1" false-label="2">每靴牌切尾牌(50-60)</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue6" true-label="1" false-label="2">牌路不显示和局</el-checkbox>
          </el-col>
        </el-row>
      </div>
      <!--比率设置 -->
      <div v-if="ruleNo==='RATE'">
        <el-row>
          <el-col :span="6">
            <span class="text">当前</span>
            <el-select v-model="form.ruleValue" clearable placeholder=" " class="input">
              <el-option
                v-for="item in roadTypeEnums"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue2" true-label="1" false-label="2">庄占庄闲总数比率</el-checkbox>
                <el-select v-model="form.ruleValue3" clearable placeholder=" " class="input" :disabled="form.ruleValue2!='1'">
                  <el-option
                    v-for="item in equalEnums"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-input
                  v-model="form.ruleValue4"
                  class="input"
                  :disabled="form.ruleValue2!='1'"
                /> <span class="text">%</span>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue5" true-label="1" false-label="2">闲占庄闲总数比率</el-checkbox>
                <el-select v-model="form.ruleValue6" clearable placeholder=" " class="input" :disabled="form.ruleValue5!='1'">
                  <el-option
                    v-for="item in equalEnums"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-input
                  v-model="form.ruleValue7"
                  class="input"
                  :disabled="form.ruleValue5!='1'"
                /> <span class="text">%</span>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue8" true-label="1" false-label="2">庄局数-闲局数</el-checkbox>
                <el-select v-model="form.ruleValue9" clearable placeholder=" " class="input" :disabled="form.ruleValue8!='1'">
                  <el-option
                    v-for="item in equalEnums"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-input
                  v-model="form.ruleValue10"
                  class="input"
                  :disabled="form.ruleValue8!='1'"
                /> <span class="text">局</span>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue11" true-label="1" false-label="2">闲局数-庄局数</el-checkbox>
                <el-select v-model="form.ruleValue12" clearable placeholder=" " class="input" :disabled="form.ruleValue11!='1'">
                  <el-option
                    v-for="item in equalEnums"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-input
                  v-model="form.ruleValue13"
                  class="input"
                  :disabled="form.ruleValue11!='1'"
                /> <span class="text">局</span>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <!--点数设置 -->
      <div v-if="ruleNo==='POINT'">
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue" true-label="1" false-label="2">使用牌面点数</el-checkbox>
            <el-checkbox v-model="form.ruleValue2" true-label="1" false-label="2">使用庄闲点比较</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <span class="text">之前第</span>
            <el-select v-model="form.ruleValue3" clearable placeholder=" " class="input">
              <el-option
                v-for="index in 60"
                :key="index"
                :label="index+''"
                :value="index+''"
              />
            </el-select> <span class="text">口牌</span>
          </el-col>
          <el-col :span="12">
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">庄点数</el-checkbox>
                <el-select v-model="form.ruleValue5" clearable placeholder=" " class="input" :disabled="form.ruleValue4!='1'">
                  <el-option
                    v-for="index in 10"
                    :key="index"
                    :label="index+''"
                    :value="index+''"
                  />
                </el-select>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue6" true-label="1" false-label="2">闲点数</el-checkbox>
                <el-select v-model="form.ruleValue7" clearable placeholder=" " class="input" :disabled="form.ruleValue6!='1'">
                  <el-option
                    v-for="index in 10"
                    :key="index"
                    :label="index+''"
                    :value="index+''"
                  />
                </el-select>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <!--牌数设置 -->
      <div v-if="ruleNo==='CARD_POINT'">
        <el-row>
          <el-col :span="6">
            <span class="text">之前第</span>
            <el-select v-model="form.ruleValue" clearable placeholder=" " class="input">
              <el-option
                v-for="index in 60"
                :key="index"
                :label="index+''"
                :value="index+''"
              />
            </el-select> <span class="text">口牌</span>
          </el-col>
          <el-col :span="12">
            <el-row>
              <el-col>
                <span class="text">庄有</span>
                <el-checkbox v-model="form.ruleValue2" true-label="1" false-label="2">2张牌</el-checkbox>
                <el-checkbox v-model="form.ruleValue3" true-label="1" false-label="2">3张牌</el-checkbox>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <span class="text">闲有</span>
                <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">2张牌</el-checkbox>
                <el-checkbox v-model="form.ruleValue5" true-label="1" false-label="2">3张牌</el-checkbox>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <!--牌数设置 -->
      <div v-if="ruleNo==='CARD_TYPE'">
        <el-row>
          <el-col :span="6">
            <span class="text">之前第</span>
            <el-select v-model="form.ruleValue" clearable placeholder=" " class="input">
              <el-option
                v-for="index in 60"
                :key="index"
                :label="index+''"
                :value="index+''"
              />
            </el-select> <span class="text">口牌</span>
          </el-col>
          <el-col :span="12">
            <el-row>
              <el-col :span="2">
                <span class="text">庄</span>
              </el-col>
              <el-col :span="20">
                <el-row>
                  <el-col>
                    <el-checkbox v-model="form.ruleValue2" true-label="1" false-label="2">第一张牌</el-checkbox>
                    <el-select v-model="form.ruleValue3" clearable placeholder=" " class="input" :disabled="form.ruleValue2!='1'">
                      <el-option
                        v-for="item in cardsEnums"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>
                    <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">第二张牌</el-checkbox>
                    <el-select v-model="form.ruleValue5" clearable placeholder=" " class="input" :disabled="form.ruleValue4!='1'">
                      <el-option
                        v-for="item in cardsEnums"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>
                    <el-checkbox v-model="form.ruleValue6" true-label="1" false-label="2">第三张牌</el-checkbox>
                    <el-select v-model="form.ruleValue7" clearable placeholder=" " class="input" :disabled="form.ruleValue6!='1'">
                      <el-option
                        v-for="item in cardsEnums"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="2">
                <span class="text">闲</span>
              </el-col>
              <el-col :span="20">
                <el-row>
                  <el-col>
                    <el-checkbox v-model="form.ruleValue8" true-label="1" false-label="2">第一张牌</el-checkbox>
                    <el-select v-model="form.ruleValue9" clearable placeholder=" " class="input" :disabled="form.ruleValue8!='1'">
                      <el-option
                        v-for="item in cardsEnums"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>
                    <el-checkbox v-model="form.ruleValue10" true-label="1" false-label="2">第二张牌</el-checkbox>
                    <el-select v-model="form.ruleValue11" clearable placeholder=" " class="input" :disabled="form.ruleValue10!='1'">
                      <el-option
                        v-for="item in cardsEnums"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>
                    <el-checkbox v-model="form.ruleValue12" true-label="1" false-label="2">第三张牌</el-checkbox>
                    <el-select v-model="form.ruleValue13" clearable placeholder=" " class="input" :disabled="form.ruleValue12!='1'">
                      <el-option
                        v-for="item in cardsEnums"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <!--胜负设置 -->
      <div v-if="ruleNo==='WIN_LOSE'">
        <el-row>
          <el-col :span="6">
            <el-select v-model="form.ruleValue" clearable placeholder=" " class="input">
              <el-option
                v-for="item in roadTypeEnums"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span class="text">之前第</span>
            <el-select v-model="form.ruleValue2" clearable placeholder=" " class="input">
              <el-option
                v-for="index in 60"
                :key="index"
                :label="index+''"
                :value="index+''"
              />
            </el-select> <span class="text">口牌</span>
          </el-col>
          <el-col :span="12">
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue3" true-label="1" false-label="2">庄胜</el-checkbox>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">闲胜</el-checkbox>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <!--系统生路 -->
      <div v-if="ruleNo==='VALID_OPTION'">
        <el-row>
          <el-col>
            <span class="text">要验证的投注法组合</span>
            <el-select v-model="form.ruleValue" clearable placeholder=" " class="input">
              <el-option
                v-for="item in 20"
                :key="item+''"
                :label="'组合'+item"
                :value="item+''"
              />
            </el-select>
            <el-checkbox v-model="form.ruleValue2" true-label="1" false-label="2">买庄时要抽水5%</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue3" true-label="1" false-label="2">使用当前牌路进行验证</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue4" true-label="1" false-label="2">手工生成牌路进行验证</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue5" true-label="1" false-label="2">系统自动生成牌路验证</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <span class="text">自动牌路验证选项</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <span class="text">验证</span>
            <el-input
              v-model="form.ruleValue6"
              class="input"
            />
            <span class="text">靴牌路</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <span class="text">每靴使用</span>
            <el-select v-model="form.ruleValue2" clearable placeholder=" " class="input">
              <el-option
                v-for="item in cardQtyEnums"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span class="text">副牌</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue8" true-label="1" false-label="2">每靴开牌时削牌</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue9" true-label="1" false-label="2">每口牌前要削1张牌</el-checkbox>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-checkbox v-model="form.ruleValue10" true-label="1" false-label="2">每靴牌切尾牌(50-60)</el-checkbox>
          </el-col>
        </el-row>
      </div>
      <!--保存 -->
      <div>
        <el-row>
          <el-col>
            <el-button class="save" type="primary" @click="handleSave">保存</el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getByRuleNo, add } from '@/api/setting/gameRule'
export default {
  name: 'GameSetting',
  data() {
    return {
      agentParam: {},
      ruleNo: 'SYS_SETTING',
      form: {
        id: null,
        ruleNo: '',
        ruleName: '',
        ruleType: '',
        ruleValue: '',
        ruleValue2: '',
        ruleValue3: '',
        ruleValue4: '',
        ruleValue5: '',
        ruleValue6: '',
        ruleValue7: '',
        ruleValue8: '',
        ruleValue9: '',
        ruleValue10: '',
        ruleValue11: '',
        ruleValue12: '',
        ruleValue13: '',
        ruleValue14: '',
        ruleValue15: '',
        ruleValue16: '',
        ruleValue17: '',
        ruleValue18: '',
        ruleValue19: '',
        ruleValue20: '',
        createBy: this.$store.getters.userId,
        modifiedBy: this.$store.getters.userId
      },
      cardQtyEnums: [{ label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
      roadTypeEnums: [{ label: '大路', value: '1' }, { label: '大眼仔', value: '2' }, { label: '小路', value: '3' }, { label: '蟑螂路', value: '4' }],
      equalEnums: [{ label: '大于', value: '1' }, { label: '等于', value: '2' }, { label: '小于', value: '3' }],
      cardsEnums: ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
    }
  },
  /*
  系统设置：SYS_SETTING
  系统生路：SYS_GEN_ROAD
  形态设置：SHAPE
  比例设置：RATE
  点数设置：POINT
  牌数设置：CARD_POINT
  牌型设置：CARD_TYPE
  胜负设置：WIN_LOSE
  验证选项：VALID_OPTION
  */
  created() {
    console.log('game setting...........................')
    console.log(this.$route)
    console.log(this.$router)
    this.ruleNo = this.$route.name
    this.form.ruleNo = this.ruleNo
    this.handleRuleNo()
    //
  },
  methods: {
    handleRuleNo() {
      getByRuleNo(this.ruleNo, this.$store.getters.userId).then(r => {
        if (r.data) {
          this.form = r.data
        }
      })
    },
    handleSave() {
      this.form.createBy = this.$store.getters.userId
      this.form.modifiedBy = this.$store.getters.userId
      if (this) {
        add(this.form).then(r => {
          if (r.code === 20000) {
            this.handleRuleNo()
            this.$message.success('保存成功')
          } else {
            this.$message.success('保存失败')
          }
        })
      }
    }
  }
}
</script>

<style>
.game-setting .el-row {
  margin-bottom: 10px;
}
.game-setting .input {
  width: 100px;
}
.game-setting .save {
  margin-left: 25px;
  width: 100px;
}
.game-setting .text {
  margin-right: 10px;
}
.game-setting .el-checkbox {
  margin-right: 10px;
}
</style>
