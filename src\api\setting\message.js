import request from '@/utils/request'
export function listMessage(data) {
  return request({
    url: '/message/list',
    method: 'post',
    data
  })
}
export function addMessage(data) {
  return request({
    url: '/message/add',
    method: 'post',
    data
  })
}
export function updateMessage(data) {
  return request({
    url: '/message/update',
    method: 'post',
    data
  })
}
export function deleteMessage(ids) {
  return request({
    url: '/message/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
