<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-date-picker
            v-model="memberParam.date"
            type="daterange"
            align="right"
            unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')"
            :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions"
          />
          <el-select v-model="memberParam.pointType" clearable :placeholder="$t('pointHistory.placeholder.pointType')">
            <el-option
              v-for="item in pointTypeEnums"
              :key="item.type"
              :label="$t(item.value)"
              :value="item.type"
            />
          </el-select>
          <!-- <el-select v-model="memberParam.memberType" clearable placeholder="选择用户类型">
            <el-option
              key="2"
              label="代理"
              value="2"
            />
            <el-option
              key="3"
              label="会员"
              value="3"
            />
          </el-select> -->
          <el-input v-model="memberParam.userName" style="width:160px;margin-right:10px" :placeholder="$t('pointHistory.placeholder.account')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="createDt" :label="$t('pointHistory.operateTime')" align="center" :formatter="dateTimeFormat" width="140" />
      <el-table-column prop="user.userType" align="center" :label="$t('pointHistory.userType')" width="80">
        <template slot-scope="scope">
          {{ scope.row.user ? (scope.row.user.userType === 2 ? $t('userType.agent') : $t('userType.member')): "" }}
        </template>
      </el-table-column>
      <el-table-column prop="user.userName" align="center" :label="$t('pointHistory.userAccount')" />
      <el-table-column prop="user.nickName" align="center" :label="$t('pointHistory.nickName')" />
      <el-table-column prop="balanceBefore" align="center" :label="$t('pointHistory.balanceBefore')">
        <template slot-scope="scope">
          <div> {{ scope.row.balanceBefore| numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="point" align="center" :label="$t('pointHistory.operateAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.point| numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="balance" align="center" :label="$t('pointHistory.balanceAfter')">
        <template slot-scope="scope">
          <div> {{ scope.row.balance| numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="handleUser.userName" align="center" :label="$t('pointHistory.operateAccount')">
        <template slot-scope="scope">
          {{ scope.row.handleUser? scope.row.handleUser.userName:'' }}
        </template>
      </el-table-column>
      <el-table-column prop="pointType" align="center" :label="$t('pointHistory.operateType')">
        <template slot-scope="scope">
          <span v-if="scope.row.pointType === 1" style="color:blue;">
            {{ $t('chargeWithdrawForm.recharge') }}
            <span v-if="scope.row.pointMethod === 1">
              ({{ $t('chargeWithdrawForm.cash') }})
            </span>
            <span v-if="scope.row.pointMethod === 2">
              ({{ $t('chargeWithdrawForm.sign') }})
            </span>
            <span v-if="scope.row.pointMethod === 3">
              ({{ $t('chargeWithdrawForm.payout') }})
            </span>
          </span>
          <span v-else-if="scope.row.pointType === 2" style="color:red;">
            {{ $t('chargeWithdrawForm.withdraw') }}
          </span>
          <span v-else style="color:green;">{{ $t('operate.settlement') }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
import { listPointHistoryInfo } from '@/api/account/pointHistory'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { PointTypeEnum } from '@/enums/system'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'MemberList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        memberType: ''
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ]),
    pointTypeEnums() {
      return PointTypeEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      if (this.member.parentId > 0) {
        if (this.member.memberType === 4) {
          this.memberParam.memberId = this.member.parentId
        } else {
          this.memberParam.memberId = this.member.id
        }
      } else {
        this.memberParam.memberId = 0
      }
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      listPointHistoryInfo(this.memberParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
        this.currentPage = r.data.pageNum
      })
    },
    handlePontChangeClick(data) {
      this.amountData.memberId = data.id
      this.amountData.parentMemberId = data.parentId
      this.amountData.handleBy = this.member.id
      this.dialogFormVisible = true
      this.amountData.point = undefined
    },
    handlePassSetClick(data) {
      this.currentUserId = data.user.id
      this.dialogPassVisible = true
    },
    passCancel() {
      this.dialogPassVisible = false
    },
    passDone(data) {
      this.dialogPassVisible = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
