import request from '@/utils/request'

export function listSystem(pageNo, pageSize) {
  return request({
    url: '/system/list',
    method: 'get',
    params: {
      pageNo: pageNo,
      pageSize: pageSize
    }
  })
}

export function listSystemOption(data) {
  return request({
    url: '/system/option',
    method: 'post',
    data
  })
}

export function addSystem(data) {
  return request({
    url: '/system/add',
    method: 'post',
    data
  })
}

export function deleteSystem(ids) {
  return request({
    url: '/system/delete',
    method: 'post',
    params: {
      ids: ids
    }
  })
}

export function updateSystem(data) {
  return request({
    url: '/system/update',
    method: 'post',
    data
  })
}
