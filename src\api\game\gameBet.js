import request from '@/utils/request'
export function listGameBet(data) {
  return request({
    url: '/game-bet/list',
    method: 'post',
    data
  })
}
export function addGameBet(data) {
  return request({
    url: '/game-bet/add',
    method: 'post',
    data
  })
}
export function updateGameBet(data) {
  return request({
    url: '/game-bet/update',
    method: 'post',
    data
  })
}
export function deleteGameBet(ids) {
  return request({
    url: '/game-bet/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
export function addGame(gameCount, gameType, createBy) {
  return request({
    url: '/game-bet/addGame',
    method: 'get',
    params: {
      gameCount: gameCount,
      gameType: gameType,
      createBy: createBy
    }
  })
}
export function cancelGameBet(data) {
  return request({
    url: '/game-bet/cancel',
    method: 'post',
    data
  })
}

export function lastGameBet(data) {
  return request({
    url: '/game-bet/last',
    method: 'post',
    data
  })
}
