import request from '@/utils/request'

export function listStaff(data) {
  return request({
    url: '/staff/list',
    method: 'post',
    data,
    headers: { 'Content-Type': 'application/json' }
  })
}

export function listStaffResult(data) {
  return request({
    url: '/staff/listStaffResult',
    method: 'post',
    data
  })
}
export function addStaff(data) {
  return request({
    url: '/staff/add',
    method: 'post',
    data
  })
}

export function deleteStaff(ids) {
  return request({
    url: '/staff/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}

export function updateStaff(data) {
  return request({
    url: '/staff/update',
    method: 'post',
    data
  })
}

export function getStaff(id) {
  return request({
    url: '/staff/get',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function staffOptions(data) {
  return request({
    url: '/staff/option',
    method: 'post',
    data
  })
}
