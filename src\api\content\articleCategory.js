import request from '@/utils/request'

export function addArticleCategory(data) {
  return request({
    url: '/article/category/add',
    method: 'post',
    data
  })
}

export function listAllCategory(data) {
  return request({
    url: '/article/category/listAll',
    method: 'post',
    data
  })
}

export function listCategory(data) {
  return request({
    url: '/article/category/list',
    method: 'post',
    data
  })
}

export function listOneLevelCategory() {
  return request({
    url: '/article/category/list-one-level',
    method: 'get'
  })
}

export function updateArticleCategory(data) {
  return request({
    url: '/article/category/update',
    method: 'post',
    data
  })
}

export function deleteArticleCategory(id) {
  return request({
    url: '/article/category/delete',
    method: 'post',
    params: {
      id: id
    }
  })
}
