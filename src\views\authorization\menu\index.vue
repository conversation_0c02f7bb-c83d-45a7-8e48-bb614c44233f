<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="margin-bottom:5px">
      <el-col :span="8">
        <el-button
          v-show="add"
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddClick"
        >{{ $t('operate.add') }}
        </el-button>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-button icon="el-icon-refresh" type="primary" @click="getMenuData" />
        </el-row>
      </el-col>
    </el-row>
    <el-table
      :data="menuTree"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column
        prop="id"
        label="ID"
        align="center"
        width="144"
      />
      <el-table-column
        prop="menuName"
        :label="$t('menu.componentName')"
        width="144"
        show-overflow-tooltip
      />
      <el-table-column
        prop="component"
        :label="$t('menu.componentPath')"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column
        prop="metaIcon"
        :label="$t('menu.menuIcon')"
        align="center"
        width="104"
      >
        <template slot-scope="scope">
          <svg-icon :icon-class="scope.row.metaIcon" class="meta-item__icon" />
        </template>
      </el-table-column>
      <el-table-column
        prop="metaTitle"
        :label="$t('menu.menuName')"
        width="114"
        show-overflow-tooltip
      />
      <el-table-column
        prop="path"
        :label="$t('menu.menuUrl')"
        width="144"
        show-overflow-tooltip
      />
      <el-table-column
        prop="sortNo"
        :label="$t('menu.sortNo')"
        sortable
        align="center"
        width="104"
      />
      <el-table-column :label="$t('menu.status')" class-name="status-col" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusTagFilter">
            {{ row.status | statusFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        :label="$t('menu.remark')"
      />
      <el-table-column :label="$t('menu.operation')" align="center" fixed="right" width="110">
        <template slot-scope="scope">
          <el-button-group>
            <el-button size="mini" plain @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
            <!-- <el-button size="mini" plain @click="handleRemoveClick(scope.row)">删除</el-button> -->
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :title="$t('menu.menuInfo')" :visible.sync="dialogVisible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-tabs v-model="activeName" @tab-click="handleＴabClick">
        <el-tab-pane :label="$t('menu.systemMenu')" name="first">
          <el-form ref="menu" :model="menu" label-width="90px" label-suffix=":" size="mini">
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('menu.menuIcon')">
                  <el-input v-model="menu.metaIcon" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('menu.menuName')">
                  <el-input v-model="menu.metaTitle" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('menu.menuUrl')">
                  <el-input v-model="menu.path" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('menu.componentName')">
                  <el-input v-model="menu.menuName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('menu.componentPath')">
                  <el-input v-if="ifShow" v-model="menu.component" />
                  <span v-else>{{ menu.component }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('menu.locale')">
                  <el-input v-model="menu.locale" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('menu.status')">
                  <el-switch v-model="menu.status" :active-text="$t('commonStatus.valid')" :inactive-text="$t('commonStatus.invalid')" :active-value="1" :inactive-value="2" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('menu.ifHidden')">
                  <el-switch v-model="menu.hidden" :active-text="$t('hiddenOptions.hidden')" :inactive-text="$t('hiddenOptions.show')" :active-value="true" :inactive-value="false" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('menu.sortNo')">
                  <el-input-number v-model="menu.sortNo" :min="1" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('menu.menuType')">
                  <el-radio-group v-model="menu.menuLevel">
                    <el-radio :label="1">{{ $t('menu.levelOptions.level1') }}</el-radio>
                    <el-radio :label="2">{{ $t('menu.levelOptions.other') }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item v-show="menu.menuLevel === 2" :label="$t('menu.menuLocation')">
              <el-tree
                ref="tree"
                :data="menuTree"
                show-checkbox
                node-key="id"
                :check-strictly="true"
                default-expanded-all
                :default-checked-keys="[menu.parentId]"
                highlight-current
                :props="defaultProps"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="$t('menu.funtionManage')" name="second">
          <el-transfer v-model="buttonValue" size="mini" :data="buttonData" :titles="[$t('menu.buttonOptions.waiting'),$t('menu.buttonOptions.slected')]" />
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="actionType =='ADD'" :loading="loadingTags.add" type="primary" @click="addMenuAData">{{ $t('operate.save') }}</el-button>
        <el-button v-else type="primary" :loading="loadingTags.edit" @click="dialogOK">{{ $t('operate.edit') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { listMenu, addMenu, updateMenu, deleteMenu } from '@/api/authorization/menu'
import { listButton } from '@/api/authorization/button'
import i18n from '@/lang'
var statusOptions = [
  { key: '1', display_name: i18n.t('commonStatus.valid') },
  { key: '0', display_name: i18n.t('commonStatus.invalid') }
]
// arr to obj, such as { CN : "China", US : "USA" }
const statusTypeKeyValue = statusOptions.reduce((acc, cur) => {
  acc[cur.key] = cur.display_name
  return acc
}, {})
export default {
  name: 'TreeTableDemo',
  filters: {
    statusTagFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    },
    statusFilter(type) {
      return statusTypeKeyValue[type]
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      menuParam: {},
      actionType: '',
      buttonData: [],
      buttonValue: [],
      ifShow: true,
      menuTree: [],
      defaultProps: {
        children: 'children',
        label: 'metaTitle'
      },
      dialogVisible: false,
      loadingTags: {
        add: false,
        edit: false
      },
      formLabelWidth: '80px',
      // 选项卡对应变量
      activeName: 'first',
      menu: {},
      data: []
    }
  },
  created() {
    this.getMenuData()
    // this.getAllButton()
  },
  methods: {
    getAllButton() {
      const param = {}
      param.ifPage = false
      const data = []
      listButton(param).then(r => {
        r.data.list.forEach(element => {
          data.push({
            key: element.id,
            label: element.buttonName
          })
        })
        this.buttonData = data
      })
    },
    getMenuData() {
      const _this = this
      listMenu().then(r => {
        r.data.forEach(function(item, index, array) {
          item['edit'] = _this.edit
          item['delete'] = _this.delete
        })
        this.data = r.data
        this.menuTree = r.data
      })
    },
    removeData(id) {
      deleteMenu(id).then(() => {
        this.getMenuData()
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    // 对话框显示函数
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogVisible = true
      this.actionType = 'ADD'
      this.menu = { sortNo: 10, status: 1, hidden: false }
      this.getAllButton()
    },
    addMenuAData() {
      if (!this.$refs.tree.getCheckedKeys()) {
        this.menu.parentId = 0
      } else {
        this.menu.parentId = this.$refs.tree.getCheckedKeys()[0]
      }
      this.menuParam.menu = this.menu
      this.menuParam.buttonKeys = this.buttonValue
      this.loadingTags.add = true
      addMenu(this.menuParam).then(() => {
        this.getMenuData()
        this.dialogVisible = false
        this.$message({
          message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    handleEditClick(val) {
      this.getAllButton()
      this.loadingTags.edit = false
      this.actionType = 'EDIT'
      this.dialogVisible = true
      this.menu = val
      if (undefined !== this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([this.menu.parentId])
      }
      this.buttonValue = this.menu.relatedButtonKeys
    },
    modifyData() {
      if (!this.$refs.tree.getCheckedKeys()) {
        this.menu.parentId = 0
      } else {
        this.menu.parentId = this.$refs.tree.getCheckedKeys()[0]
      }
      this.menuParam.menu = this.menu
      delete this.menuParam.menu.children
      delete this.menuParam.menu.parent
      this.menuParam.buttonKeys = this.buttonValue
      this.loadingTags.edit = true
      updateMenu(this.menuParam).then(() => {
        this.dialogVisible = false
        this.getMenuData()
        this.$message({
          message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    handleＴabClick(tab, event) {
    },
    dialogOK() {
      this.dialogVisible = false
      if (this.actionType === 'EDIT') {
        this.modifyData()
      }
    },
    handleRemoveClick(val) {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.menu = val
        this.removeData(this.menu.id)
      })
    }
  }
}
</script>
