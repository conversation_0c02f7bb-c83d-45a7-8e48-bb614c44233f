import request from '@/utils/request'

export function listRole(userId, systemId) {
  return request({
    url: '/role/list',
    method: 'get',
    params: {
      userId: userId,
      systemId: systemId
    }
  })
}

export function listAll(userType) {
  return request({
    url: '/role/listAll',
    method: 'get',
    params: {
      userType: userType
    }
  })
}

export function addRole(data) {
  return request({
    url: '/role/add',
    method: 'post',
    data
  })
}

export function deleteRole(data) {
  return request({
    url: '/role/delete',
    method: 'post',
    data
  })
}

export function updateRole(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data
  })
}
