<template>
  <div class="app-container">
    <el-row type="flex" justify="space-between" style="padding-bottom:5px; ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-input v-model="accountParam.userName" style="width:144px" placeholder="根据账号查询" clearable />
          <el-select v-model="accountParam.userType" clearable placeholder="根据账号类型查询">
            <el-option
              v-for="item in userTypeEnums"
              :key="item.type"
              :label="item.value"
              :value="item.type"
            />
          </el-select>
          <el-button icon="fa fa-search" type="primary" @click="listTableData" />
        </el-row>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center">
      <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column prop="id" label="ID" width="64" />
        <el-table-column prop="userName" label="用户名" width="124" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.userName">{{ scope.row.userName }}</span>
            <span v-else>{{ scope.row.openId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="昵称" width="114" show-overflow-tooltip />
        <el-table-column prop="avatar" label="用户图像" width="164" show-overflow-tooltip />
        <el-table-column prop="userType" label="账号类型" width="154" :formatter="userTypeEnumFormat" />
        <el-table-column prop="userStatus" label="账号状态" width="154" :formatter="userStatusEnumFormat" />
        <el-table-column prop="createDt" align="center" label="创建时间" width="164" :formatter="dateTimeFormat" />
        <el-table-column label="备注" show-overflow-tooltip />
        <el-table-column fixed="right" align="center" label="操作" width="250">
          <template slot-scope="scope">
            <el-button-group>
              <el-button size="mini" style="width:74px;" title="开关账号" plain @click="handleAccountOpenClick(scope.row)">开关账号</el-button>
              <el-button size="mini" style="width:74px;" title="权限设置" plain @click="handleAccountAuthClick(scope.row)">权限设置</el-button>
              <el-button size="mini" style="width:74px;" title="重置密码" plain @click="handlePasswordResetClick(scope.row)">重置密码</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog title="账号权限" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-tree
        ref="roleTreeForm"
        :data="roleTree"
        show-checkbox
        default-expand-all
        node-key="id"
        highlight-current
        :default-checked-keys="object.roleIds"
        :check-strictly="true"
        :props="defaultProps"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="账号信息" :visible.sync="accountOpenVisible" width="40%" :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form label-suffix=":">
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号">
              <span>{{ user.userName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户昵称">
              <span>{{ user.nickName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账号类型">
              <span>{{ user.userType | userTypeEnumFilter }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号状态">
              <span>{{ user.userStatus | userStatusEnumFilter }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作">
              <el-switch
                v-model="user.userStatus"
                active-text="开通"
                inactive-text="关闭"
                :active-value="2"
                :inactive-value="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="accountOpenVisible = false">取 消</el-button>
        <el-button :disable="repeatSubmit" type="primary" @click="submitUserEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listAccount, changePasswordByAdmin } from '@/api/authorization/account'
import { getToken } from '@/utils/auth' // 验权
import { updateUserStatus } from '@/api/common/user'
import { getUserStatusEnum } from '@/data/userCenter/user'
import { getUserTypeEnum, UserTypeEnum } from '@/enums/system'
import { listRole } from '@/api/authorization/role'
import moment from 'moment'
import { deepClone, genPassword } from '@/utils/transferUtil'
export default {
  name: 'UserList',
  filters: {
    userTypeEnumFilter: function(val) {
      if (!val) {
        return ''
      } else {
        return getUserTypeEnum(val)
      }
    },
    userStatusEnumFilter: function(val) {
      if (!val) {
        return ''
      } else {
        return getUserStatusEnum(val)
      }
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    },
    query: {
      type: Boolean,
      default: false
    },
    review: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      adminFormVisible: false,
      admin: {},
      userTypeEnums: [],
      roleTree: [],
      staffDetail: {
        staff: {},
        relatedUser: {},
        roleIds: []
      },
      staffSalary: {},
      defaultProps: {
        children: 'children',
        label: 'roleName'
      },
      user: {
        userName: '',
        userType: ''
      },
      repeatSubmit: false,
      loading: false,
      idCardValid: false,
      stations: [],
      accountParam: {},
      activeName: 'first',
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      accountOpenVisible: false,
      object: {
      },
      formLabelWidth: '80px',
      ifView: false,
      loadingTags: {
        add: false,
        edit: false
      },
      ids: [],
      dialogStatus: ''
    }
  },
  computed: {
    myHeaders: function() {
      return {
        'X-Token': getToken()
      }
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].staff.id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.userTypeEnums = UserTypeEnum
    this.listTableData()
  },
  mounted() {
  },
  methods: {
    buildRoleTree(userId) {
      listRole(userId).then(r => {
        this.roleTree = r.data
      })
    },
    listTableData() {
      this.accountParam.pageNo = this.currentPage
      this.accountParam.pageSize = this.pageSize
      const _this = this
      listAccount(this.accountParam).then(r => {
        r.data.list.forEach(function(item, index, array) {
          item['review'] = _this.review
          item['edit'] = _this.edit
        })
        this.objectList = r.data.list
        this.total = r.data.total
        this.currentPage = r.data.pageNum
      })
    },
    handleAddClick() {
      this.adminFormVisible = true
    },
    handleAccountOpenClick(val) {
      this.user = deepClone(val)
      this.accountOpenVisible = true
    },
    handleAccountAuthClick(val) {
      this.buildRoleTree(val.id)
      this.object.roleIds = val.roleIds
      this.dialogFormVisible = true
    },
    handlePasswordResetClick(val) {
      this.$prompt('请输入新密码', '提示', {
        closeOnClickModal: false,
        inputType: 'password',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/,
        inputErrorMessage: '密码为6-16个数字和字母组成'
      }).then(({ value }) => {
        changePasswordByAdmin(val.id, genPassword(value)).then(r => {
          if (r.data) {
            this.$message({
              type: 'success',
              message: '密码重置成功'
            })
          } else {
            this.$message({
              type: 'error',
              message: '密码重置失败'
            })
          }
        })
      }).catch(() => {
      })
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.buildRoleTree(val.id)
      if (this.$refs.roleTreeForm) {
        this.$refs.roleTreeForm.setCheckedKeys(val.roleIds)
      }
      this.object = {}
      this.object = val
      this.ifView = false
      this.dialogStatus = 'edit'
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    handleViewClick(val) {
      this.dialogFormVisible = true
      this.object = val
      this.ifView = true
      this.dialogStatus = 'view'
    },
    handleDeleteClick() {
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteRows()
      })
    },
    submitUserEdit() {
      this.repeatSubmit = true
      updateUserStatus(this.user.id, this.user.userStatus).then(() => {
        this.accountOpenVisible = false
        this.repeatSubmit = false
      })
    },
    userTypeEnumFormat: function(row, column, cellValue) {
      if (!cellValue) {
        return ''
      } else {
        return getUserTypeEnum(cellValue)
      }
    },
    userStatusEnumFormat: function(row, column, cellValue) {
      if (!cellValue) {
        return ''
      } else {
        return getUserStatusEnum(cellValue)
      }
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
