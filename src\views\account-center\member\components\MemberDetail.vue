<template>
  <div class="app-container">
    <el-form ref="postForm" label-width="180px" :model="postForm" label-suffix=":" :rules="rules" class="form-container">
      <div class="createPost-main-container">
        <el-row :gutter="15">
          <el-col :span="16">
            <div class="span-title mb-5">{{ $t('member.accountInfo') }}</div>
            <el-input v-model="postForm.user.password" type="password" style="position:fixed;bottom:-9999px;" />
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('member.account')" prop="user.userName" required>
                  <el-input v-model="postForm.user.userName" class="input-with-select" :disabled="isEdit" clearable>
                    <el-button
                      slot="append"
                      :disabled="isEdit"
                      :loading="remoteLoading"
                      @click="genUserName"
                    > {{ $t('operate.sysgen') }} </el-button>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('member.fullName')" class="postInfo-container-item">
                  <el-input v-model="postForm.member.fullName" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('member.passForm.password')" prop="user.password" class="postInfo-container-item" required>
                  <el-input v-model="postForm.user.password" :disabled="isEdit" type="password" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('member.passForm.passwordConfirm')" prop="user.repeatPassword" class="postInfo-container-item" required>
                  <el-input v-model="postForm.user.repeatPassword" :disabled="isEdit" type="password" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('member.mobilePhone')" class="postInfo-container-item">
                  <el-input v-model="postForm.member.phoneNo" clearable />
                </el-form-item>
              </el-col>
              <!--<el-col v-if="userName === 'superadmin'" :span="12">
                <el-form-item :label="$t('member.memberType')" class="postInfo-container-item">
                  <el-radio v-model="postForm.member.isHide" :label="1">{{ $t('member.normal') }}</el-radio>
                  <el-radio v-model="postForm.member.isHide" :label="2">{{ $t('member.hide') }}</el-radio>
                </el-form-item>
              </el-col>
              </el-row> -->
              <el-col :span="12">
                <el-form-item :label="$t('member.commission')" class="postInfo-container-item">
                  <el-input-number v-model="postForm.member.commission" :max="100" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <el-row>
              <el-col :span="24">
                <el-form-item :label="$t('member.currency')" class="postInfo-container-item">
                  <el-radio v-if="userType === 1 || member.currencyCode==='KHR'" v-model="postForm.member.currencyCode" label="KHR">KHR</el-radio>
                  <el-radio v-if="userType === 1 || member.currencyCode==='USD'" v-model="postForm.member.currencyCode" label="USD">USD</el-radio>
                  <el-radio v-if="userType === 1 || member.currencyCode==='CNY'" v-model="postForm.member.currencyCode" label="CNY">CNY</el-radio>
                  <el-radio v-if="userType === 1 || member.currencyCode==='THB'" v-model="postForm.member.currencyCode" label="THB">THB</el-radio>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item :label="$t('member.commission')" class="postInfo-container-item">
                  <el-input-number v-model="postForm.commission" :max="100" />
                </el-form-item>
              </el-col>
            </el-row> -->
            <!--</el-collapse-item>
            <el-collapse-item v-show="false" :title="$t('member.washRateInfo')" name="1">
              <el-table ref="multipleTable" size="small" :data="postForm.washItems" border highlight-current-row>
                <el-table-column type="index" width="42" />
                <el-table-column prop="price" :label="$t('member.codeWashingFrom')" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.number="scope.row.washFrom" :disabled="isEdit" class="inline-input" :placeholder="$t('member.codeWashingFrom')" :trigger-on-focus="false" clearable border="false" />
                  </template>
                </el-table-column>
                <el-table-column prop="price" :label="$t('member.codeWashingTo') + '('+ $t('member.washTips') + ')'" align="center">
                  <template slot-scope="scope">
                    <el-input v-model.number="scope.row.washTo" :disabled="isEdit" class="inline-input" :placeholder="$t('member.codeWashingTo')" :trigger-on-focus="false" clearable border="false" />
                  </template>
                </el-table-column>
                <el-table-column prop="price" :label="$t('member.rateOfCodeWashing') + '(%)'" align="center">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.percentWash" style="width:100%;" :controls="false" :min="0" :max="2" :disabled="isEdit" :placeholder="$t('member.rateOfCodeWashing') + '(%)'" :trigger-on-focus="false" clearable border="false" />
                  </template>
                </el-table-column>
                <el-table-column width="155" :label="$t('member.operation')" fixed="right" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button-group>
                      <el-button plain size="mini" :disabled="isEdit" icon="el-icon-plus" @click="plusRow(scope.$index,scope.row)" />
                      <el-button plain size="mini" :disabled="isEdit" icon="el-icon-minus" @click="minusRow(scope.$index,scope.row)" />
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>-->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <div class="span-title mt-10 mb-5">{{ $t('member.limitInfo') }}</div>
            <el-table ref="multipleTable" size="small" :data="jettonList" border highlight-current-row @selection-change="handleSelectionChange">
              <el-table-column type="selection" align="center" width="44" />
              <el-table-column type="index" align="center" width="44" />
              <el-table-column prop="jettonNo" align="center" :label="$t('member.jetton.chipNo')" width="90" />
              <el-table-column prop="jettonName" align="center" :label="$t('member.jetton.chipName')" width="90" />
              <el-table-column prop="downLimit" align="center" :label="$t('member.jetton.lowerLimit')" width="90" />
              <el-table-column prop="upperLimit" align="center" :label="$t('member.jetton.upperLimit')" width="90" />
              <el-table-column prop="jetton" :label="$t('member.jetton.chipsOption')" show-overflow-tooltip />
            </el-table>
          </el-col>
        </el-row>
        <el-row type="flex" class="mt-10">
          <el-col :offset="4" :span="12">
            <el-form-item>
              <el-button :loading="loading" type="primary" @click="submitForm">
                {{ $t('operate.save') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>
<script>
import { addMember, fetchMember, genAccount } from '@/api/account/member'
import { listJetton } from '@/api/setting/jetton'
import { mapGetters } from 'vuex'
import { checkAccountExist } from '@/api/account/accountCheck'
import { isValidPassword } from '@/utils/validate'
import { deepClone } from '@/utils/transferUtil'
const defaultForm = {
  user: {
    id: 0,
    userName: '',
    nickName: ''
  }, // 前台展示时间
  member: {
    percentAgent: 0,
    memberType: 3,
    isHide: 1,
    currencyCode: 'KHR'
  },
  washItems: [],
  removedItems: [],
  jettonIdList: []
}

export default {
  name: 'MemberDetail',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    admin: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('memberAddForm.validateMsg.account')))
      } else {
        checkAccountExist(
          value,
          3,
          this.postForm.user.id
        ).then(r => {
          if (r.data) {
            callback(new Error(this.$t('memberAddForm.validateMsg.accountExist')))
          } else {
            callback()
          }
        })
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.password')))
      } else if (!isValidPassword(value)) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordFormate')))
      } else {
        callback()
      }
    }
    const validateCheckPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordConfirm')))
      } else if (value !== this.postForm.user.password) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordDifferent')))
      } else {
        callback()
      }
    }
    return {
      multipleSelection: [],
      ids: [],
      activeName: 'first',
      postForm: {
        user: {
          id: 0,
          userName: '',
          nickName: ''
        }, // 前台展示时间
        member: {
          percentAgent: 0,
          memberType: 3,
          isHide: 1,
          currencyCode: 'CNY'
        },
        washItems: [],
        removedItems: [],
        jettonIdList: []
      },
      // Object.assign({}, defaultForm),
      remoteLoading: false,
      loading: false,
      userListOptions: [],
      rules: {
        'user.userName': [{ validator: validateUsername }],
        'user.password': [{ validator: validatePassword }],
        'user.repeatPassword': [{ validator: validateCheckPassword }]
      },
      tempRoute: {},
      activeNames: ['1', '2', '3'],
      jettonList: []
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'userName',
      'userType',
      'device'
    ])
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
      this.postForm.jettonIdList = arr
    },
    'postForm.member.currencyCode': function() {
      this.initJettonData()
    }
  },
  created() {
    if (this.isEdit) {
      const id = this.$route.params && this.$route.params.id
      this.fetchData(id)
    } else {
      this.postForm = deepClone(defaultForm)
      this.postForm.member.currencyCode = this.member.currencyCode
      this.initEmbedTable(1)
    }
    this.tempRoute = Object.assign({}, this.$route)
    this.initJettonData()
  },
  methods: {
    genUserName() {
      this.remoteLoading = true
      genAccount(3).then(r => {
        this.postForm.user.userName = r.data
        this.remoteLoading = false
      })
    },
    initJettonData(data) {
      const param = {}
      listJetton(param).then(r => {
        this.jettonList = []
        for (var i = 0; i < r.data.list.length; i++) {
          /* if (r.data.list[i].jettonName.toUpperCase() === this.postForm.member.currencyCode) {
            this.jettonList.push(r.data.list[i])
          }*/
          this.jettonList.push(r.data.list[i])
        }
        if (!data) {
          data = []
          data.push(this.jettonList[0])
        }
        setTimeout(() => {
          if (data.length > 0) {
            this.callToggleSelection(data)
          }
        }, 100)
      })
    },
    initDefaultJetton() {
      setTimeout(() => {
        var defaultJettonIds = [9]
        if (this.postForm.member.currencyCode === 'KHR') {
          defaultJettonIds = [10]
        }
        this.$refs.multipleTable.clearSelection()
        for (var i = 0; i < defaultJettonIds.length; i++) {
          for (var j = 0; j < this.jettonList.length; j++) {
            if (defaultJettonIds[i] === this.jettonList[j].id) {
              this.toggleSelection([this.jettonList[j]])
            }
          }
        }
      }, 100)
    },
    initSelectedJettonData(data) {
      const param = {}
      listJetton(param).then(r => {
        this.jettonList = r.data.list
        setTimeout(() => {
          this.callToggleSelection(data)
        }, 100)
      })
    },
    callToggleSelection(data) {
      this.$refs.multipleTable.clearSelection()
      for (var i = 0; i < data.length; i++) {
        for (var j = 0; j < this.jettonList.length; j++) {
          if (data[i].id === this.jettonList[j].id) {
            this.toggleSelection([this.jettonList[j]])
          }
        }
      }
    },
    toggleSelection(rows) {
      console.log(rows)
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        // this.$refs.multipleTable.clearSelection()
      }
    },
    fetchData(id) {
      fetchMember(id).then(response => {
        this.postForm.member = response.data
        this.postForm.user = response.data.user
        this.postForm.user.repeatPassword = this.postForm.user.password
        this.postForm.washItems = response.data.memberWashList
        this.postForm.jettonList = response.data.jettonList
        if (this.postForm.washItems.length > 0) {
          this.postForm.member.commission = this.postForm.washItems[0].percentWash
        }
        this.initJettonData(this.postForm.jettonList)
      }).catch(err => {
        console.log(err)
      })
    },
    submitForm() {
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (!this.postForm.washItems) {
            this.postForm.washItems = []
            this.postForm.washItems.push({ percentWash: this.postForm.member.commission, washFrom: 0, washTo: 0 })
          }
          if (this.postForm.washItems.length === 0) {
            this.$message({
              showClose: true,
              message: this.$t('memberAddForm.validateMsg.rateSetting'),
              type: 'error'
            })
            this.loading = false
            return false
          } else {
            this.postForm.washItems[0].percentWash = this.postForm.member.commission
            this.postForm.washItems[0].washFrom = 0
            this.postForm.washItems[0].washTo = 0
            for (var i = 0; i < this.postForm.washItems.length; i++) {
              if (this.postForm.washItems[i].percentWash > 2) {
                this.$message({
                  showClose: true,
                  message: this.$t('memberAddForm.validateMsg.rateLimit'),
                  type: 'error'
                })
                this.loading = false
                return false
              }
            }
          }
          if (this.postForm.jettonIdList.length === 0) {
            this.$message({
              showClose: true,
              message: this.$t('memberAddForm.validateMsg.chipOptions'),
              type: 'error'
            })
            this.loading = false
            return false
          }
          if (this.postForm.jettonIdList.length > 3) {
            this.$message({
              showClose: true,
              message: this.$t('memberAddForm.validateMsg.chipOptionsLimit'),
              type: 'error'
            })
            this.loading = false
            return false
          }
          if (this.member.memberType === 4) {
            this.postForm.member.parentId = this.member.parentId
          } else {
            this.postForm.member.parentId = this.member.id
          }
          addMember(this.postForm).then(r => {
            if (this.isEdit) {
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
                type: 'success',
                duration: 2000
              })
            } else {
              this.$message({
                message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
                type: 'success',
                duration: 2000
              })
              this.postForm = deepClone(defaultForm)
              this.initEmbedTable(1)
              this.initJettonData()
              this.$refs.postForm.resetFields()
            }
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    initEmbedTable(size) {
      var i = 0
      while (i < size) {
        i++
        var tempObject = { id: 0 }
        this.$set(tempObject, 'willShow', true)
        this.postForm.washItems.push(tempObject)
      }
    },
    plusRow(index, val) {
      var startData = this.postForm.washItems.slice(0, index + 1)
      var endData = this.postForm.washItems.slice(index + 1, this.postForm.washItems.length)
      var tempObject = { id: 0, willShow: true }
      startData.push(tempObject)
      this.postForm.washItems = startData.concat(endData)
    },
    minusRow(index, row) {
      if (this.postForm.washItems.length === 1) {
        return
      }
      if (!this.postForm.removedItems) {
        this.postForm.removedItems = []
      }
      this.postForm.removedItems.push(row.id)
      this.postForm.washItems.splice(index, 1)
    },
    handleSelectionChange(rows) {
      if (rows.length > 3) { // 单选时为1，需要选择n项改数值为n就可以
        this.$message({
          showClose: true,
          message: this.$t('memberAddForm.validateMsg.chipOptionsLimit'),
          type: 'error'
        })
        this.$refs.multipleTable.toggleRowSelection(rows[3], false)// 超出指定选择个数后，把第一个选中的selection设为false
        rows.pop()// 同时要把选中第一项移除
      }
      this.multipleSelection = rows
    },
    checkBetLimit() {
      if (this.member.betMin) {
        if (this.postForm.member.betMin > this.member.betMin) {
          this.$message.error(this.$t('member.betMin') + this.$t('member.dataError') + this.member.betMin)
          return false
        }
      }
      if (this.member.betMax) {
        if (this.postForm.member.betMax > this.member.betMax) {
          this.$message.error(this.$t('member.betMax') + this.$t('member.dataError') + this.member.betMax)
          return false
        }
      }
      if (this.member.betMax2) {
        if (this.postForm.member.betMax2 > this.member.betMax2) {
          this.$message.error(this.$t('member.betMax2') + this.$t('member.dataError') + this.member.betMax2)
          return false
        }
      }
      if (this.member.betMax3) {
        if (this.postForm.member.betMax3 > this.member.betMax3) {
          this.$message.error(this.$t('member.betMax3') + this.$t('member.dataError') + this.member.betMax3)
          return false
        }
      }
      if (this.member.maxWinPoint) {
        if (this.postForm.member.maxWinPoint > this.member.maxWinPoint) {
          this.$message.error(this.$t('member.maxWin') + this.$t('member.dataError') + this.member.maxWinPoint)
          return false
        }
      }
      if (this.member.bet2Min) {
        if (this.postForm.member.bet2Min > this.member.bet2Min) {
          this.$message.error(this.$t('member.betMin') + this.$t('member.dataError') + this.member.bet2Min)
          return false
        }
      }
      if (this.member.bet2Max) {
        if (this.postForm.member.bet2Max > this.member.bet2Max) {
          this.$message.error(this.$t('member.betMax') + this.$t('member.dataError') + this.member.bet2Max)
          return false
        }
      }
      if (this.member.bet2Max2) {
        if (this.postForm.member.bet2Max2 > this.member.bet2Max2) {
          this.$message.error(this.$t('member.betMax2') + this.$t('member.dataError') + this.member.bet2Max2)
          return false
        }
      }
      if (this.member.bet2Max3) {
        if (this.postForm.member.bet2Max3 > this.member.bet2Max3) {
          this.$message.error(this.$t('member.betMax3') + this.$t('member.dataError') + this.member.bet2Max3)
          return false
        }
      }
      if (this.member.maxWinPoint2) {
        if (this.postForm.member.maxWinPoint2 > this.member.maxWinPoint2) {
          this.$message.error(this.$t('member.maxWin') + this.$t('member.dataError') + this.member.maxWinPoint2)
          return false
        }
      }
      return true
    }
  }
}
</script>
<style>
.input-with-select .el-input-group__append {
  background-color: #fff;
  color: #006dfe;
}
.jetton .has-gutter .el-checkbox {
   display:none
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.createPost-container {
  position: relative;

  .createPost-main-container {
    padding: 40px 45px 20px 50px;

    .postInfo-container {
      position: relative;
      @include clearfix;
      margin-bottom: 10px;

      .postInfo-container-item {
        float: left;
      }
    }
  }

  .word-counter {
    width: 40px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
}

.article-textarea /deep/ {
  textarea {
    padding-right: 40px;
    resize: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #bfcbd9;
  }
}
</style>
