export const GameCategoryEnum = [{
  category: 1, label: 'gameCategory.proxy'
}, {
  category: 2, label: 'gameCategory.speed'
}
]
export const HideEnum = [
  {
    type: 0, label: 'gameTypeEnum.all'
  },
  {
    type: 1, label: 'member.normal'
  }, {
    type: 2, label: 'member.hide'
  }
]
export function getGameCategoryEnum(category) {
  for (const val of GameCategoryEnum) {
    if (val.category === category) {
      return val
    }
  }
}
export const CurrencyCodeEnum = [{
  code: 1, label: 'currencyCode.RMB'
}, {
  code: 2, label: 'currencyCode.USD'
}
]

export function getCurrencyCodeEnum(code) {
  for (const val of CurrencyCodeEnum) {
    if (val.code === code) {
      return val
    }
  }
}
export const GameTypeEnum = [{
  type: 0, label: 'gameTypeEnum.all'
}, {
  type: 1, label: 'gameTypeEnum.baccarat'
}, {
  type: 2, label: 'gameTypeEnum.dragonAndTiger'
},
{
  type: 3, label: 'gameTypeEnum.bullAndBull'
}
]
export function getGameTypeEnum(type) {
  for (const val of GameTypeEnum) {
    if (val.type === type) {
      return val
    }
  }
}
export const GameTypePureEnum = [{
  type: 1, label: 'gameTypeEnum.baccarat'
}, {
  type: 2, label: 'gameTypeEnum.dragonAndTiger'
},
{
  type: 3, label: 'gameTypeEnum.bullAndBull'
}
]
export function getGameTypePureEnum(type) {
  for (const val of GameTypePureEnum) {
    if (val.type === type) {
      return val
    }
  }
}
export const TableInfoStatusEnum = [{
  status: -1, label: 'tableInfoStatusEnum.new'
}, {
  status: 0, label: 'tableInfoStatusEnum.end'
},
{
  status: 1, label: 'tableInfoStatusEnum.betting'
},
{
  status: 2, label: 'tableInfoStatusEnum.bet'
},
{
  status: 3, label: 'tableInfoStatusEnum.start'
},
{
  status: 4, label: 'tableInfoStatusEnum.wait'
},
{
  status: 5, label: 'tableInfoStatusEnum.break'
},
{
  status: 6, label: 'tableInfoStatusEnum.pause'
},
{
  status: 7, label: 'tableInfoStatusEnum.washing'
}
]
export function getTableInfoStatusEnum(status) {
  for (const val of TableInfoStatusEnum) {
    if (val.status === status) {
      return val
    }
  }
}

export const PlatformEnum = [{
  platform: 1, label: 'platformEnum.pc'
}, {
  platform: 2, label: 'platformEnum.android'
},
{
  platform: 3, label: 'platformEnum.apple'
},
{
  platform: 4, label: 'platformEnum.browser'
}
]
export function getPlatformEnum(platform) {
  for (const val of PlatformEnum) {
    if (val.platform === platform) {
      return val
    }
  }
  return ''
}

