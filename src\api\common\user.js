import request from '@/utils/request'

export function updateUserStatus(userId, userStatus) {
  return request({
    url: '/user/updateUserStatus',
    method: 'post',
    params: {
      userId: userId,
      userStatus: userStatus
    }
  })
}

export function getUser(refId, refType) {
  return request({
    url: '/user/getUser',
    method: 'post',
    params: {
      refId: refId,
      refType: refType
    }
  })
}

export function checkPassword(userId, password) {
  return request({
    url: '/user/checkPassword',
    method: 'post',
    params: {
      userId: userId,
      password: password
    }
  })
}

export function changePassword(userId, password) {
  return request({
    url: '/user/inner/changePassword',
    method: 'post',
    params: {
      userId: userId,
      password: password
    }
  })
}
