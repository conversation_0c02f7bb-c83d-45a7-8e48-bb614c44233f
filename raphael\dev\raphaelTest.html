<!DOCTYPE html>
<head>
    <title><PERSON> Dev testing html</title>

    <!-- HTML to try new developments in Raphael -->

    <!-- Global use -->

    <!-- To work with full version -->
    <!--<script type="text/javascript" src="../raphael.js"></script>-->

    <!-- To work with minified version -->
    <!--<script type="text/javascript" src="../raphael-min.js"></script>-->

    <!-- Comment this script tag if you are testing with AMD -->
    <!--<script type="text/javascript">
        // Initialize container when document is loaded
        window.onload = function () {
            paper = Raphael(0, 0, 640, 720, "container");
            paper.circle(100, 100, 100); //example
        };
        //Work here, in a separate script file or via command line
    </script>-->

    <!-- Use amdDev.js to work with <PERSON><PERSON> and <PERSON> -->
    <!-- You need to do a 'bower install -D' first to get requirejs -->
    <script data-main="amdDev" src="../bower_components/requirejs/require.js"></script>

</head>
<body>
<!-- Container for svg/vml root element -->
<div id="container"></div>
</body>
</html>
