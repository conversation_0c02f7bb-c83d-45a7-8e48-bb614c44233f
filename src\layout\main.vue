<template>
  <div v-if="device === 'mobile'" class="mobile login-box">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </div>
  <el-container v-else>
    <el-header>
      <el-row>
        <div class="main-box">
          <el-col :span="12">
            <a class="logo" href="#" />
            <span class="header-title">{{ headerTitle }}</span>
          </el-col>
          <el-col :span="12">
            <el-popover ref="popoverWb" placement="bottom" width="60" trigger="hover">
              <img class="qrcode-img" src="../assets/images/app.png">
            </el-popover>
            <el-breadcrumb v-show="isLogin" class="header-breadcrumb">
              <!-- <el-breadcrumb-item v-popover:popoverWb class="weixin"><i class="fa fa-apple" />{{ $t('header.appDownload') }}</el-breadcrumb-item> -->
              <!-- <el-breadcrumb-item @click.native="openGame"><span class="weibo"><i class="fa fa fa-home" />{{ $t('header.playNow') }}</span></el-breadcrumb-item> -->
              <lang-select v-show="false" />
            </el-breadcrumb>
          </el-col>
        </div>
      </el-row>
    </el-header>
    <el-main>
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <router-view :key="key" />
        </keep-alive>
      </transition>
    </el-main>
    <el-footer style="height: 40px;">
      <div class="main-box">
        <div class="footer-box">
          <el-breadcrumb class="footer-breadcrumb">
            <el-breadcrumb-item>©2011 - {{ year }} {{ $t('header.copyright') }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
import LangSelect from '@/components/LangSelect'
import ResizeMixin from './mixin/ResizeHandler'
import { mapGetters } from 'vuex'

export default {
  name: 'LayoutMain',
  components: {
    LangSelect
  },
  mixins: [ResizeMixin],
  data() {
    return {
      year: (new Date()).getFullYear(),
      headerTitle: this.$t('header.welcome'),
      isLogin: true,
      isMemberRegister: false,
      isCompanyRegister: false
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    },
    ...mapGetters(['device'])
  },
  methods: {
    openGame() {
      window.open('http://bellagio.ictbs.io', '_blank')
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.mobile.login-box {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  /* background-image:url("../assets/images/login-bg.png"); */
  background-repeat: no-repeat;
  background-size: 300% 100%;
  background-position: 20% 0;
  background-color: #040f15;
}

.to-login {
  margin-top: 25px;
  font-size: 13px;
  float: right;
}

.to-login a:hover {
  color: #006dfe;
}

.register {
  text-align: center;
  margin-top: 15px;
  width: 100px;
  height: 32px;
  line-height: 32px;
  border: 0px solid #dcdfe6;
  border-radius: 5px;
  background-color: #006dfe;
  color: white;
  float: right;
  margin-left: 10px;
  font-size: 13px;
}

.qrcode-img {
  width: 100px;
  height: 100px;
}

.header-breadcrumb {
  float: right;
  margin-top: 25px;
  text-align: end;
}

.el-header {
  width: 100%;
  height: 60px;
  background-color: #ffffff;
  box-shadow: 0px 3px 3px #dcdfe6;
  position: fixed;
  top: 0px;
  z-index: 2;
}

.el-main {
  margin-top: 60px;
  margin-bottom: 40px;
  padding: 0px;
}

.el-footer {
  background-color: #f5f7fa;
  width: 100%;
  height: 40px;
  border-top: 1px solid #d4d4d4;
  position: fixed;
  bottom: 0px;
}

.footer-box {
  padding-top: 10px;
  padding-bottom: 10px;
  width: 330px;
  margin: 0 auto;

  .el-breadcrumb__inner,
  .el-breadcrumb__inner a {
    font-size: 12px;
  }

  .el-breadcrumb {
    font-size: 12px;
  }
}

.footer-breadcrumb {
  height: 20px;
  line-height: 20px;
  position: absolute;
}

.footer-breadcrumb .beian {
  cursor: pointer;
}

.logo {
  height: 44px;
  width: 188px;
  background-image: url(../assets/images/logo.png);
  background-color: #ffffff;
  background-repeat: no-repeat;
  background-size: 120px 44px;
  float: left;
  margin-left: 10px;
  margin-top: 10px;
  text-indent: -9999em;
  border: none;
}

.header-title {
  float: left;
  margin-left: 20px;
  margin-top: 13px;
  padding-left: 20px;
  height: 35px;
  line-height: 35px;
  color: #555555;
  font-weight: bold;
  font-size: 20px !important;
  border-left: 1px solid #dddddd;
}

.weibo,
.weixin {
  cursor: pointer;
}

.footer-breadcrumb .el-breadcrumb__inner,
.footer-breadcrumb .el-breadcrumb__inner a,
.footer-breadcrumb .el-breadcrumb__inner span {
  font-weight: normal !important;
  font-size: 12px !important;
}

.header-breadcrumb .el-breadcrumb__inner,
.header-breadcrumb .el-breadcrumb__inner a,
.header-breadcrumb .el-breadcrumb__inner span {
  font-weight: normal !important;
  font-size: 12px !important;
}
</style>
