<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px; ">
      <el-col :span="8">
        <el-button
          v-show="add"
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddClick"
        >
          {{ $t('operate.add') }}
        </el-button>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-select v-model="userType" clearable>
            <el-option
              v-for="item in userTypeEnums"
              :key="item.type"
              :label="$t(item.value)"
              :value="item.type"
            />
          </el-select>
          <el-button icon="fa fa-search" type="primary" @click="listTableData" />
        </el-row>
      </el-col>
    </el-row>
    <el-table
      :data="data"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column
        prop="id"
        label="ID"
        align="center"
        width="144"
      />
      <el-table-column
        prop="roleName"
        :label="$t('role.roleName')"
        align="center"
        width="104"
      />
      <el-table-column
        prop="roleNo"
        :label="$t('role.roleNo')"
        align="center"
        width="164"
      />
      <el-table-column
        prop="sortNo"
        :label="$t('role.sortNo')"
        sortable
        align="center"
        width="114"
      />
      <el-table-column prop="userType" :label="$t('role.userType')" width="154" :formatter="userTypeEnumFormat" />
      <el-table-column :label="$t('role.status')" class-name="status-col" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusTagFilter">
            {{ row.status | statusFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        :label="$t('role.remark')"
      />
      <el-table-column :label="$t('role.operation')" align="center" fixed="right" width="110">
        <template slot-scope="scope">
          <el-button-group>
            <el-button size="mini" plain @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
            <!-- <el-button size="mini" plain @click="handleRemoveClick(scope.row)">删除</el-button> -->
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :visible.sync="dialogVisible">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane :label="$t('role.roleInfo')" name="first">
          <el-form ref="menu" :model="object" label-width="80px" size="mini">
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('role.roleName')">
                  <el-input v-model="object.roleName" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('role.roleNo')">
                  <el-input v-model="object.roleNo" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('role.sortNo')">
                  <el-input-number v-model="object.sortNo" :min="1" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('role.userType')">
                  <el-select v-model="object.userType">
                    <el-option
                      v-for="item in userTypeEnums"
                      :key="item.type"
                      :label="$t(item.value)"
                      :value="item.type"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('role.status')">
                  <el-switch v-model="object.status" :active-text="$t('commonStatus.valid')" :inactive-text="$t('commonStatus.invalid')" :active-value="1" :inactive-value="2" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item :label="$t('role.nodeLocation')">
              <el-tree
                ref="roleTreeForm"
                :data="roleTree"
                show-checkbox
                node-key="id"
                :default-checked-keys="[object.parentId]"
                :check-strictly="true"
                :props="defaultProps"
              />
            </el-form-item>
            <el-form-item :label="$t('role.remark')">
              <el-input
                v-model="object.remark"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 6}"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="$t('role.permissionConfiguration')" name="third">
          <el-table ref="menuTreeForm" row-key="menuId" :data="menuButtonOptionTree" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" border>
            <el-table-column
              prop="menuId"
              label="ID"
              width="104"
            />
            <el-table-column
              prop="menuText"
              :label="$t('menu.menuName')"
              width="104"
            />
            <el-table-column fixed="right" align="center" :label="$t('role.operation')">
              <template slot-scope="scope">
                <el-checkbox-group v-model="scope.row.relatedButtonIds" size="mini">
                  <el-checkbox-button v-for="item in scope.row.buttonOptionList" :key="item.buttonId" :label="item.buttonId" border>{{ item.buttonText }}</el-checkbox-button>
                </el-checkbox-group>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="actionType ==='ADD'" :lading="loadingTags.add" type="primary" @click="addData">{{ $t('operate.save') }}</el-button>
        <el-button v-else type="primary" :lading="loadingTags.edit" @click="modifyData">{{ $t('operate.edit') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { addRole, deleteRole, updateRole, listAll } from '@/api/authorization/role'
import { mapGetters } from 'vuex'
import { listMenuButtonOption } from '@/api/authorization/menu'
import { deepClone } from '@/utils/transferUtil'
import { getUserTypeEnum, UserTypeEnum } from '@/enums/system'
import i18n from '@/lang'
var statusOptions = [
  { key: '1', display_name: i18n.t('commonStatus.valid') },
  { key: '0', display_name: i18n.t('commonStatus.invalid') }
]
// arr to obj, such as { CN : "China", US : "USA" }
const statusTypeKeyValue = statusOptions.reduce((acc, cur) => {
  acc[cur.key] = cur.display_name
  return acc
}, {})
export default {
  name: 'RolePage',
  filters: {
    statusTagFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    },
    statusFilter(type) {
      return statusTypeKeyValue[type]
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userType: '',
      userTypeEnums: [],
      roleParam: {
        relatedUserIds: [],
        allUserIds: [],
        role: {},
        menuButtonKeys: []
      },
      loading: false,
      ifShow: true,
      roleTree: [],
      menuButtonOptionTree: [],
      defaultProps: {
        children: 'children',
        label: 'roleName'
      },
      dialogVisible: false,
      loadingTags: {
        add: false,
        edit: false
      },
      formLabelWidth: '80px',
      // 选项卡对应变量
      activeName: 'first',
      object: {},
      data: [],
      actionType: ''
    }
  },
  computed: {
    ...mapGetters([
      'userName',
      'userId',
      'refId'
    ])
  },
  created() {
    this.userTypeEnums = UserTypeEnum
    this.listTableData()
  },
  methods: {
    buildRoleTree(userType) {
      listAll(userType).then(r => {
        this.roleTree = r.data
      })
    },
    initMenuButtonOption(roleId) {
      listMenuButtonOption(roleId, 1).then(r => {
        this.menuButtonOptionTree = r.data
      })
    },
    listTableData() {
      const _this = this
      listAll(this.userType).then(r => {
        r.data.forEach(function(item, index, array) {
          item['edit'] = _this.edit
          item['delete'] = _this.delete
        })
        this.data = r.data
        this.total = r.data.total
        this.currentPage = r.data.pageNum
      })
    },
    removeData(data) {
      deleteRole(data).then(r => {
        this.listTableData()
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
      })
    },
    parseMenuParam(treeData, menuButtonKeys) {
      var _this = this
      treeData.forEach(function(item, index, array) {
        if (item.relatedButtonIds.length > 0) {
          menuButtonKeys.push({ menuId: item.menuId, buttonIdList: item.relatedButtonIds })
        }
        if (item.children.length > 0) {
          _this.parseMenuParam(item.children, menuButtonKeys)
        }
      })
    },
    initRoleParam() {
      this.roleParam.role = this.object
      if (!this.$refs.roleTreeForm.getCheckedKeys()) {
        this.roleParam.role.parentId = 0
      } else {
        this.roleParam.role.parentId = this.$refs.roleTreeForm.getCheckedKeys()[0]
      }
      delete this.roleParam.role.children
      delete this.roleParam.role.parent
      const menuButtonKeys = []
      this.parseMenuParam(this.menuButtonOptionTree, menuButtonKeys)
      this.roleParam.menuButtonKeys = menuButtonKeys
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogVisible = true
      this.object = deepClone(val)
      this.actionType = 'EDIT'
      this.buildRoleTree(val.userType)
      this.initMenuButtonOption(val.id)
    },
    modifyData() {
      this.initRoleParam(0)
      this.loadingTags.edit = true
      updateRole(this.roleParam).then(r => {
        this.dialogVisible = false
        this.$message({
          message: this.$t('operate.change') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
        this.listTableData()
      }).catch(() => {
        this.loadingTags.edit = false
        this.$message({
          message: this.$t('operate.change') + ' ' + this.$t('operate.message.fail'),
          type: 'error'
        })
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogVisible = true
      this.object = { status: 1, sortNo: 10 }
      this.actionType = 'ADD'
      this.buildRoleTree()
      this.initMenuButtonOption(0)
    },
    addData() {
      this.loadingTags.add = true
      this.initRoleParam()
      addRole(this.roleParam).then(r => {
        this.dialogVisible = false
        this.$message({
          message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
        this.listTableData()
      }).catch(() => {
        this.loadingTags.add = false
        this.$message({
          message: this.$t('operate.add') + ' ' + this.$t('operate.message.success'),
          type: 'error'
        })
      })
    },
    handleRemoveClick(val) {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.object = val
        delete this.object.children
        delete this.object.parent
        this.removeData(this.object)
      })
    },
    userTypeEnumFormat: function(row, column, cellValue) {
      if (!cellValue) {
        return ''
      } else {
        return this.$t(getUserTypeEnum(cellValue))
      }
    }
  }
}
</script>
