import request from '@/utils/request'

export function listTableInfo(data) {
  return request({
    url: '/table-info/list',
    method: 'post',
    data
  })
}
export function listAllTableInfo() {
  return request({
    url: '/table-info/all',
    method: 'post'
  })
}
export function addTableInfo(data) {
  return request({
    url: '/table-info/add',
    method: 'post',
    data
  })
}
export function updateTableInfo(data) {
  return request({
    url: '/table-info/update',
    method: 'post',
    data
  })
}
export function deleteTableInfo(ids) {
  return request({
    url: '/table-info/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}

export function getTableInfo(id) {
  return request({
    url: '/table-info/get',
    method: 'get',
    params: {
      id: id
    }
  })
}
