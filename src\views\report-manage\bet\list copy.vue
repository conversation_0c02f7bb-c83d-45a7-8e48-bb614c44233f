<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-select v-show="hide" v-model="memberParam.isHide">
            <el-option v-for="item in hideEnums" :key="item.type" :label="$t(item.label)" :value="item.type" />
          </el-select>
          <el-date-picker v-model="memberParam.date" type="daterange" align="right" unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')" :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions" />
          <!-- <el-select v-model="memberParam.gameType" :placeholder="$t('betInfo.placeholder.gameType')">
            <el-option
              v-for="item in gameTypeEnums"
              :key="item.type"
              :label="$t(item.label)"
              :value="item.type"
            />
          </el-select>
         <el-input v-model="memberParam.chipInNo" style="width:184px" :placeholder="$t('betInfo.placeholder.gameType')" clearable /> -->
          <el-input v-model="memberParam.tableNo" style="width:184px" :placeholder="$t('betInfo.placeholder.tableNo')"
            clearable />
          <el-input v-model="memberParam.matchNo" style="width:184px" :placeholder="$t('betInfo.placeholder.matchNo')"
            clearable />
          <el-input v-model="memberParam.gameNo" style="width:184px" :placeholder="$t('betInfo.placeholder.gameNo')"
            clearable />
          <el-input v-model="memberParam.userName" style="width:224px"
            :placeholder="$t('betInfo.placeholder.userAccount')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border
      highlight-current-row show-summary :summary-method="getSummaries" @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <!--<el-table-column prop="chipInNo" align="center" :label="$t('betInfo.betNo')" width="154" /> -->
      <el-table-column prop="tableInfo.tableNo" align="center" :label="$t('betInfo.tableNo')" width="84" />
      <el-table-column prop="gameMatch.matchNo" align="center" :label="$t('betInfo.shoeNo')" width="84" />
      <el-table-column prop="game.gameNo" align="center" :label="$t('betInfo.gameNo')" width="84" />
      <el-table-column prop="user.userName" align="center" :label="$t('betInfo.memberAccount')" width="144" />
      <!--<el-table-column prop="user.nickName" align="center" :label="$t('betInfo.memberName')" width="124" />-->
      <el-table-column prop="chipInAmount" align="center" :label="$t('betInfo.betAmount')" width="104">
        <template slot-scope="scope">
          <div> {{ scope.row.chipInAmount | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="chipInResult" align="center" :label="$t('betInfo.betDetail')" width="164"
        show-overflow-tooltip :formatter="gameChipFormat" />
      <el-table-column prop="gameResult" align="center" :label="$t('betInfo.cardType')" width="104">
        <template slot-scope="scope">
          <div v-if="scope.row.game">
            <el-popover trigger="hover" placement="right">
              <div class="container">
                <div v-if="scope.row.gameType == 1 || scope.row.gameType == 2" class="mipai-box">
                  <div class="zhuang-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.game.bankerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.game.bankerB, 1) + ' on'" />
                    <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.game.bankerC, 2) + ' on'" />
                    <div class="total-box on">{{ scope.row.game.bankerPoint }}</div>
                    <div class="title-box on">
                      {{ $t('game.bankerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="xian-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.game.playerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.game.playerB, 1) + ' on'" />
                    <div :class="'smallcard card3 hcardpicmiddle ' + getCardType(scope.row.game.playerC, 2) + ' on'" />
                    <div class="total-box on">{{ scope.row.game.playerPoint }}</div>
                    <div class="title-box on ">
                      {{ $t('game.playerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="clear" />
                </div>
                <div v-else class="mipai-box mipai-box2">
                  <div class="zhuang-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.game.bankerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.game.bankerB, 1) + ' on'" />
                    <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.game.bankerC, 2) + ' on'" />
                    <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.game.bankerD, 3) + ' on'" />
                    <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.game.bankerE, 4) + ' on'" />
                    <div class="total-box on">{{ getBull(scope.row.game.bankerPoint) }}</div>
                    <div class="title-box on">
                      {{ $t('game.bankerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="xian-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.game.playerA, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.game.playerB, 1) + ' on'" />
                    <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.game.playerC, 2) + ' on'" />
                    <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.game.playerD, 3) + ' on'" />
                    <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.game.playerE, 4) + ' on'" />
                    <div class="total-box on">{{ getBull(scope.row.game.playerPoint) }}</div>
                    <div class="title-box on ">
                      {{ $t('game.playerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="xian-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.game.player2A, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.game.player2B, 1) + ' on'" />
                    <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.game.player2C, 2) + ' on'" />
                    <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.game.player2D, 3) + ' on'" />
                    <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.game.player2E, 4) + ' on'" />
                    <div class="total-box on">{{ getBull(scope.row.game.player2Point) }}</div>
                    <div class="title-box on ">
                      {{ $t('game.playerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="xian-box">
                    <div :class="'smallcard card1 vcardpicmiddle ' + getCardType(scope.row.game.player3A, 0) + ' on'" />
                    <div :class="'smallcard card2 vcardpicmiddle ' + getCardType(scope.row.game.player3B, 1) + ' on'" />
                    <div :class="'smallcard card3 vcardpicmiddle ' + getCardType(scope.row.game.player3C, 2) + ' on'" />
                    <div :class="'smallcard card4 vcardpicmiddle ' + getCardType(scope.row.game.player3D, 3) + ' on'" />
                    <div :class="'smallcard card5 vcardpicmiddle ' + getCardType(scope.row.game.player3E, 4) + ' on'" />
                    <div class="total-box on">{{ getBull(scope.row.game.player3Point) }}</div>
                    <div class="title-box on ">
                      {{ $t('game.playerAbbr') }}
                    </div>
                    <div class="notice-box" />
                  </div>
                  <div class="clear" />
                </div>
              </div>
              <div slot="reference" class="name-wrapper">
                <el-tag size="medium">{{ $t('betInfo.cardsView') }}</el-tag>
              </div>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="gameResult" align="center" :label="$t('betInfo.cardResult')" :formatter="gameResultFormat"
        width="104" />
      <el-table-column prop="winAmount" align="center" :label="$t('betInfo.winAmount')" width="104">
        <template slot-scope="scope">
          <div> {{ scope.row.winAmount | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="balance" align="center" :label="$t('betInfo.banlance')" width="104">
        <template slot-scope="scope">
          <div> {{ scope.row.balance | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="washAmout" align="center" :label="$t('betInfo.washAmount')" width="104">
        <template slot-scope="scope">
          <div> {{ scope.row.washAmout | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="washRate" align="center" :label="$t('betInfo.washRate')" width="104">
        <template slot-scope="scope">
          {{ scope.row.washRate ? scope.row.washRate + '%' : 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="washAmout" align="center" :label="$t('betInfo.commission')" width="94">
        <template slot-scope="scope">
          {{ scope.row.washAmout * scope.row.washRate / 100 | numberFilter | moneyFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="chipInDt" align="center" :label="$t('betInfo.betTime')" width="144"
        :formatter="dateTimeFormat" />
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
  import { listGameTransHistInfo, getPersonalWashInfo } from '@/api/game/gameTransHist'
  import { getGameTypeEnum, GameTypeEnum, HideEnum } from '@/enums/setting.js'
  import moment from 'moment'
  import { mapGetters } from 'vuex'
  import { formatMoney, formatNumber } from '@/utils/formatter'
  import { gameResultFilter } from '@/filters/gameResult'
  export default {
    name: 'MemberList',
    filters: {
      numberFilter(data) {
        return formatNumber(data)
      },
      moneyFilter(money) {
        return formatMoney(money)
      }
    },
    props: {
      add: {
        type: Boolean,
        default: false
      },
      edit: {
        type: Boolean,
        default: false
      },
      delete: {
        type: Boolean,
        default: false
      },
      hide: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        memberParam: {
          memberType: 3,
          isHide: 0
        },
        objectList: [],
        multipleSelection: [],
        currentPage: 1,
        total: 0,
        pageSize: 10,
        ids: [],
        dialogFormVisible: false,
        dialogPassVisible: false,
        currentUserId: 0,
        pickerOptions: {
          shortcuts: [{
            text: this.$t('dateTemplate.lastWeek'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: this.$t('dateTemplate.lastMonth'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }, {
            text: this.$t('dateTemplate.last3Months'),
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }]
        },
        sums: []
      }
    },
    computed: {
      ...mapGetters([
        'member',
        'userType'
      ]),
      gameTypeEnums() {
        return GameTypeEnum
      },
      hideEnums() {
        return HideEnum
      }
    },
    watch: {
      multipleSelection: function () {
        const arr = []
        for (const i in this.multipleSelection) {
          arr.push(this.multipleSelection[i].id)
        }
        this.ids = arr.join()
      }
    },
    created() {
      this.listTableData()
    },
    methods: {
      formatMoney,
      formatNumber,
      getBull(point) {
        if (point === 0) {
          return this.$t('bull.noBull')
        } else if (point >= 10) {
          return this.$t('bull.bull')
        } else {
          return point
        }
      },
      handFilter() {
        this.currentPage = 1
        this.listTableData()
      },
      listTableData() {
        if (!this.hide) {
          this.memberParam.isHide = 1
        }
        if (this.memberParam.date) {
          this.memberParam.dateFrom = this.memberParam.date[0]
          this.memberParam.dateTo = this.memberParam.date[1]
        } else {
          this.memberParam.dateFrom = ''
          this.memberParam.dateTo = ''
        }
        if (this.userType === 4) {
          this.memberParam.parentId = 0
        } else {
          if (this.member.parentId) {
            this.memberParam.parentId = this.member.id
          } else {
            this.memberParam.parentId = 0
          }
        }
        this.memberParam.pageSize = this.pageSize
        this.memberParam.pageNo = this.currentPage
        this.getWashInfo()
        listGameTransHistInfo(this.memberParam).then(r => {
          this.sumTotal(r.data.list)
          this.objectList = r.data.list
          this.total = r.data.total
          console.log('listGameTransHistInfo.....................', r.data.list)
        })
      },
      getWashInfo() {
        getPersonalWashInfo(this.memberParam).then(r => {
          console.log(r.data)
        })
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      handleSizeChange(val) {
        this.pageSize = val
        this.listTableData()
      },
      handleCurrentChange(val) {
        this.currentPage = val
        this.listTableData()
      },
      dateTimeFormat: function (row, column) {
        var date = row[column.property]
        if (!date) {
          return ''
        }
        return moment(date).format('YYYY-MM-DD HH:mm:ss')
      },
      gameResultFormat: function (row, column) {
        const items = gameResultFilter(row.gameType, row.gameResult)
        var tempItems = []
        for (var i = 0; i < items.length; i++) {
          tempItems.push(this.$t(items[i]))
        }
        return tempItems.join(',')
      },
      cardsFormat: function (row, column) {
        return gameResultFilter(row.gameType, row.gameResult)
      },
      gameCardsFiter(gameTrans) {
        var chipItems = []
        var bankerCardDesc = ''
        if (gameTrans.bankerA) {
          chipItems.push(gameTrans.bankerA)
        }
        if (gameTrans.bankerB) {
          chipItems.push(gameTrans.bankerB)
        }
        if (gameTrans.bankerC) {
          chipItems.push(gameTrans.bankerC)
        }
        if (gameTrans.bankerD) {
          chipItems.push(gameTrans.bankerD)
        }
        if (gameTrans.bankerE) {
          chipItems.push(gameTrans.bankerE)
        }
        bankerCardDesc = chipItems.join(',')
        return bankerCardDesc
      },
      getCardType(card, index) {
        if (!card) {
          return ''
        }
        var cardPoint = Number(card.substr(2, 2))
        var point = cardPoint % 13
        if (point === 0) {
          point = 13
        }
        var suitStr = ''
        var suit = Math.ceil(cardPoint / 13)

        if (suit === 1) suitStr = 's'
        else if (suit === 2) suitStr = 'h'
        else if (suit === 3) suitStr = 'd'
        else if (suit === 4) suitStr = 'c'
        return 'type' + suitStr + point
      },
      gameChipFormat(row, column) {
        return this.gameChipFiter(row)
      },
      gameTypeFilter(row, column) {
        return this.$t(getGameTypeEnum(row.gameType).label)
      },
      gameChipFiter(gameTrans) {
        var gameResultDesc = ''
        var chipItems = []
        switch (Number(gameTrans.gameType)) {
          case 1:
            if (gameTrans.bankerAmout) {
              chipItems.push(this.$t('game.banker') + ': ' + gameTrans.bankerAmout)
            }
            if (gameTrans.bankerPairAmout) {
              chipItems.push(this.$t('game.bankerPair') + ': ' + gameTrans.bankerPairAmout)
            }
            if (gameTrans.bankerSuperAmout) {
              chipItems.push(this.$t('game.luckSix') + ': ' + gameTrans.bankerSuperAmout)
            }
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.player') + ': ' + gameTrans.playerAmout)
            }
            if (gameTrans.playerPairAmout) {
              chipItems.push(this.$t('game.playerPair') + ': ' + gameTrans.playerPairAmout)
            }
            if (gameTrans.tieAmout) {
              chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
          case 2:
            if (gameTrans.bankerAmout) {
              chipItems.push(this.$t('game.tiger') + ': ' + gameTrans.bankerAmout)
            }
            if (gameTrans.bankerPairAmout) {
              chipItems.push(this.$t('game.pair') + ': ' + gameTrans.bankerPairAmout)
            }
            if (gameTrans.tieAmout) {
              chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
            }
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.dragon') + ': ' + gameTrans.playerAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
          case 3:
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.player1Equal') + ': ' + gameTrans.playerAmout)
            }
            if (gameTrans.playerPairAmout) {
              chipItems.push(this.$t('game.player1Double') + ': ' + gameTrans.playerPairAmout)
            }
            if (gameTrans.playerSuperAmout) {
              chipItems.push(this.$t('game.player1Super') + ': ' + gameTrans.playerSuperAmout)
            }
            if (gameTrans.player2Amout) {
              chipItems.push(this.$t('game.player2Equal') + ': ' + gameTrans.player2Amout)
            }
            if (gameTrans.player2PairAmout) {
              chipItems.push(this.$t('game.player2Double') + ': ' + gameTrans.player2PairAmout)
            }
            if (gameTrans.player2SuperAmout) {
              chipItems.push(this.$t('game.player2Super') + ': ' + gameTrans.player2SuperAmout)
            }
            if (gameTrans.player3Amout) {
              chipItems.push(this.$t('game.player3Equal') + ': ' + gameTrans.player3Amout)
            }
            if (gameTrans.player3PairAmout) {
              chipItems.push(this.$t('game.player3Double') + ': ' + gameTrans.player3PairAmout)
            }
            if (gameTrans.player3SuperAmout) {
              chipItems.push(this.$t('game.player3Super') + ': ' + gameTrans.player3SuperAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
        }
        return gameResultDesc
      },
      getSummaries(param) {
        const { columns } = param
        const sums = []
        columns.forEach((column, index) => {
          if (this.sums.length) {
            sums[index] = this.sums[index]
          }
        })
        return sums
      },
      sumTotal(objectList) {
        this.sums = ['', '', '', '', '', '', '', '', 0, '', '', '', 0, 0, 0, '', 0, '']
        this.sums[2] = this.$t('game.totalText')
        for (var i = 0; i < objectList.length; i++) {
          // 6-下注金额 10-输赢 11-余额 12-码量  14-码佣
          this.sums[12] = this.sums[12] + objectList[i].winAmount
          // this.sums[13] = this.sums[13] + objectList[i].balance
          this.sums[14] = this.sums[14] + objectList[i].washAmout
          this.sums[16] = this.sums[16] + (objectList[i].washAmout * objectList[i].washRate / 100)
          this.sums[8] = this.sums[8] + objectList[i].bankerAmout +
            objectList[i].bankerPairAmout +
            objectList[i].bankerSuperAmout +
            objectList[i].playerAmout +
            objectList[i].playerPairAmout +
            objectList[i].tieAmout
        }
        for (i = 0; i < this.sums.length; i++) {
          if (i > 2 && this.sums[i]) {
            this.sums[i] = formatMoney(formatNumber(this.sums[i]))
          }
        }
      }
    }
  }
</script>
<style lang="scss" scoped>

  /*开牌 */
  .container .mipai-box {
    top: 0%;
    left: 0%;
    width: 175px;
    height: 125px;
    position: absolute;
    flex-direction: column;
    border-radius: .5rem;
    z-index: 999;
    padding-top: 1%;
    background: #131313;
    opacity: 0.9;
    border-radius: 0.5rem;
  }

  .container .mipai-box2 {
    width: 250px;
    height: 250px;
  }

  .container .mipai-box.active {
    display: block;
    animation: bounce02 1.5s;
  }

  .container .mipai-box.active-a {
    display: block !important;
    animation: bounce03 1.5s;
  }

  .container .mipai-box.inactive {
    display: none;
  }

  .container .mipai-box .xian-box,
  .container .mipai-box .zhuang-box {
    height: 60px;
    position: relative;
    display: flex;
    border-bottom: 1px solid #222222;
  }

  .container .mipai-box .total-box {
    font-size: 16px;
    position: absolute;
    left: 5px;
    top: 30px;
    padding: 0 5px 0 5px;
    text-align: center;
    border-radius: 50%;
  }

  .container .mipai-box .title-box {
    position: absolute;
    font-size: 16px;
    top: 5px;
    left: 5px;
    text-align: center;
  }

  .container .mipai-box .zhuang-box .total-box,
  .container .mipai-box .long-box .total-box {
    color: #ffffff;
    background-color: #c1182e;
  }

  .container .mipai-box .xian-box .total-box,
  .container .mipai-box .hu-box .total-box {
    color: #ffffff;
    background-color: #2b4be9;
  }

  .container .mipai-box .zhuang-box .title-box,
  .container .mipai-box .long-box .title-box {
    color: #c1182e;
  }

  .container .mipai-box .xian-box .title-box,
  .container .mipai-box .hu-box .title-box,
  .container .mipai-box .feng-box .title-box {
    color: #2b4be9;
  }

  .container .mipai-box .smallcard {
    position: absolute;
  }

  .container .mipai-box .smallcard.on {
    display: block;
  }

  .container .mipai-box .smallcard.card1 {
    top: 10px;
    height: 40px;
    left: 40px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card2 {
    top: 10px;
    height: 40px;
    left: 80px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card3 {
    top: 20px;
    height: 30px;
    left: 120px;
    width: 40px;
  }

  .container .mipai-box2 .smallcard.card3 {
    top: 10px;
    height: 40px;
    left: 120px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card4 {
    top: 10px;
    height: 40px;
    left: 160px;
    width: 30px;
  }

  .container .mipai-box .smallcard.card5 {
    top: 10px;
    height: 40px;
    left: 200px;
    width: 30px;
  }
</style>
