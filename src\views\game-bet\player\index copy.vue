<template>
  <div v-if="device === 'mobile'" class="app-container mobile">
    <mobile-header title="下注端" @back="handleBack" />
    <div class="card-box">
      <div class="ex-table-container">
        <table class="ex-table">
          <tr class="card-name">
            <td>A</td>
            <td>2</td>
            <td>3</td>
            <td>4</td>
            <td>5</td>
            <td>6</td>
            <td>7</td>
            <td>8</td>
            <td>9</td>
            <td>10</td>
            <td>J</td>
            <td>Q</td>
            <td>K</td>
          </tr>
          <tr>
            <td :class="getRemainCardsClass(remainCards.card1)">{{ remainCards.card1 }}</td>
            <td :class="getRemainCardsClass(remainCards.card2)">{{ remainCards.card2 }}</td>
            <td :class="getRemainCardsClass(remainCards.card3)">{{ remainCards.card3 }}</td>
            <td :class="getRemainCardsClass(remainCards.card4)">{{ remainCards.card4 }}</td>
            <td :class="getRemainCardsClass(remainCards.card5)">{{ remainCards.card5 }}</td>
            <td :class="getRemainCardsClass(remainCards.card6)">{{ remainCards.card6 }}</td>
            <td :class="getRemainCardsClass(remainCards.card7)">{{ remainCards.card7 }}</td>
            <td :class="getRemainCardsClass(remainCards.card8)">{{ remainCards.card8 }}</td>
            <td :class="getRemainCardsClass(remainCards.card9)">{{ remainCards.card9 }}</td>
            <td :class="getRemainCardsClass(remainCards.card10)">{{ remainCards.card10 }}</td>
            <td :class="getRemainCardsClass(remainCards.card11)">{{ remainCards.card11 }}</td>
            <td :class="getRemainCardsClass(remainCards.card12)">{{ remainCards.card12 }}</td>
            <td :class="getRemainCardsClass(remainCards.card13)">{{ remainCards.card13 }}</td>
          </tr>
        </table>
      </div>
      <el-form ref="postForm" label-width="100px" :model="gameInfo">
        <div class="span-title mb-5" @click="handleType">下注提示</div>
        <el-form-item label="台号">
          <el-select v-model="gameInfo.tableId" filterable clearable placeholder=" " class="input"
            @change="handleTableId">
            <el-option v-for="item in tableList" :key="item.id" :label="item.tableNo" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="下注方向" v-if="type">
          <el-radio-group v-model="gameInfo.type">
            <el-radio :label="1">正向</el-radio>
            <el-radio :label="2">反向</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="基码金额">
          <el-input v-model="gameInfo.baseAmount" />
        </el-form-item>
        <el-form-item label="下注金额">
          <el-input v-model="gameInfo.amount" disabled />
        </el-form-item>
        <el-row class="mt-10">
          <el-col>
            <div v-if="gameInfo.betResult === 'B'" class="banker-text" style="font-size: 1.8rem !important;">
              B {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
            <div v-if="gameInfo.betResult === 'P'" class="player-text" style="font-size: 1.8rem !important;">
              P {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="span-title mb-5">实际下注</div>
        <el-form-item label="下注金额">
          <el-input v-model="gameInfo.chipAmount" />
        </el-form-item>
        <el-form-item label="B/P">
          <el-radio-group v-model="gameInfo.chipType">
            <el-radio label="P">P</el-radio>
            <el-radio label="B">B</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row type="flex" justify="center" class="mt-10">
          <el-button type="primary" class="w-150" @click="handleSaveGame">下注</el-button>
        </el-row>
        <div class="span-title mb-5">下注统计</div>
        <el-form-item label="本局下注基码">
          <el-input v-model="gameTransHist.currentBase" disabled />
        </el-form-item>
        <el-form-item label="本局下注金额">
          <el-input v-model="gameTransHist.currentAmount" disabled />
        </el-form-item>
        <el-form-item label="累计下注基码">
          <el-input v-model="gameTransHist.chipBase" disabled />
        </el-form-item>
        <el-form-item label="累计下注金额">
          <el-input v-model="gameTransHist.chipAmount" disabled />
        </el-form-item>
        <el-form-item label="累计输赢基码">
          <el-input v-model="gameTransHist.winBase" disabled />
        </el-form-item>
        <el-form-item label="累计输赢金额">
          <el-input v-model="gameTransHist.winAmount" disabled />
        </el-form-item>
      </el-form>
    </div>
  </div>
  <div v-else class="app-container">
    <div class="card-box">
      <div class="ex-table-container">
        <table class="ex-table">
          <tr class="card-name">
            <td>A</td>
            <td>2</td>
            <td>3</td>
            <td>4</td>
            <td>5</td>
            <td>6</td>
            <td>7</td>
            <td>8</td>
            <td>9</td>
            <td>10</td>
            <td>J</td>
            <td>Q</td>
            <td>K</td>
          </tr>
          <tr>
            <td :class="getRemainCardsClass(remainCards.card1)">{{ remainCards.card1 }}</td>
            <td :class="getRemainCardsClass(remainCards.card2)">{{ remainCards.card2 }}</td>
            <td :class="getRemainCardsClass(remainCards.card3)">{{ remainCards.card3 }}</td>
            <td :class="getRemainCardsClass(remainCards.card4)">{{ remainCards.card4 }}</td>
            <td :class="getRemainCardsClass(remainCards.card5)">{{ remainCards.card5 }}</td>
            <td :class="getRemainCardsClass(remainCards.card6)">{{ remainCards.card6 }}</td>
            <td :class="getRemainCardsClass(remainCards.card7)">{{ remainCards.card7 }}</td>
            <td :class="getRemainCardsClass(remainCards.card8)">{{ remainCards.card8 }}</td>
            <td :class="getRemainCardsClass(remainCards.card9)">{{ remainCards.card9 }}</td>
            <td :class="getRemainCardsClass(remainCards.card10)">{{ remainCards.card10 }}</td>
            <td :class="getRemainCardsClass(remainCards.card11)">{{ remainCards.card11 }}</td>
            <td :class="getRemainCardsClass(remainCards.card12)">{{ remainCards.card12 }}</td>
            <td :class="getRemainCardsClass(remainCards.card13)">{{ remainCards.card13 }}</td>
          </tr>
        </table>
      </div>
      <el-form ref="postForm" label-width="180px" :model="gameInfo">
        <div class="span-title mb-5" @dblclick="handleType">下注提示</div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="台号">
              <el-select v-model="gameInfo.tableId" filterable clearable placeholder=" " class="input"
                @change="handleTableId">
                <el-option v-for="item in tableList" :key="item.id" :label="item.tableNo" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="type">
            <el-form-item label="下注方向">
              <el-radio-group v-model="gameInfo.type">
                <el-radio :label="1">正向</el-radio>
                <el-radio :label="2">反向</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mb-10">
          <el-col :span="12">
            <el-form-item label="基码金额">
              <el-input v-model="gameInfo.baseAmount" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下注金额">
              <el-input v-model="gameInfo.amount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="mb-10">
          <el-col>
            <div v-if="gameInfo.betResult === 'B'" class="banker-text" style="font-size: 1.8rem !important;">
              B {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
            <div v-if="gameInfo.betResult === 'P'" class="player-text" style="font-size: 1.8rem !important;">
              P {{ gameInfo.betAmount }} <br /> <span class="win-tips" v-if="gameInfo.profitResult">{{
                gameInfo.profitResult }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="span-title mb-5">实际下注</div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="下注金额">
              <el-input v-model="gameInfo.chipAmount" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="B/P">
              <el-radio-group v-model="gameInfo.chipType">
                <el-radio label="P">P</el-radio>
                <el-radio label="B">B</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="mt-10">
          <el-button type="primary" class="w-20p" @click="handleSaveGame">下注</el-button>
        </el-row>
        <div class="span-title mb-5">下注统计</div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="本局下注基码">
              <el-input v-model="gameTransHist.currentBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="本局下注金额">
              <el-input v-model="gameTransHist.currentAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="累计下注基码">
              <el-input v-model="gameTransHist.chipBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="累计下注金额">
              <el-input v-model="gameTransHist.chipAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="累计输赢基码">
              <el-input v-model="gameTransHist.winBase" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="累计输赢金额">
              <el-input v-model="gameTransHist.winAmount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <!--<el-row class="mb-10">
          <el-col :span="12">
            <el-form-item>
              <div class="banker-text" style="font-size: 1.8rem !important;">
                B
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <div class="player-text" style="font-size: 1.8rem !important;">
                P
              </div>
            </el-form-item>
          </el-col>
        </el-row>-->
      </el-form>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { listTableInfo, getTableInfo } from '@/api/setting/tableInfo'
  import { listGameResult } from '@/api/game/game'
  import { addGameTransHist, listGameTransHist } from '@/api/game/gameTransHist'
  import { gameResultFilter } from '@/filters/gameResult'
  import { getGameTypeEnum } from '@/enums/setting.js'
  import moment from 'moment'

  export default {
    name: 'BetPlayer',
    data() {
      return {
        socketInfo: null,
        objectList: [],
        tableList: [],
        gameInfo: { tableId: null, matchId: null, type: 1, baseAmount: 50, amount: null, betAmount: null, betResult: null, chipType: null, chipAmount: null, profitResult: null },
        gameTransHist: { currentBase: 0, currentAmount: 0, winAmount: 0, winBase: 0, chipAmount: 0, chipBase: 0 },
        remainCards: { card1: 0, card2: 0, card3: 0, card4: 0, card5: 0, card6: 0, card7: 0, card8: 0, card9: 0, card10: 0, card11: 0, card12: 0, card13: 0 },
        type: false,
        loadingTags: { bet: false }
      }
    },
    computed: {
      socketInfoOrginal: {
        get() {
          return this.$store.state.app.socketInfo
        }
      },
      ...mapGetters(['member', 'userName', 'userType', 'device'])
    },
    watch: {
      socketInfoOrginal(val) {
        if (val) {
          this.socketInfo = val
          this.handleSocketInfo()
        }
      }
    },
    created() {
      this.getTableList()
      console.log('player created...........................1')
    },
    methods: {
      handleType() {
        this.type = !this.type
        console.log('handleType...................')
      },
      getRemainCardsClass(qty) {
        if (qty === this.remainCards.card0) {
          return 'remain-all'
        } else if (qty === 0) {
          return 'remain-zero'
        } else {
          return 'remain-part'
        }
      },
      handleRemainCards() {
        if (!this.gameInfo.cardsQty) {
          this.gameInfo.cardsQty = 8
        }
        var cardsQty = parseInt(this.gameInfo.cardsQty) * 4
        this.remainCards.card0 = cardsQty
        for (var i = 1; i <= 13; i++) {
          this.remainCards['card' + i] = cardsQty
        }
        var value = 1
        for (var item of this.objectList) {
          this.getCardValue(item.playerA)
          this.getCardValue(item.playerB)
          this.getCardValue(item.playerC)
          this.getCardValue(item.bankerA)
          this.getCardValue(item.bankerB)
          this.getCardValue(item.bankerC)
        }
      },
      getCardValue(card) {
        if (!card) {
          return ''
        }
        var cardPoint = Number(card.substr(2, 2))
        var point = cardPoint % 13
        this.remainCards['card' + point] = this.remainCards['card' + point] - 1
      },
      handleBack() {
        this.$router.push({ path: '/dashboard/index' })
      },
      handleSaveGame() {
        if (this.gameTransHist.currentAmount) {
          this.$message.error('已下注，等待开牌')
          return
        }
        if (this.loadingTags.bet) {
          this.$message.error('下注中，请稍后')
          return
        }
        if (!this.gameInfo.tableId) {
          this.$message.error('请选择台号')
          return
        }
        if (!this.gameInfo.matchId) {
          this.$message.error('暂无下注的局')
          return
        }
        if (!this.gameInfo.baseAmount) {
          this.$message.error('请输入基码金额')
          return
        }
        if (!this.gameInfo.chipAmount) {
          this.$message.error('请输入下注金额')
          return
        }
        if (!this.gameInfo.chipType) {
          this.$message.error('请选择B/P')
          return
        }
        var bankerAmout = 0
        var playerAmout = 0
        var bankerRate = 0
        var playerRate = 0
        if (this.gameInfo.chipType === 'B') {
          bankerAmout = parseFloat(this.gameInfo.chipAmount)
          if (this.gameInfo.baseAmount) {
            bankerRate = parseFloat(this.gameInfo.chipAmount) / parseFloat(this.gameInfo.baseAmount)
          }
        } else {
          playerAmout = parseFloat(this.gameInfo.chipAmount)
          if (this.gameInfo.baseAmount) {
            playerRate = parseFloat(this.gameInfo.chipAmount) / parseFloat(this.gameInfo.baseAmount)
          }
        }
        this.gameTransHist.currentAmount = this.gameTransHist.currentAmount + bankerAmout + playerAmout
        this.gameTransHist.currentBase = this.gameTransHist.currentBase + bankerRate + playerRate

        var gameTransHist = {
          id: null, parentId: 0, verifyId: 0, chipInNo: moment().format('YYYYMMDDHHmmss'), chipInDt: null,
          memberId: this.member.id, tableId: this.gameInfo.tableId, matchId: this.gameInfo.matchId, gameId: 0,
          gameType: 1, gameCategory: 1,
          bankerRate: bankerRate, bankerAmout: bankerAmout, playerRate: playerRate, playerAmout: playerAmout,
          gameResult: '', chipInResult: this.gameInfo.chipType, chipInAmount: bankerAmout + playerAmout, winAmount: 0, balance: 0, washAmount: 0, status: 0
        }
        this.loadingTags.bet = true
        addGameTransHist(gameTransHist).then(res => {
          /* this.gameInfo.chipAmount = null
          if (!this.gameTransHist.currentAmount && this.gameInfo.amount) {
            this.gameInfo.chipAmount = this.gameInfo.amount
          } */
          this.loadingTags.bet = false
          this.$message.success('下注成功')
          console.log(res)
        }).catch(() => {
          this.$message.success('下注失败')
          this.loadingTags.bet = false
        })
        // 基码金额	100
        // 当前下注基码个数	3		当前下注金额	300
        // 累计下注基码个数	25		累计金额	2500
        // 输赢情况基码个数	2		输赢金额	200
      },
      handleGameTransHist() {
        var params = { memberId: this.member.id, matchId: this.gameInfo.matchId }
        listGameTransHist(params).then(res => {
          console.log('handleGameTransHist..............................')
          console.log(res.data.list)
          this.sumGameTransHist(res.data.list)
        })
      },
      sumGameTransHist(list) {
        var winAmount = 0
        var winBase = 0
        var chipAmount = 0
        var chipBase = 0
        for (var item of list) {
          winAmount = winAmount + item.winAmount
          if (item.winAmount > 0) {
            winBase = winBase + item.bankerRate + item.playerRate
          } else {
            winBase = winBase - item.bankerRate - item.playerRate
          }
          chipAmount = chipAmount + item.chipInAmount
          chipBase = chipBase + item.bankerRate + item.playerRate
        }
        this.gameTransHist.currentAmount = 0
        this.gameTransHist.currentBase = 0

        this.gameTransHist.winAmount = winAmount
        this.gameTransHist.winBase = winBase
        this.gameTransHist.chipAmount = chipAmount
        this.gameTransHist.chipBase = chipBase
      },
      handleSocketInfo() {
        console.log('play1................................1')
        console.log(this.socketInfo)
        if (this.socketInfo.tableId === this.gameInfo.tableId) {
          console.log('play1................................2')
          this.gameInfo.profitResult = ''
          if (this.gameInfo.type === 1) {
            this.gameInfo.betResult = this.socketInfo.player2A
            this.gameInfo.betAmount = this.socketInfo.player2Amount
            if (this.socketInfo.profitResult) {
              this.gameInfo.profitResult = this.socketInfo.profitResult
            }
          } else {
            this.gameInfo.betResult = this.socketInfo.player3A
            this.gameInfo.betAmount = this.socketInfo.player3Amount
            if (this.socketInfo.profitResult2) {
              this.gameInfo.profitResult = this.socketInfo.profitResult2
            }
          }
          console.log('play1................................3')
          if (this.gameInfo.baseAmount) {
            this.gameInfo.amount = parseFloat(this.gameInfo.baseAmount) * this.gameInfo.betAmount
          }
          this.gameInfo.matchId = this.socketInfo.matchId
          console.log('play1................................4')
          this.gameInfo.chipType = this.gameInfo.betResult
          this.gameInfo.chipAmount = this.gameInfo.amount
        }
        console.log(this.gameInfo)
        this.getGameResult()
      },
      getTableList() {
        var agentParam = { ifPage: false }
        listTableInfo(agentParam).then(r => {
          this.tableList = r.data.list
          console.log(this.objectList)
        })
      },
      handleTableId() {
        this.getTable()
      },
      getTable() {
        getTableInfo(this.gameInfo.tableId).then(res => {
          this.tableInfo = res.data
          this.gameInfo.matchId = this.tableInfo.matchId
          this.gameInfo.gameId = this.tableInfo.gameId
          this.getGameResult()
        })
      },
      getGameResult() {
        var gameInfo = { tableId: this.gameInfo.tableId, matchIdList: [this.gameInfo.matchId] }
        listGameResult(gameInfo).then(res => {
          console.log('getGameResult..................')
          console.log(res)
          this.objectList = res.data.list
          this.handleRemainCards()
          this.handleGameTransHist()
        })
      },
      gameResultFormat: function (row, column) {
        const items = gameResultFilter(row.gameType, row.gameResult)
        var tempItems = []
        for (var i = 0; i < items.length; i++) {
          tempItems.push(this.$t(items[i]))
        }
        return tempItems.join(',')
      },
      getCardType(card, index) {
        if (!card) {
          return ''
        }
        var cardPoint = Number(card.substr(2, 2))
        var point = cardPoint % 13
        if (point === 0) {
          point = 13
        }
        var suitStr = ''
        var suit = Math.ceil(cardPoint / 13)

        if (suit === 1) suitStr = 's'
        else if (suit === 2) suitStr = 'h'
        else if (suit === 3) suitStr = 'd'
        else if (suit === 4) suitStr = 'c'
        return 'type' + suitStr + point
      },
      gameChipFormat(row, column) {
        return this.gameChipFiter(row)
      },
      gameTypeFilter(row, column) {
        return this.$t(getGameTypeEnum(row.gameType).label)
      },
      dateTimeFormat: function (row, column) {
        var date = row[column.property]
        if (!date) {
          return ''
        }
        return moment(date).format('YYYY-MM-DD HH:mm:ss')
      },
      gameChipFiter(gameTrans) {
        var gameResultDesc = ''
        var chipItems = []
        switch (Number(gameTrans.gameType)) {
          case 1:
            if (gameTrans.bankerAmout) {
              chipItems.push(this.$t('game.banker') + ': ' + gameTrans.bankerAmout)
            }
            if (gameTrans.bankerPairAmout) {
              chipItems.push(this.$t('game.bankerPair') + ': ' + gameTrans.bankerPairAmout)
            }
            if (gameTrans.bankerSuperAmout) {
              chipItems.push(this.$t('game.luckSix') + ': ' + gameTrans.bankerSuperAmout)
            }
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.player') + ': ' + gameTrans.playerAmout)
            }
            if (gameTrans.playerPairAmout) {
              chipItems.push(this.$t('game.playerPair') + ': ' + gameTrans.playerPairAmout)
            }
            if (gameTrans.tieAmout) {
              chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
          case 2:
            if (gameTrans.bankerAmout) {
              chipItems.push(this.$t('game.tiger') + ': ' + gameTrans.bankerAmout)
            }
            if (gameTrans.bankerPairAmout) {
              chipItems.push(this.$t('game.pair') + ': ' + gameTrans.bankerPairAmout)
            }
            if (gameTrans.tieAmout) {
              chipItems.push(this.$t('game.tie') + ': ' + gameTrans.tieAmout)
            }
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.dragon') + ': ' + gameTrans.playerAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
          case 3:
            if (gameTrans.playerAmout) {
              chipItems.push(this.$t('game.player1Equal') + ': ' + gameTrans.playerAmout)
            }
            if (gameTrans.playerPairAmout) {
              chipItems.push(this.$t('game.player1Double') + ': ' + gameTrans.playerPairAmout)
            }
            if (gameTrans.playerSuperAmout) {
              chipItems.push(this.$t('game.player1Super') + ': ' + gameTrans.playerSuperAmout)
            }
            if (gameTrans.player2Amout) {
              chipItems.push(this.$t('game.player2Equal') + ': ' + gameTrans.player2Amout)
            }
            if (gameTrans.player2PairAmout) {
              chipItems.push(this.$t('game.player2Double') + ': ' + gameTrans.player2PairAmout)
            }
            if (gameTrans.player2SuperAmout) {
              chipItems.push(this.$t('game.player2Super') + ': ' + gameTrans.player2SuperAmout)
            }
            if (gameTrans.player3Amout) {
              chipItems.push(this.$t('game.player3Equal') + ': ' + gameTrans.player3Amout)
            }
            if (gameTrans.player3PairAmout) {
              chipItems.push(this.$t('game.player3Double') + ': ' + gameTrans.player3PairAmout)
            }
            if (gameTrans.player3SuperAmout) {
              chipItems.push(this.$t('game.player3Super') + ': ' + gameTrans.player3SuperAmout)
            }
            gameResultDesc = chipItems.join(',')
            break
        }
        return gameResultDesc
      }
    }
  }
</script>

<style lang="scss" scoped>
  .card-box {
    margin-top: 0;
    padding-left: 10%;
    padding-right: 10%;
  }

  .ex-table-container {
    padding-left: 0;
    padding-right: 0;
  }

  .ex-table {
    font-weight: bold;
  }

  .ex-table td {
    height: 30px;
    line-height: 30px;
    width: 5%;
  }

  .card-name {
    color: #643c29;
  }

  .remain-all {
    color: #c40000;
  }

  .remain-part {
    color: #e97024;
  }

  .remain-zero {
    color: #b2b2b2;
  }

  .mobile .app-container {
    margin: 5px;
    margin-top: 40px;
    padding-top: 0px;
    padding-left: 0;
    padding-right: 0;
  }

  .mobile .card-box {
    padding-top: 5px;
    padding-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .banker-text,
  .player-text {
    font-size: 1.8rem !important;
    text-align: center;
    margin-top: -5px;
  }

  .banker-text {
    color: red;
  }

  .player-text {
    color: blue;
  }

  .win-tips {
    font-size: 14px !important;
    color: #67c23a;
    margin-left: 10px;
  }
</style>
