<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button size="mini" type="primary" @click="handleBatchSettleClick">{{ $t('operate.settlement') }}</el-button>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-date-picker
            v-model="memberParam.dateTo"
            align="right"
            type="date"
            :placeholder="$t('dateTemplate.selectDate')"
            :picker-options="pickerOptions"
          />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="user.userName" align="center" :label="$t('jettonSettle.memberAccount')" />
      <el-table-column prop="user.nickName" align="center" :label="$t('jettonSettle.agentNickname')" />
      <el-table-column prop="washRate" align="center" :label="$t('jettonSettle.currentWashRate')">
        <template slot-scope="scope">
          <div> {{ scope.row.washRate }}% </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalWashAmount" align="center" :label="$t('jettonSettle.totalWashAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.totalWashAmount | numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="washRate" align="center" :label="$t('jettonSettle.commissionAmount')">
        <template slot-scope="scope">
          <div> {{ scope.row.washRate*scope.row.totalWashAmount/100 | numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" :label="$t('jettonSettle.operation')" width="124">
        <template slot-scope="scope">
          <el-button-group>
            <el-button plain size="mini" @click="handleDoSettleClick(scope.row)">{{ $t('operate.settlement') }}</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <!-- <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row> -->
  </div>
</template>
<script>
import { listMemberJettonSettle, doJettonSettle } from '@/api/settlement/jettonSettle'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'MemberList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [{
          text: this.$t('dateTemplate.today'),
          onClick(picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: this.$t('dateTemplate.tomorrow'),
          onClick(picker) {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', date)
          }
        }, {
          text: this.$t('dateTemplate.aWeekAgo'),
          onClick(picker) {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', date)
          }
        }]
      },
      memberParam: {
        memberType: 3,
        gameType: 0,
        date: ''
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      settleParam: {
        toSettleList: [],
        parentMemberId: 0,
        handleBy: 0
      }
    }
  },
  computed: {
    ...mapGetters([
      'member',
      'userId'
    ])
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      if (this.member.memberType === 4) {
        this.memberParam.parentId = this.member.parentId
      } else {
        this.memberParam.parentId = this.member.id
      }
      listMemberJettonSettle(this.memberParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleDoSettleClick(val) {
      this.doJettonClick([val])
    },
    doJettonClick(list) {
      this.$confirm(this.$t('operate.settlement') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        if (this.member.memberType === 4) {
          this.settleParam.parentMemberId = this.member.parentId
        } else {
          this.settleParam.parentMemberId = this.member.id
        }
        this.settleParam.handleBy = this.member.id
        this.settleParam.toSettleList = list
        doJettonSettle(this.settleParam).then(r => {
          if (r.data) {
            this.$message({
              message: this.$t('operate.settlement') + ' ' + this.$t('operate.message.success'),
              type: 'success',
              center: true
            })
            this.listTableData()
          } else {
            this.$message({
              message: this.$t('operate.settlement') + ' ' + this.$t('operate.message.fail'),
              type: 'error',
              center: true
            })
          }
        })
      }).catch(() => {
        this.$message({
          message: this.$t('operate.settlement') + ' ' + this.$t('operate.message.cancel'),
          type: 'warning',
          center: true
        })
      })
    },
    handleBatchSettleClick() {
      if (this.multipleSelection.length > 0) {
        this.doJettonClick(this.multipleSelection)
      } else {
        this.$message({
          message: '请选择你需要结算的数据',
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
