import request from '@/utils/request'

export function listButton(data) {
  return request({
    url: '/button/list',
    method: 'post',
    data
  })
}
export function addButton(data) {
  return request({
    url: '/button/add',
    method: 'post',
    data
  })
}

export function deleteButton(ids) {
  return request({
    url: '/button/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}

export function updateButton(data) {
  return request({
    url: '/button/update',
    method: 'post',
    data
  })
}
