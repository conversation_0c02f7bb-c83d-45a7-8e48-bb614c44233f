<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" @click="handleAddClick">
            {{ $t('operate.add') }}
          </el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-input v-model="agentParam.userName" style="width:214px;margin-right:10px" :placeholder="$t('member.placeholder.memberAccount')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="accountStatus" :label="$t('agentForm.accountStatus')" width="110" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.accountStatus" type="success">
            {{ $t('status.open') }}
          </el-tag>
          <el-tag v-else type="danger">
            {{ $t('status.closed') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="chipInStatus" :label="$t('agentForm.bettingStatus')" width="110" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.chipInStatus" type="success">
            {{ $t('status.open') }}
          </el-tag>
          <el-tag v-else type="danger">  {{ $t('status.closed') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="user.userName" align="center" :label="$t('agentForm.agentAccount')" width="124" />
      <!--<el-table-column prop="user.nickName" align="center" :label="$t('agentForm.agentNickname')" width="94" />-->
      <el-table-column prop="fullName" align="center" :label="$t('agentForm.fullName')" width="100" />
      <el-table-column prop="phoneNo" align="center" :label="$t('agentForm.mobilePhone')" width="124" />
      <el-table-column prop="currencyCode" align="center" :label="$t('member.currency')" width="84" />
      <el-table-column prop="remainPoint" align="center" :label="$t('agentForm.accountBalance')" width="130">
        <template slot-scope="scope">
          {{ scope.row.remainPoint | numberFilter | moneyFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="percentAgent" align="center" :label="$t('agentForm.share')" width="74">
        <template slot-scope="scope">
          {{ scope.row.percentAgent ? scope.row.percentAgent + '%': 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="isOnline" :label="$t('agentForm.onLine')" align="center" width="84">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isOnline" type="success">
            {{ $t('status.onLine') }}</el-tag>
          <el-tag v-else type="danger"> {{ $t('status.offLine') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('agentForm.rateOfCodeWashing')" align="center" width="104">
        <template slot-scope="scope">
          {{ scope.row.memberWashList.length ? scope.row.memberWashList[0].percentWash : '' }}
          <!--<el-popover trigger="hover" placement="right">
            <el-table
              :data="scope.row.memberWashList"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="washFrom"
                :label="$t('member.codeWashingFrom')"
                width="180"
              />
              <el-table-column
                prop="washTo"
                :label="$t('member.codeWashingTo')"
                width="180"
              />
              <el-table-column
                prop="percentWash"
                :label="$t('member.rateOfCodeWashing')"
                width="180"
              />
            </el-table>
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium">{{ $t('operate.viewDetail') }}</el-tag>
            </div>
          </el-popover>-->
        </template>
      </el-table-column>
      <el-table-column :label="$t('agentForm.parentAgent')" align="center" width="104">
        <template v-if="scope.row.parentId!==member.id || member.parentId===0" slot-scope="scope">
          <el-link type="primary" @click="parentAgentClick(scope.row)">{{ $t('agentForm.parentAgent') }}</el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('agentForm.childAgent')" align="center" width="104">
        <template slot-scope="scope">
          <el-link type="primary" @click="childAgentClick(scope.row)">{{ $t('agentForm.childAgent') }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="createDt" :label="$t('agentForm.createdTime')" align="center" width="144" :formatter="dateTimeFormat" />
      <el-table-column fixed="right" align="center" :label="$t('agentForm.operation')" width="384">
        <template slot-scope="scope">
          <el-row>
            <el-col :span="16">
              <el-button-group>
                <el-button v-if="!scope.row.accountStatus&&(member.parentId === 0||scope.row.parentId === member.id||member.id === scope.row.id||(member.memberType === 4 &&member.parentId ==scope.row.parentId))" v-show="accountOperate" type="success" plain size="mini" @click="handleAccountClick(scope.row)">
                  {{ $t('operate.accountEnabled') }}
                </el-button>
                <el-button v-else-if="(member.parentId === 0||scope.row.parentId === member.id||member.id === scope.row.id||(member.memberType === 4 &&member.parentId ==scope.row.parentId))" v-show="accountOperate" type="danger" plain size="mini" @click="handleAccountClick(scope.row)">
                  {{ $t('operate.accountDisabled') }}
                </el-button>
                <el-button v-if="!scope.row.chipInStatus&&(member.parentId === 0||scope.row.parentId === member.id||member.id === scope.row.id||(member.memberType === 4 &&member.parentId ==scope.row.parentId))" v-show="chipOperate" type="success" plain size="mini" @click="handleChipClick(scope.row)">
                  <span>  {{ $t('operate.betEnabled') }}</span>
                </el-button>
                <el-button v-else-if="(member.parentId === 0||scope.row.parentId === member.id||member.id === scope.row.id||(member.memberType === 4 &&member.parentId ==scope.row.parentId))" v-show="chipOperate" type="danger" plain size="mini" @click="handleChipClick(scope.row)">
                  {{ $t('operate.betDisabled') }}
                </el-button>
              </el-button-group>
            </el-col>
            <el-col :span="8">
              <el-dropdown>
                <el-button plain size="mini">
                  {{ $t('operate.moreOperate') }}<i class="el-icon-arrow-down el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-show="password" @click.native="handlePassSetClick(scope.row)">  {{ $t('operate.passwordChange') }}</el-dropdown-item>
                  <el-dropdown-item v-show="rechargeWithdraw&&(member.id == scope.row.parentId||(member.memberType === 4 &&member.parentId ==scope.row.parentId))" @click.native="handlePontChangeClick(scope.row)">
                    {{ $t('operate.rechargeWithdraw') }}
                  </el-dropdown-item>
                  <el-dropdown-item v-show="edit&&(member.id == scope.row.parentId||(member.memberType === 4 &&member.parentId ==scope.row.parentId))">
                    <router-link :to="'/account-center/agent-edit/'+scope.row.id">
                      {{ $t('operate.agentEdit') }}
                    </router-link>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('chargeWithdrawForm.tittle')" :visible.sync="dialogFormVisible" class="form-item-normal" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="32%">
      <el-form ref="dataForm" :model="amountData" label-width="120px">
        <el-form-item :label="$t('chargeWithdrawForm.operation')">
          <el-radio-group v-model="amountData.pointType">
            <el-radio :label="1" border>
              {{ $t('chargeWithdrawForm.recharge') }}
            </el-radio>
            <el-radio :label="2" border>
              {{ $t('chargeWithdrawForm.withdraw') }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="amountData.pointType === 1" :label="$t('chargeWithdrawForm.rechargeType')">
          <el-radio-group v-model="amountData.pointMethod" class="dialog-footer">
            <el-radio :label="1" border>
              {{ $t('chargeWithdrawForm.cash') }}
            </el-radio>
            <el-radio :label="2" border>
              {{ $t('chargeWithdrawForm.sign') }}</el-radio>
            <el-radio :label="3" border>
              {{ $t('chargeWithdrawForm.payout') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('chargeWithdrawForm.amount')">
          <el-input-number v-model="amountData.point" :placeholder="$t('chargeWithdrawForm.placeholder.amount')" :min="0" :controls="false" :precision="2" clearable>
            {{ $t('chargeWithdrawForm.priceUnit') }}
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">  {{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="pointLoading" @click="submitPontChange">{{ $t('operate.confirm') }}</el-button>
      </div>
    </el-dialog>
    <change-password :visible="dialogPassVisible" :user-id="currentUserId" @cancelCall="passCancel" @doneCall="passDone" />
  </div>
</template>
<script>
import { listMemberInfo, doChipStatusChange, doAccountStatusChange } from '@/api/account/member'
import { addPointHistory } from '@/api/account/pointHistory'
import ChangePass from '../components/ChangePass.vue'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'AgentList',
  components: {
    'change-password': ChangePass
  },
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    view: {
      type: Boolean,
      default: false
    },
    password: {
      type: Boolean,
      default: false
    },
    chipOperate: {
      type: Boolean,
      default: false
    },
    accountOperate: {
      type: Boolean,
      default: false
    },
    rechargeWithdraw: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      agentParam: {
        memberType: 2
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      amountData: {
        pointType: 1,
        pointMethod: 1
      },
      currentUserId: 0,
      dialogPassVisible: false,
      memberStatusParam: {},
      pointLoading: false,
      levelFlag: true
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ])
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.agentParam.parentId = 0
    this.handFilter()
  },
  activated() {
    this.agentParam.parentId = 0
    this.handFilter()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      // 子账号逻辑
      if (this.member.memberType === 4) {
        this.agentParam.parentId = this.member.parentId
      } else {
        this.agentParam.parentId = this.member.id
      }
      // superAdmin 逻辑
      if (this.member.parentId === 0) {
        this.agentParam.parentId = 0
      }
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = this.currentPage
      listMemberInfo(this.agentParam).then(r => {
        if (r.data.list.length > 0) {
          this.objectList = r.data.list
          this.total = r.data.total
        } else {
          this.$message({
            message: this.$t('agentForm.info.noData'),
            type: 'info',
            center: true
          })
        }
      })
    },
    pageTableData() {
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = this.currentPage
      listMemberInfo(this.agentParam).then(r => {
        if (r.data.list.length > 0) {
          this.objectList = r.data.list
          this.total = r.data.total
        } else {
          this.$message({
            message: this.$t('agentForm.info.noData'),
            type: 'info',
            center: true
          })
        }
      })
    },
    // 查询上级代理
    parentAgentClick(val) {
      if (!val.parentId && this.member.parentId) {
        this.$message({
          message: this.$t('agentForm.info.noParentData'),
          type: 'info',
          center: true
        })
        return
      }
      if (this.member.parentId) {
        this.levelFlag = true
      }
      this.agentParam.parentId = 0
      this.agentParam.memberId = val.parentId
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = 1
      listMemberInfo(this.agentParam).then(r => {
        if (r.data.list.length > 0) {
          this.objectList = r.data.list
          this.total = r.data.total
        } else {
          this.$message({
            message: this.$t('agentForm.info.noParentData'),
            type: 'info',
            center: true
          })
        }
      })
    },
    // 查询下级代理
    childAgentClick(val) {
      if (this.member.parentId) {
        this.levelFlag = true
      }
      this.agentParam.memberId = 0
      this.agentParam.parentId = val.id
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = 1
      listMemberInfo(this.agentParam).then(r => {
        if (r.data.list.length > 0) {
          this.objectList = r.data.list
          this.total = r.data.total
        } else {
          this.$message({
            message: this.$t('agentForm.info.noChildData'),
            type: 'info',
            center: true
          })
        }
      })
    },
    handleAccountClick(val) {
      var message = ''
      this.memberStatusParam.agentId = val.id
      this.memberStatusParam.accountStatus = !val.accountStatus ? 1 : 0
      this.memberStatusParam.isDisabledAlone = 0
      this.memberStatusParam.memberType = 2
      if (!val.accountStatus) {
        message = this.$t('operate.accountEnabled')
      } else {
        message = this.$t('operate.accountDisabled')
      }
      this.$confirm(message + '!' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        doAccountStatusChange(this.memberStatusParam).then(r => {
          this.$message({
            message: message + '.' + this.$t('operate.message.success') + '!',
            type: 'success',
            center: true
          })
          this.listTableData()
        })
      }).catch(() => {
        this.$message({
          message: this.$t('operate.message.cancel') + '!',
          type: 'success',
          center: true
        })
      })
    },
    handleChipClick(val) {
      var message = ''
      this.memberStatusParam.agentId = val.id
      this.memberStatusParam.chipInStatus = !val.chipInStatus ? 1 : 0
      this.memberStatusParam.isDisabledAlone = 1
      this.memberStatusParam.memberType = 3
      if (!val.chipInStatus) {
        message = this.$t('operate.betEnabled')
      } else {
        message = this.$t('operate.betDisabled')
      }
      this.$confirm(message + '!' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        doChipStatusChange(this.memberStatusParam).then(r => {
          this.$message({
            message: message + '.' + this.$t('operate.message.success') + '!',
            type: 'success',
            center: true
          })
          this.listTableData()
        })
      }).catch(() => {
        this.$message({
          message: this.$t('operate.message.cancel') + '!',
          type: 'success',
          center: true
        })
      })
    },
    handleAddClick() {
      this.$router.push('/account-center/agent-create')
    },
    handlePontChangeClick(data) {
      this.amountData.memberId = data.id
      this.amountData.parentMemberId = data.parentId
      this.amountData.handleBy = this.member.id
      this.dialogFormVisible = true
      this.amountData.point = undefined
    },
    submitPontChange() {
      this.pointLoading = true
      if (!this.amountData.point) {
        this.$message({
          showClose: true,
          message: this.$t('chargeWithdrawForm.validateMsg.amount'),
          type: 'error'
        })
        this.pointLoading = false
        return
      }
      addPointHistory(this.amountData).then(r => {
        if (r.data) {
          this.$message({
            showClose: true,
            message: this.$t('operate.message.success') + '!',
            type: 'success'
          })
          this.listTableData()
          this.dialogFormVisible = false
        } else {
          this.$message({
            showClose: true,
            message: this.$t('operate.message.fail') + '!',
            type: 'error'
          })
        }
        this.pointLoading = false
      })
    },
    handlePassSetClick(data) {
      this.currentUserId = data.user.id
      this.dialogPassVisible = true
    },
    passCancel() {
      this.dialogPassVisible = false
    },
    passDone(data) {
      this.dialogPassVisible = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.pageTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
