import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'// element-ui lang
import elementKoLocale from 'element-ui/lib/locale/lang/ko'// element-ui lang
import enLocale from './en'
import zhLocale from './zh'
import koLocale from './ko'
Vue.use(VueI18n)

const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale
  },
  zh: {
    ...zhLocale,
    ...elementZhLocale
  },
  ko: {
    ...koLocale,
    ...elementKoLocale
  }
}
export function getLanguage() {
  const chooseLanguage = Cookies.get('language')
  console.log('chooseLanguage: ' + chooseLanguage)
  if (chooseLanguage) return chooseLanguage
  else return 'zh'
}
const i18n = new VueI18n({
  // set locale
  // options: en | zh | es
  locale: getLanguage(),
  // set locale messages
  messages
})

export default i18n
