<template>
  <div class="app-container game-setting">
    <el-form>
      <el-tabs v-model="tabName">
        <el-tab-pane label="庄闲规则" name="first">
          <el-row>
            <el-col>
              <span class="text">每靴从第</span>
              <el-select v-model="form.ruleValue2" clearable placeholder=" " class="input">
                <el-option
                  v-for="index in 60"
                  :key="index"
                  :label="index+''"
                  :value="index+''"
                />
              </el-select>
              <span class="text">口牌开始</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">连续出现</span>
              <el-select v-model="form.ruleValue3" clearable placeholder=" " class="input">
                <el-option
                  v-for="index in 10"
                  :key="index"
                  :label="index+''"
                  :value="index+''"
                />
              </el-select>
              <span class="text">口牌开始追</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">前一列或前二列最多</span>
              <el-select v-model="form.ruleValue4" clearable placeholder=" " class="input">
                <el-option
                  v-for="index in 10"
                  :key="index"
                  :label="index+''"
                  :value="index+''"
                />
              </el-select>
              <span class="text">口牌</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">验证靴数</span>
              <el-input
                v-model="form.ruleValue7"
                class="input"
              />
              <span class="text">靴</span>

              <span class="text ml-10">从</span>
              <el-input
                v-model="form.ruleValue9"
                class="input"
              />
              <span class="text">靴开始验证</span>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <span class="text">连续下注(不按靴)</span>
              <el-radio-group v-model="form.ruleValue11">
                <el-radio label="1">是</el-radio>
                <el-radio label="2">否</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="注码公式" name="second">
          <el-row>
            <el-col>
              <span class="text">基码</span>
              <el-input
                v-model="form.ruleValue8"
                class="input"
              />
              <span class="text ml-10">使用</span>
              <el-radio-group v-model="form.ruleValue10">
                <el-radio label="1">过两关</el-radio>
                <el-radio label="2">过三关</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-table ref="chipData" :data="chipData" tooltip-effect="dark" border style="width:815px">
                <el-table-column type="index" align="center" width="42" />
                <el-table-column prop="value1" label="输下(基码数)" align="center" width="110">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value1" />
                  </template>
                </el-table-column>
                <el-table-column prop="value1" label="赢下(基码数)" align="center" width="110">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value2" />
                  </template>
                </el-table-column>
                <el-table-column prop="value2" label="赢下2(基码数)" align="center" width="110">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value3" />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <!--保存 -->
      <div>
        <el-row>
          <el-col>
            <el-button class="save" type="primary" @click="handleSave">保存</el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getByRuleNo, add } from '@/api/setting/gameRule'
export default {
  name: 'GameSetting',
  data() {
    return {
      tabName: 'first',
      agentParam: {},
      ruleNo: 'TWO_EIGHT_SETTING',
      form: {
        id: null,
        ruleNo: 'TWO_EIGHT_SETTING',
        ruleName: '28杠设置',
        ruleType: '',
        ruleValue: '',
        ruleValue2: '',
        ruleValue3: '',
        ruleValue4: '',
        ruleValue5: '',
        ruleValue6: '',
        ruleValue7: '',
        ruleValue8: '',
        ruleValue9: '1',
        ruleValue10: '1',
        ruleValue11: '2',
        ruleValue12: '',
        ruleValue13: '',
        ruleValue14: '',
        ruleValue15: '',
        ruleValue16: '',
        ruleValue17: '',
        ruleValue18: '',
        ruleValue19: '',
        ruleValue20: '',
        createBy: this.$store.getters.userId,
        modifiedBy: this.$store.getters.userId
      },
      cardQtyEnums: [{ label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
      roadTypeEnums: [{ label: '大路', value: '1' }, { label: '大眼仔', value: '2' }, { label: '小路', value: '3' }, { label: '蟑螂路', value: '4' }],
      equalEnums: [{ label: '大于', value: '1' }, { label: '等于', value: '2' }, { label: '小于', value: '3' }],
      cardsEnums: ['A', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'],
      bpData: [],
      chipData: [],
      tempChipData: [],
      onlyUpdate: false
    }
  },
  /*
  系统设置：SYS_SETTING
  系统生路：SYS_GEN_ROAD
  形态设置：SHAPE
  比例设置：RATE
  点数设置：POINT
  牌数设置：CARD_POINT
  牌型设置：CARD_TYPE
  胜负设置：WIN_LOSE
  验证选项：VALID_OPTION
  */
  watch: {
    'form.ruleValue10': 'handleChipData'
  },
  created() {
    this.form.ruleNo = this.ruleNo
    this.handleChipData()
    this.handleRuleNo()
  },
  methods: {
    handleChipData() {
      if (this.chipData) {
        this.tempChipData = JSON.parse(JSON.stringify(this.chipData))
      }
      this.chipData = this.handleChipLevel()
      var length = this.chipData.length
      if (length > this.tempChipData.length) {
        length = this.tempChipData.length
      }
      for (var index = 0; index < length; index++) {
        this.chipData[index].value1 = this.tempChipData[index].value1
        this.chipData[index].value2 = this.tempChipData[index].value2
      }
    },
    handleChipLevel() {
      var item
      var chipData = []
      if (this.form.ruleValue10 === '1') {
        item = { value1: '1', value2: '2', value3: '' }
        chipData.push(item)
        item = { value1: '1.4', value2: '2.8', value3: '' }
        chipData.push(item)
        item = { value1: '2', value2: '4', value3: '' }
        chipData.push(item)
        item = { value1: '2.7', value2: '5.4', value3: '' }
        chipData.push(item)
        item = { value1: '3.7', value2: '7.4', value3: '' }
        chipData.push(item)
        item = { value1: '5.4', value2: '10.8', value3: '' }
        chipData.push(item)
        item = { value1: '8', value2: '16', value3: '' }
        chipData.push(item)

        item = { value1: '11', value2: '22', value3: '' }
        chipData.push(item)
        item = { value1: '15', value2: '30', value3: '' }
        chipData.push(item)
        item = { value1: '20', value2: '40', value3: '' }
        chipData.push(item)
        item = { value1: '28', value2: '56', value3: '' }
        chipData.push(item)
        item = { value1: '38', value2: '76', value3: '' }
        chipData.push(item)
        item = { value1: '50', value2: '100', value3: '' }
        chipData.push(item)

        item = { value1: '68', value2: '136', value3: '' }
        chipData.push(item)
        item = { value1: '92', value2: '184', value3: '' }
        chipData.push(item)
        item = { value1: '130', value2: '260', value3: '' }
        chipData.push(item)
        item = { value1: '180', value2: '360', value3: '' }
        chipData.push(item)
        item = { value1: '240', value2: '480', value3: '' }
        chipData.push(item)
        item = { value1: '330', value2: '660', value3: '' }
        chipData.push(item)
        item = { value1: '450', value2: '900', value3: '' }
        chipData.push(item)

        item = { value1: '600', value2: '1200', value3: '' }
        chipData.push(item)
        item = { value1: '800', value2: '1600', value3: '' }
        chipData.push(item)
      } else if (this.form.ruleValue10 === '2') {
        item = { value1: '1', value2: '2', value3: '4' }
        chipData.push(item)
        item = { value1: '1.4', value2: '2.8', value3: '5.6' }
        chipData.push(item)
        item = { value1: '2', value2: '4', value3: '8' }
        chipData.push(item)
        item = { value1: '2.7', value2: '5.4', value3: '10.8' }
        chipData.push(item)
        item = { value1: '3.7', value2: '7.4', value3: '14.8' }
        chipData.push(item)
        item = { value1: '5.4', value2: '10.8', value3: '21.6' }
        chipData.push(item)
        item = { value1: '8', value2: '16', value3: '32' }
        chipData.push(item)

        item = { value1: '11', value2: '22', value3: '44' }
        chipData.push(item)
        item = { value1: '15', value2: '30', value3: '60' }
        chipData.push(item)
        item = { value1: '20', value2: '40', value3: '80' }
        chipData.push(item)
        item = { value1: '28', value2: '56', value3: '112' }
        chipData.push(item)
        item = { value1: '38', value2: '76', value3: '152' }
        chipData.push(item)
        item = { value1: '50', value2: '100', value3: '200' }
        chipData.push(item)

        item = { value1: '68', value2: '136', value3: '272' }
        chipData.push(item)
        item = { value1: '92', value2: '184', value3: '368' }
        chipData.push(item)
        item = { value1: '130', value2: '260', value3: '520' }
        chipData.push(item)
        item = { value1: '180', value2: '360', value3: '720' }
        chipData.push(item)
        item = { value1: '240', value2: '480', value3: '960' }
        chipData.push(item)
        item = { value1: '330', value2: '660', value3: '1320' }
        chipData.push(item)
        item = { value1: '450', value2: '900', value3: '1800' }
        chipData.push(item)

        item = { value1: '600', value2: '1200', value3: '2400' }
        chipData.push(item)
        item = { value1: '800', value2: '1600', value3: '3200' }
        chipData.push(item)
      }
      return chipData
    },
    handleRuleNo() {
      getByRuleNo(this.ruleNo, this.$store.getters.userId).then(r => {
        if (r.data && !this.onlyUpdate) {
          this.form = r.data
          if (this.form.ruleValue20) {
            this.chipData = JSON.parse(this.form.ruleValue20)
          }
        }
      })
    },
    handleSave() {
      this.form.createBy = this.$store.getters.userId
      this.form.modifiedBy = this.$store.getters.userId
      if (this.bpData.length) {
        this.form.ruleValue19 = JSON.stringify(this.bpData)
      }
      if (this.chipData.length) {
        this.form.ruleValue20 = JSON.stringify(this.chipData)
      }
      if (this.onlyUpdate) {
        this.onlyUpdate = false
        this.$emit('submit', this.form)
      } else {
        add(this.form).then(r => {
          if (r.code === 20000) {
            this.handleRuleNo()
            this.$message.success('保存成功')
          } else {
            this.$message.success('保存失败')
          }
        })
      }
    },
    setData(data) {
      this.form = data
      this.onlyUpdate = true
    }
  }
}
</script>

<style>
.game-setting .el-table--small th, .game-setting .el-table--small td {
  padding: 0;
}
.game-setting .el-table--small td .el-input__inner {
  border-radius: 0;
  border: 0 solid #DCDFE6;
  text-align: center;
}
.game-setting .el-table--small th, .game-setting .el-table--small td {
  padding: 0;
}
.game-setting .el-table .cell {
  padding: 0;
}
.game-setting th, .game-setting td {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.game-setting .el-row {
  margin-bottom: 10px;
}
.game-setting .input {
  width: 100px;
}
.game-setting .save {
  margin-left: 25px;
  width: 100px;
}
.game-setting .text {
  margin-right: 10px;
}
.game-setting .el-checkbox {
  margin-right: 10px;
}
</style>
