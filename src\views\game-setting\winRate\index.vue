<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-select v-model="param.gameType" clearable :placeholder="$t('winRate.placeholder')">
            <el-option
              v-for="item in gameTypeEnums"
              :key="item.type"
              :label="$t(item.label)"
              :value="item.type"
            />
          </el-select>
          <el-button icon="fa fa-search" type="primary" @click="dataFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="44" />
      <el-table-column type="index" align="center" width="44" />
      <el-table-column prop="gameType" align="center" :label="$t('winRate.gameType')" width="124" :formatter="gameTypeFomatter" />
      <el-table-column prop="rateNo" align="center" :label="$t('winRate.rateNo')" width="144" />
      <el-table-column prop="rate" align="center" :label="$t('winRate.rate')" width="120">
        <template slot-scope="scope">
          <div> {{ scope.row.rate| numberFilter | moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="remark" :label="$t('winRate.remark')" show-overflow-tooltip />
      <el-table-column fixed="right" align="center" :label="$t('winRate.operation')" width="104">
        <template slot-scope="scope">
          <el-button plain size="mini" @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('winRate.rateInfo')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="30%">
      <el-form ref="dataForm" :model="object" label-suffix=":" :label-width="formLabelWidth" :disabled="ifView" :rules="buttonRules">
        <el-form-item :label="$t('winRate.gameType')">
          <span>
            {{ gameTypeFilter(object.gameType) }}
          </span>
        </el-form-item>
        <el-form-item :label="$t('winRate.rateNo')">
          <span>
            {{ object.rateNo }}
          </span>
        </el-form-item>
        <el-form-item :label="$t('winRate.rate')" prop="rate">
          <el-input-number v-model="object.rate" :controls="false" />
        </el-form-item>
        <el-form-item :label="$t('winRate.remark')" prop="remark">
          <el-input v-model="object.remark" type="textarea" auto-complete="off" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.edit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listWinRate, updateWinRate } from '@/api/setting/winRate'
import { deepClone } from '@/utils/transferUtil'
import { formatMoney, formatNumber } from '@/utils/formatter'
import { getGameTypeEnum, GameTypeEnum } from '@/enums/setting.js'
export default {
  name: 'JettonList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      param: {},
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {
        buttonNo: '',
        buttonName: ''
      },
      formLabelWidth: '80px',
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      },
      buttonRules: {
        rate: [{ required: true, trigger: 'blur', message: this.$t('winRate.validateMsg.rate') }],
        remark: [{ required: true, trigger: 'blur', message: this.$t('winRate.validateMsg.remark') }]
      }
    }
  },
  computed: {
    gameTypeEnums() {
      return GameTypeEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    dataFilter() {
      this.currentPage = 1
      this.getData()
    },
    getData() {
      this.param.pageSize = this.pageSize
      this.param.pageNo = this.currentPage
      listWinRate(this.param).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.object = deepClone(val)
      this.ifView = false
      this.dialogStatus = 'edit'
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.edit = true
          updateWinRate(this.object).then(() => {
            this.dialogFormVisible = false
            this.getData()
            this.$message({
              message: this.$t('operate.change') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    gameTypeFilter: function(gameType) {
      if (!gameType) {
        return ''
      } else {
        return this.$t(getGameTypeEnum(gameType).label)
      }
    },
    gameTypeFomatter: function(row, column) {
      if (!row.gameType) {
        return ''
      } else {
        return this.$t(getGameTypeEnum(row.gameType).label)
      }
    }
  }
}
</script>
<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
