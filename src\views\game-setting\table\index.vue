<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button size="mini" type="primary" icon="el-icon-plus" round @click="handleAddClick">{{ $t('operate.add') }}</el-button>
          <el-button size="mini" type="danger" icon="el-icon-delete" round @click="handleDeleteClick">{{ $t('operate.delete') }}</el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <!-- <el-input v-model="agentParam.userName" style="width:144px" placeholder="根据账号查询" clearable /> -->
          <el-button icon="el-icon-refresh" type="primary" @click="getData" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" v-loading="listLoading" :data="objectList" tooltip-effect="dark" style="width: 100%" border fit highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="tableNo" align="center" :label="$t('tableInfo.tableNo')" width="80" />
      <el-table-column prop="tableName" align="center" :label="$t('tableInfo.tableName')" width="110" />
      <el-table-column prop="tableNameEn" align="center" :label="$t('tableInfo.tableNameEn')" width="110" />
      <!-- <el-table-column prop="isNet" align="center" :label="$t('tableInfo.tableType')" width="104">
        <template slot-scope="scope">
          {{ scope.row.isNet === 1 ? $t('tableInfo.networkTable'): $t('tableInfo.liveTable') }}
        </template>
      </el-table-column> -->
      <el-table-column prop="gameType" align="center" :label="$t('tableInfo.gameType')" :formatter="gameTypeFilter" width="104" />
      <el-table-column prop="gameCategory" align="center" :label="$t('tableInfo.betMethod')" :formatter="gameCategoryFilter" width="94" />
      <!-- <el-table-column prop="dealerRtmp" align="center" label="荷官地址" width="184" show-overflow-tooltip /> -->
      <el-table-column prop="rtmp" align="center" :label="$t('tableInfo.PCVideoUrl')" width="114">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="right">
            <p>{{ $t('tableInfo.PCVideoUrl1') }}: {{ scope.row.rtmp }}</p>
            <p>{{ $t('tableInfo.PCVideoUrl2') }}: {{ scope.row.rtmp2 }}</p>
            <p>{{ $t('tableInfo.PCVideoUrl3') }}: {{ scope.row.rtmp3 }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-show="scope.row.rtmp||scope.row.rtmp2||scope.row.rtmp3" size="medium">{{ $t('tableInfo.PCVideoUrl') }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="rtsp" align="center" :label="$t('tableInfo.AppVedioUrl')" width="114">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="right">
            <p>{{ $t('tableInfo.AppVedioUrl1') }}: {{ scope.row.rtsp }}</p>
            <p>{{ $t('tableInfo.AppVedioUrl2') }}: {{ scope.row.rtsp2 }}</p>
            <p>{{ $t('tableInfo.AppVedioUrl3') }}: {{ scope.row.rtsp3 }}</p>
            <div slot="reference" class="name-wrapper">
              <el-tag v-show="scope.row.rtsp|| scope.row.rtsp2||scope.row.rtsp3" size="medium">{{ $t('tableInfo.AppVedioUrl') }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column> -->
      <el-table-column prop="shoeIp" align="center" :label="$t('tableInfo.shoeIp') + ':' + $t('tableInfo.port')" width="144">
        <template slot-scope="scope">
          <div> {{ scope.row.shoeIp }}:{{ scope.row.shoePort }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="serverIp" align="center" :label="$t('tableInfo.serverIp') + ':' + $t('tableInfo.port')" width="144">
        <template slot-scope="scope">
          <div> {{ scope.row.serverIp }}:{{ scope.row.serverPort }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="notChipTimes" align="center" :label="$t('tableInfo.notChipTimes')" width="84" />
      <el-table-column prop="chipInterval" align="center" :label="$t('tableInfo.chipInterval')" width="84" />
      <el-table-column prop="totalMax" align="center" :label="$t('tableInfo.totalMax')" width="84" />
      <el-table-column prop="betMin" align="center" :label="$t('tableInfo.betMin')" width="84" />
      <el-table-column prop="betMax" align="center" :label="$t('tableInfo.betMax')" width="84" />
      <el-table-column prop="tieMin" align="center" :label="$t('tableInfo.tieMin')" width="84" />
      <el-table-column prop="tieMax" align="center" :label="$t('tableInfo.tieMax')" width="84" />
      <el-table-column prop="pairMin" align="center" :label="$t('tableInfo.pairMin')" width="84" />
      <el-table-column prop="pairMax" align="center" :label="$t('tableInfo.pairMax')" width="84" />
      <el-table-column prop="totalMax2" align="center" :label="$t('tableInfo.totalMax')+'2'" width="84" />
      <el-table-column prop="betMin2" align="center" :label="$t('tableInfo.betMin')+'2'" width="84" />
      <el-table-column prop="betMax2" align="center" :label="$t('tableInfo.betMax')+'2'" width="84" />
      <el-table-column prop="tieMin2" align="center" :label="$t('tableInfo.tieMin')+'2'" width="84" />
      <el-table-column prop="tieMax2" align="center" :label="$t('tableInfo.tieMax')+'2'" width="84" />
      <el-table-column prop="pairMin2" align="center" :label="$t('tableInfo.pairMin')+'2'" width="84" />
      <el-table-column prop="pairMax2" align="center" :label="$t('tableInfo.pairMax')+'2'" width="84" />
      <!-- <el-table-column prop="superMin" align="center" :label="$t('tableInfo.superMin')" width="100" />
      <el-table-column prop="superMax" align="center" :label="$t('tableInfo.superMax')" width="100" />
      <el-table-column prop="currencyCode" align="center" :label="$t('tableInfo.currencyCode')" width="74" /> -->
      <!-- <el-table-column prop="isConfirm" align="center" :label="$t('tableInfo.isConfirm')" width="74">
        <template slot-scope="scope">
          <div> {{ scope.row.isConfirm ? $t('tableInfo.need'):$t('tableInfo.noNeed') }} </div>
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="isFree" align="center" :label="$t('tableInfo.freeCommission')" width="74">
        <template slot-scope="scope">
          <div> {{ scope.row.isFree ? $t('tableInfo.freeYes'):$t('tableInfo.freeNo') }} </div>
        </template>
      </el-table-column> -->
      <el-table-column prop="status" align="center" :label="$t('tableInfo.status')" :formatter="statusFilter" width="94" />
      <el-table-column fixed="right" align="center" :label="$t('tableInfo.operation')" width="94">
        <template slot-scope="scope">
          <el-button plain size="mini" @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <el-dialog :title="$t('tableInfo.tableInfo')" :visible.sync="dialogFormVisible" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="60%">
      <el-form ref="dataForm" :model="object" :label-width="formLabelWidth" label-suffix=":" :disabled="ifView" :rules="tableRules">
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.tableNo')" prop="tableNo">
              <el-input v-model="object.tableNo" auto-complete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.tableName')" prop="tableName">
              <el-input v-model="object.tableName" auto-complete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.tableNameEn')" prop="tableNameEn">
              <el-input v-model="object.tableNameEn" auto-complete="off" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.gameType')" prop="gameType">
              <el-select v-model="object.gameType">
                <el-option
                  v-for="item in gameTypeEnums"
                  :key="item.type"
                  :label="$t(item.label) "
                  :value="item.type"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.betMethod')" prop="gameCategory">
              <el-select v-model="object.gameCategory">
                <el-option
                  v-for="item in gameCategoryEnums"
                  :key="item.category"
                  :label="$t(item.label) "
                  :value="item.category"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.chipInterval')" prop="chipInterval">
              <el-input v-model.number="object.chipInterval" auto-complete="off">
                <template slot="append">{{ $t('timeUnit.second') }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item :label="$t('tableInfo.casino')">
              <el-select v-model="object.isNet">
                <el-option
                  key="1"
                  :label="$t('tableInfo.manila')"
                  :value="1"
                />
                <el-option
                  key="2"
                  :label="$t('tableInfo.cebu')"
                  :value="2"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="8">
            <el-form-item label="荷官地址" prop="dealerRtmp">
              <el-input v-model="object.dealerRtmp" auto-complete="off" />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.PCVideoUrl1')" prop="rtmp">
              <el-input v-model.trim="object.rtmp" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.PCVideoUrl2')" prop="rtmp2">
              <el-input v-model.trim="object.rtmp2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.PCVideoUrl3')" prop="rtmp3">
              <el-input v-model.trim="object.rtmp3" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.AppVedioUrl1')" prop="rtsp">
              <el-input v-model.trim="object.rtsp" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.AppVedioUrl2')" prop="rtsp2">
              <el-input v-model.trim="object.rtsp2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.AppVedioUrl3')" prop="rtsp3">
              <el-input v-model.trim="object.rtsp3" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.serverIp')" label-width="106px" prop="serverIp">
              <el-input v-model="object.serverIp" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.serverPort')" label-width="106px" prop="serverPort">
              <el-input v-model.number="object.serverPort" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.shoeIp')" label-width="106px" prop="shoeIp">
              <el-input v-model="object.shoeIp" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.shoePort')" label-width="106px" prop="shoePort">
              <el-input v-model.number="object.shoePort" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.isHide')" label-width="106px">
              <el-switch v-model="object.isConfirm" :active-text="$t('tableInfo.yes')" :inactive-text="$t('tableInfo.no')" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.notChipTimes')" label-width="110px" prop="notChipTimes">
              <el-input v-model.number="object.notChipTimes" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('tableInfo.dealerUrl')" prop="dealerRtmp">
              <el-input v-model="object.dealerRtmp" auto-complete="off" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.isConfirm')" label-width="106px">
              <el-switch v-model="object.isConfirm" :active-text="$t('tableInfo.need')" :inactive-text="$t('tableInfo.noNeed')" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('tableInfo.totalMax')" label-width="110px" prop="totalMax">
              <el-input v-model.number="object.totalMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('tableInfo.freeCommission')" label-width="106px">
              <el-switch v-model="object.isFree" :active-text="$t('tableInfo.freeYes')" :inactive-text="$t('tableInfo.freeNo')" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
          <<el-col :span="6">
            <el-form-item :label="$t('tableInfo.tableType')" label-width="106px">
              <el-switch
                v-model="object.isNet"
                active-color="#13ce66"
                inactive-color="#ff4949"
                :active-text="$t('tableInfo.networkTable')"
                :inactive-text="$t('tableInfo.liveTable')"
                :active-value="1"
                :inactive-value="2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('tableInfo.totalMax')+'2'" label-width="110px" prop="totalMax2">
              <el-input v-model.number="object.totalMax2" auto-complete="off" clearable />
            </el-form-item>
            <el-form-item :label="$t('tableInfo.currencyCode')" prop="gameCategory">
              <el-select v-model="object.currencyCode">
                <el-option
                  v-for="item in currencyCodeEnums"
                  :key="item.code"
                  :label="$t(item.label)"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.betMin')" label-width="110px" prop="betMin">
              <el-input v-model.number="object.betMin" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.betMax')" label-width="110px" prop="betMax">
              <el-input v-model.number="object.betMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.tieMin')" label-width="110px" prop="tieMin">
              <el-input v-model.number="object.tieMin" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.tieMax')" label-width="110px" prop="tieMax">
              <el-input v-model.number="object.tieMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.pairMin')" label-width="110px" prop="pairMin">
              <el-input v-model.number="object.pairMin" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.pairMax')" label-width="110px" prop="pairMax">
              <el-input v-model.number="object.pairMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item :label="$t('tableInfo.superMin')" label-width="110px" prop="superMin">
              <el-input v-model.number="object.superMin" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.superMax')" label-width="110px" prop="superMax">
              <el-input v-model.number="object.superMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col> -->
          <!-- </el-row> -->
          <!-- <el-row> -->
          <!-- <el-col :span="6">
            <el-form-item :label="$t('tableInfo.totalMax')" label-width="110px" prop="totalMax">
              <el-input v-model.number="object.totalMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col> -->
          <!--<el-col :span="6">
            <el-form-item :label="$t('tableInfo.totalMax2')" label-width="110px" prop="totalMax2">
              <el-input v-model.number="object.totalMax2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.totalMax3')" label-width="110px" prop="totalMax3">
              <el-input v-model.number="object.totalMax3" auto-complete="off" clearable />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item :label="$t('tableInfo.totalMax')" label-width="110px" prop="totalMax">
              <el-input v-model.number="object.totalMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.betMin')+'2'" label-width="110px" prop="betMin2">
              <el-input v-model.number="object.betMin2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.betMax')+'2'" label-width="110px" prop="betMax2">
              <el-input v-model.number="object.betMax2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.tieMin')+'2'" label-width="110px" prop="tieMin2">
              <el-input v-model.number="object.tieMin2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.tieMax')+'2'" label-width="110px" prop="tieMax2">
              <el-input v-model.number="object.tieMax2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.pairMin')+'2'" label-width="110px" prop="pairMin2">
              <el-input v-model.number="object.pairMin2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.pairMax')+'2'" label-width="110px" prop="pairMax2">
              <el-input v-model.number="object.pairMax2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('tableInfo.totalMax')+'2'" label-width="110px" prop="totalMax2">
              <el-input v-model.number="object.totalMax2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item :label="$t('tableInfo.superMin')" label-width="110px" prop="superMin">
              <el-input v-model.number="object.superMin" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.superMax')" label-width="110px" prop="superMax">
              <el-input v-model.number="object.superMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col> -->
          <!-- </el-row> -->
          <!-- <el-row> -->
          <!-- <el-col :span="6">
            <el-form-item :label="$t('tableInfo.totalMax')" label-width="110px" prop="totalMax">
              <el-input v-model.number="object.totalMax" auto-complete="off" clearable />
            </el-form-item>
          </el-col> -->
          <!--<el-col :span="6">
            <el-form-item :label="$t('tableInfo.totalMax2')" label-width="110px" prop="totalMax2">
              <el-input v-model.number="object.totalMax2" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.totalMax3')" label-width="110px" prop="totalMax3">
              <el-input v-model.number="object.totalMax3" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.notChipTimes')" label-width="110px" prop="notChipTimes">
              <el-input v-model.number="object.notChipTimes" auto-complete="off" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.dealerUrl')" prop="dealerRtmp">
              <el-input v-model="object.dealerRtmp" auto-complete="off" />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item :label="$t('tableInfo.isJoin')" label-width="106px">
              <el-switch v-model="object.isJoin" :active-text="$t('tableInfo.yes')" :inactive-text="$t('tableInfo.no')" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button v-if="dialogStatus=='create'" type="primary" :loading="loadingTags.add" @click="createData">{{ $t('operate.save') }}</el-button>
        <el-button v-else type="primary" :loading="loadingTags.edit" @click="modifyData">{{ $t('operate.edit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listTableInfo, addTableInfo, updateTableInfo, deleteTableInfo } from '@/api/setting/tableInfo'
import { getDataStatusEnum } from '@/data/common/commonEnum'
import { deepClone } from '@/utils/transferUtil'
import { CurrencyCodeEnum, GameCategoryEnum, getGameCategoryEnum, GameTypePureEnum, getGameTypePureEnum, getTableInfoStatusEnum } from '@/enums/setting.js'
import moment from 'moment'
export default {
  name: 'TableInfo',
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listLoading: true,
      agentParam: {},
      objectList: [],
      multipleSelection: [],
      systemOptions: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      dialogFormVisible: false,
      object: {},
      formLabelWidth: '114px',
      ifView: false,
      ids: [],
      dialogStatus: '',
      loadingTags: {
        add: false,
        edit: false
      },
      tableRules: {
        tableNo: [{ required: true, trigger: 'blur', message: this.$t('tableInfo.validateMsg.tableNo') }],
        tableName: [{ required: true, trigger: 'blur', message: this.$t('tableInfo.validateMsg.tableName') }],
        tableNameEn: [{ required: true, trigger: 'blur', message: this.$t('tableInfo.validateMsg.tableNameEn') }],
        gameType: [{ required: true, trigger: 'blur', message: this.$t('tableInfo.validateMsg.gameType') }],
        gameCategory: [{ required: true, trigger: 'blur', message: this.$t('tableInfo.validateMsg.gameCategory') }],
        rtmp: [
          { required: true, message: this.$t('tableInfo.validateMsg.rtmp'), trigger: 'blur' }
        ],
        rtmp2: [
          { required: true, message: this.$t('tableInfo.validateMsg.rtmp2'), trigger: 'blur' }
        ],
        rtmp3: [
          { required: true, message: this.$t('tableInfo.validateMsg.rtmp3'), trigger: 'blur' }
        ],
        dealerRtmp: [
          { required: true, message: this.$t('tableInfo.validateMsg.dealerRtmp'), trigger: 'blur' }
        ],
        rtsp: [
          { required: true, message: this.$t('tableInfo.validateMsg.rtsp'), trigger: 'blur' }
        ],
        rtsp2: [
          { required: true, message: this.$t('tableInfo.validateMsg.rtsp2'), trigger: 'blur' }
        ],
        rtsp3: [
          { required: true, message: this.$t('tableInfo.validateMsg.rtsp3'), trigger: 'blur' }
        ],
        betMin: [
          { required: true, message: this.$t('tableInfo.validateMsg.betMin'), trigger: 'blur' }
        ],
        betMax: [
          { required: true, message: this.$t('tableInfo.validateMsg.betMax'), trigger: 'blur' }
        ],
        tieMin: [
          { required: true, message: this.$t('tableInfo.validateMsg.tieMin'), trigger: 'blur' }
        ],
        tieMax: [
          { required: true, message: this.$t('tableInfo.validateMsg.tieMax'), trigger: 'blur' }
        ],
        pairMin: [
          { required: true, message: this.$t('tableInfo.validateMsg.pairMin'), trigger: 'blur' }
        ],
        pairMax: [
          { required: true, message: this.$t('tableInfo.validateMsg.pairMax'), trigger: 'blur' }
        ],
        superMin: [
          { required: true, message: this.$t('tableInfo.validateMsg.superMin'), trigger: 'blur' }
        ],
        superMax: [
          { required: true, message: this.$t('tableInfo.validateMsg.superMax'), trigger: 'blur' }
        ],
        totalMax: [
          { required: true, message: this.$t('tableInfo.validateMsg.totalMax'), trigger: 'blur' }
        ],
        /* totalMax2: [
          { required: true, message: this.$t('tableInfo.validateMsg.totalMax2'), trigger: 'blur' }
        ],
        totalMax3: [
          { required: true, message: this.$t('tableInfo.validateMsg.totalMax3'), trigger: 'blur' }
        ],*/
        serverIp: [
          { required: true, message: this.$t('tableInfo.validateMsg.serverIp'), trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: this.$t('tableInfo.validateMsg.serverPort'), trigger: 'blur' }
        ],
        shoeIp: [
          { required: true, message: this.$t('tableInfo.validateMsg.shoeIp'), trigger: 'blur' }
        ],
        shoePort: [
          { required: true, message: this.$t('tableInfo.validateMsg.shoePort'), trigger: 'blur' }
        ],
        notChipTimes: [
          { required: true, message: this.$t('tableInfo.validateMsg.notChipTimes'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    gameCategoryEnums() {
      return GameCategoryEnum
    },
    gameTypeEnums() {
      return GameTypePureEnum
    },
    currencyCodeEnums() {
      return CurrencyCodeEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.getData()
  },
  methods: {
    getData() {
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = this.currentPage
      this.listLoading = true
      listTableInfo(this.agentParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
        this.listLoading = false
        console.log(this.objectList)
      })
    },
    handleAddClick() {
      this.loadingTags.add = false
      this.dialogFormVisible = true
      this.object = { gameType: 1, gameCategory: 1, isNet: 2 }
      this.ifView = false
      this.dialogStatus = 'create'
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.add = true
          addTableInfo(this.object).then(() => {
            this.dialogFormVisible = false
            this.loadingTags.add = false
            this.getData()
            this.$message({
              message: this.$t('operate.save') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
          })
        }
      })
    },
    handleEditClick(val) {
      this.loadingTags.edit = false
      this.dialogFormVisible = true
      this.object = deepClone(val)
      this.ifView = false
      this.dialogStatus = 'edit'
    },
    modifyData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.loadingTags.edit = true
          this.object.matchId = ''
          this.object.gameId = ''
          this.object.clientIp = undefined
          this.object.status = ''
          updateTableInfo(this.object).then(r => {
            if (r.code === 20000) {
              this.dialogFormVisible = false
              this.loadingTags.edit = false
              this.getData()
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.success'),
                type: 'success'
              })
            } else {
              this.$message({
                message: this.$t('operate.edit') + ' ' + this.$t('operate.message.fail'),
                type: 'error'
              })
            }
          })
        }
      })
    },
    removeData() {
      deleteTableInfo(this.ids).then(() => {
        this.$message({
          message: this.$t('operate.delete') + ' ' + this.$t('operate.message.success'),
          type: 'success'
        })
        this.getData()
      })
    },
    deleteRows() {
      if (this.ids.length > 0) {
        this.removeData()
      } else {
        this.$message({
          message: this.$t('jetton.msg.deleteData'),
          type: 'warning'
        })
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData()
    },
    handleDeleteClick() {
      this.$confirm(this.$t('operate.delete') + ',' + this.$t('operate.info.continue') + '?', this.$t('operate.message.tips'), {
        confirmButtonText: this.$t('operate.confirm'),
        cancelButtonText: this.$t('operate.cancel'),
        type: 'warning'
      }).then(() => {
        this.deleteRows()
      })
    },
    formatDataStatus(row, column, cellValue) {
      return getDataStatusEnum(cellValue)
    },
    gameCategoryFilter(row, column) {
      return this.$t(getGameCategoryEnum(row.gameCategory).label)
    },
    gameTypeFilter(row, column) {
      return this.$t(getGameTypePureEnum(row.gameType).label)
    },
    statusFilter(row, column) {
      return this.$t(getTableInfoStatusEnum(row.status).label)
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
