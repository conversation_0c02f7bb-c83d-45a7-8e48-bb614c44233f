<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-select v-model="memberParam.isOnline" clearable placeholder="在线/离线">
            <el-option v-for="item in onlineTypeEnums" :key="item.type" :label="$t(item.value)" :value="item.type" />
          </el-select>
          <el-select v-model="memberParam.operateType" clearable
            :placeholder="$t('operateHistory.placeholder.operateType')">
            <el-option v-for="item in operateTypeEnums" :key="item.type" :label="$t(item.value)" :value="item.type" />
          </el-select>
          <el-select v-model="memberParam.userType" clearable :placeholder="$t('operateHistory.placeholder.userType')">
            <el-option v-for="item in userTypeEnums" :key="item.type" :label="$t(item.value)" :value="item.type" />
          </el-select>
          <el-select v-model="memberParam.platform" clearable
            :placeholder="$t('operateHistory.placeholder.operatePlatform')">
            <el-option v-for="item in platformEnums" :key="item.platform" :label="$t(item.label)"
              :value="item.platform" />
          </el-select>
          <el-date-picker v-model="memberParam.date" type="daterange" align="right" unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')" :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions" />
          <el-input v-model="memberParam.userName" style="width:160px;margin-right:10px"
            :placeholder="$t('operateHistory.placeholder.operateAccount')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border
      highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column align="center" :label="$t('operateHistory.operateAccount')" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <div> {{ scope.row.user ? scope.row.user.userName : '' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="operateType" align="center" :label="$t('operateHistory.operateType')"
        :formatter="operateTypeEnumFormat" width="100" />
      <!--<el-table-column prop="remark" align="center" :label="$t('operateHistory.operatedAccount')" /> 
      <el-table-column prop="userType" align="center" :label="$t('operateHistory.userType')" :formatter="userTypeEnumFormat" /> -->
      <el-table-column prop="isOnline" :label="$t('agentForm.onLine')" align="center" width="84">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isOnline" type="success">
            {{ $t('status.onLine') }}</el-tag>
          <el-tag v-else type="danger"> {{ $t('status.offLine') }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="loginDt" align="center" label="登录时间" :formatter="dateTimeFormat" width="150" />
      <el-table-column prop="logoutDt" align="center" label="登出时间" :formatter="dateTimeFormat" width="150" />
      <el-table-column prop="totalMinute" align="center" label="时长(分钟)" width="100" />
      <el-table-column prop="member.expiredDt" align="center" label="到期时间" width="150" />
      <el-table-column prop="platform" align="center" :label="$t('operateHistory.operatePlatform')"
        :formatter="platformFormat" width="100" />
      <el-table-column prop="loginIp" align="center" :label="$t('operateHistory.operateIp')" width="120" />
      <el-table-column prop="loginAddr" align="center" :label="$t('operateHistory.operateArea')" show-overflow-tooltip
        width="150" />
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-row>
  </div>
</template>
<script>
import { listLoginHistoryInfo } from '@/api/account/loginHistory'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { gameResultFilter } from '@/filters/gameResult'
import { getPlatformEnum, PlatformEnum } from '@/enums/setting'
import { getUserTypeEnum, UserTypeEnum, getOperateTypeEnum, OperateTypeEnum } from '@/enums/system'
import { formatNumeric } from '@/utils/formatter'
export default {
  name: 'LoginHistory',
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        userType: '',
        matchNo: '',
        tableId: '',
        date: '',
        platform: '',
        isOnline: 1
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      tableList: [],
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      onlineTypeEnums: [{ value: '全部', type: null }, { value: '在线', type: 1 }, { value: '离线', type: 0 }]
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ]),
    userTypeEnums() {
      return UserTypeEnum
    },
    platformEnums() {
      return PlatformEnum
    },
    operateTypeEnums() {
      return OperateTypeEnum
    }
  },
  watch: {
    multipleSelection: function () {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      this.memberParam.pageSize = this.pageSize
      this.memberParam.pageNo = this.currentPage
      listLoginHistoryInfo(this.memberParam).then(r => {
        // this.objectList = r.data.list
        this.formatData(r.data.list)
        this.total = r.data.total
        console.log('listLoginHistoryInfo..................1...', r.data.list)
      })
    },
    formatData(list) {
      for (var item of list) {
        var logoutDt = item.logoutDt
        if (!item.logoutDt) {
          logoutDt = new Date()
        }
        item.totalMinute = formatNumeric(moment(logoutDt).diff(item.loginDt) / (1000 * 60))
      }
      this.objectList = list
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      console.log('size is changed .............' + val)
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function (row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    userTypeEnumFormat: function (row, column) {
      if (!row.userType) {
        return ''
      } else {
        return this.$t(getUserTypeEnum(row.userType))
      }
    },
    platformFormat: function (row, column) {
      return this.$t(getPlatformEnum(row.platform).label)
    },
    operateTypeEnumFormat: function (row, column) {
      if (!row.operateType) {
        return ''
      } else {
        return this.$t(getOperateTypeEnum(row.operateType))
      }
    },
    gameResultFormat: function (row, column) {
      return gameResultFilter(row.gameType, row.gameResult)
    }
  }
}
</script>
