<template>
  <div class="app-container">
    <mobile-header v-if="device === 'mobile'" title="修改密码" @back="handleBack" />
    <el-form ref="passwordForm" :model="object" :rules="changePassWordRules" label-suffix=":" label-width="110px"
      class="form-item-normal mt-40">
      <el-row>
        <el-col :xs="24" :sm="12" :lg="8">
          <el-form-item :label="$t('member.passForm.loginAccount')" class="mt-10">
            <el-input v-model="userName" readonly />
          </el-form-item>
          <el-form-item :label="$t('member.passForm.oldPassword')" prop="oldPassword">
            <el-input v-model="object.oldPassword" type="password" :placeholder="$t('member.passForm.placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('member.passForm.newpassword')" prop="newPassword">
            <el-input v-model="object.newPassword" type="password" :placeholder="$t('member.passForm.placeholder')" />
          </el-form-item>
          <el-form-item :label="$t('member.passForm.passwordConfirm')" prop="confirmPassword">
            <el-input v-model="object.confirmPassword" type="password"
              :placeholder="$t('member.passForm.placeholder')" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" icon="el-icon-edit" @click="changePassword">{{
              $t('operate.change')
            }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { checkPassword, changePassword } from '@/api/common/user'
import { isValidPassword } from '@/utils/validate'
import { genPassword } from '@/utils/transferUtil'
export default {
  data() {
    const validateOldPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.oldPassword')))
      } else {
        const password = genPassword(this.object.oldPassword)
        checkPassword(this.userId, password).then(r => {
          if (r.data) {
            callback()
          } else {
            callback(new Error(this.$t('member.passForm.validateMsg.oldPasswordError')))
          }
        })
      }
    }
    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.newPassword')))
      } else if (!isValidPassword(value)) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordFormate')))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordConfirm')))
      } else if (this.object.newPassword !== this.object.confirmPassword) {
        callback(new Error(this.$t('member.passForm.validateMsg.passwordDifferent')))
      } else {
        callback()
      }
    }
    return {
      object: {},
      loading: false,
      changePassWordRules: {
        oldPassword: [{ required: true, trigger: 'blur', validator: validateOldPassword }],
        newPassword: [{ required: true, trigger: 'blur', validator: validateNewPassword }],
        confirmPassword: [{ required: true, trigger: 'blur', validator: validateConfirmPassword }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userName',
      'userId',
      'device'
    ])
  },
  methods: {
    handleBack() {
      this.$router.push({ path: '/dashboard/index' })
    },
    changePassword() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          const saltPassword = genPassword(this.object.newPassword)
          this.loading = true
          changePassword(this.userId, saltPassword).then(() => {
            this.object = {}
            this.$message({
              message: this.$t('operate.change') + ' ' + this.$t('operate.message.success'),
              type: 'success'
            })
            this.loading = false
          }).catch(() => {
            this.$message({
              message: this.$t('operate.change') + ' ' + this.$t('operate.message.fail'),
              type: 'error'
            })
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
