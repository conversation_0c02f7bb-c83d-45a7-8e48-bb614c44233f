import Vue from 'vue'
import Router from 'vue-router'
import i18n from '@/lang'
const _import = require('./_import_' + process.env.NODE_ENV)

// in development-env not use lazy-loading, because lazy-loading too many pages will cause webpack hot update too slow. so only in production use lazy-loading;
// detail: https://panjiachen.github.io/vue-element-admin-site/#/lazy-loading

Vue.use(Router)

/* Layout */
import Layout from '../layout/index'
import LayoutMain from '../layout/main'
// import LayoutMain2 from '../layout/main2'
import { listRouter, listUserRouter } from '@/api/common/common'

/**
* hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
* redirect: noredirect           if `redirect:noredirect` will no redirct in the breadcrumb
* name:'router-name'             the name is used by <keep-alive> (must set!!!)
* meta : {
    title: 'title'               the name show in submenu and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar,
  }
**/
export const constantRouterMap = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: _import('redirect/index')
      }
    ]
  },
  {
    path: '/',
    component: LayoutMain,
    hidden: true,
    redirect: '/login',
    children: [{
      path: 'login',
      name: 'login',
      component: _import('login/index')
    }
    ]
  },
  /* {
    path: '/',
    component: LayoutMain2,
    hidden: true,
    redirect: '/register',
    children: [{
      path: 'register',
      name: 'Register',
      component: _import('register/index')
    }
    ]
  },*/
  { path: '/404', component: _import('404'), hidden: true },
  { path: '/game-bet/history', component: _import('game-bet/player2/history'), hidden: true },
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/index',
    children: [{
      path: 'index',
      name: 'Dashboard',
      component: _import('dashboard/index'),
      meta: {
        title: i18n.t('router.dashboard'), icon: 'dashboard', affix: true
      }
    }
    ]
  },
  // ======================================================
  {
    path: '/user-center',
    hidden: true,
    component: Layout,
    children: [{
      path: 'change-pwd',
      name: 'ChangePwd',
      hidden: true,
      component: _import('user-center/changePwd/index'),
      meta: {
        title: i18n.t('router.changePassword'), icon: 'function'
      }
    }
    ]
  },
  {
    path: '/member',
    hidden: true,
    component: Layout,
    children: [{
      path: 'member-batch',
      name: 'MemberBatch',
      hidden: true,
      component: _import('account-center/member/batch'),
      meta: {
        title: '批量新增', icon: 'function'
      }
    }
    ]
  }
  // {
  //   path: '/account-center',
  //   component: Layout,
  //   meta: {
  //     title: '账户中心', icon: 'sub-menu'
  //   },
  //   children: [
  //     {
  //       path: 'recharge',
  //       name: 'AccountRecharge',
  //       component: _import('account-center/recharge/index'),
  //       meta: {
  //         title: '我要充值', icon: 'function'
  //       }
  //     },
  //     {
  //       path: 'withdraw',
  //       name: 'AccountWithdraw',
  //       component: _import('account-center/withdraw/index'),
  //       meta: {
  //         title: '我要提现', icon: 'function'
  //       }
  //     }
  //   ]
  // },
]

export default new Router({
  // mode: 'hash', // 后端支持可开
  // mode: 'history',
  // base: '/', // static
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})

export function getAsyncRouterMap(callback, userInfo) {
  if (userInfo.userType === 4) {
    listUserRouter(userInfo.userId).then(r => {
      var jsonRouter = r.data
      for (var i = 0; i < jsonRouter.length; i++) {
        if (jsonRouter[i].locale) {
          jsonRouter[i].meta.title = i18n.t(jsonRouter[i].locale)
        }
        if (jsonRouter[i].component === 'Layout') {
          jsonRouter[i].component = Layout
          if (!jsonRouter[i].meta.icon) {
            jsonRouter[i].meta.icon = 'module'
          }
        }
        if (jsonRouter[i].children) {
          for (var j = 0; j < jsonRouter[i].children.length; j++) {
            var child = jsonRouter[i].children[j]
            if (child.locale) {
              child.meta.title = i18n.t(child.locale)
            }
            if (child.component) {
              child.component = _import(child.component)
              if (!child.meta.icon) {
                child.meta.icon = 'sub-menu'
              }
            }
            if (child.children) {
              for (var k = 0; k < child.children.length; k++) {
                var childs = child.children[k]
                if (childs.locale) {
                  childs.meta.title = i18n.t(childs.locale)
                }
                if (childs.component) {
                  childs.component = _import(childs.component)
                  if (!childs.meta.icon) {
                    childs.meta.icon = 'function'
                  }
                }
              }
            } else {
              child.meta.icon = 'function'
            }
          }
        }
      }
      callback(jsonRouter)
    })
  } else {
    listRouter(userInfo.userId).then(r => {
      var jsonRouter = r.data
      for (var i = 0; i < jsonRouter.length; i++) {
        if (jsonRouter[i].locale) {
          jsonRouter[i].meta.title = i18n.t(jsonRouter[i].locale)
        }
        if (jsonRouter[i].component === 'Layout') {
          jsonRouter[i].component = Layout
          if (!jsonRouter[i].meta.icon) {
            jsonRouter[i].meta.icon = 'module'
          }
        }
        if (jsonRouter[i].children) {
          for (var j = 0; j < jsonRouter[i].children.length; j++) {
            var child = jsonRouter[i].children[j]
            if (child.locale) {
              child.meta.title = i18n.t(child.locale)
            }
            if (child.component) {
              child.component = _import(child.component)
              if (!child.meta.icon) {
                child.meta.icon = 'sub-menu'
              }
            }
            if (child.children) {
              for (var k = 0; k < child.children.length; k++) {
                var childs = child.children[k]
                if (child.locale) {
                  childs.meta.title = i18n.t(childs.locale)
                }
                if (childs.component) {
                  childs.component = _import(childs.component)
                  if (!childs.meta.icon) {
                    childs.meta.icon = 'function'
                  }
                }
              }
            } else {
              child.meta.icon = 'function'
            }
          }
        }
      }
      callback(jsonRouter)
    })
  }
}
