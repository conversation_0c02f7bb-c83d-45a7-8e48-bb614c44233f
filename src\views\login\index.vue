<template>
  <div>
    <div v-if="device === 'mobile'">
      <div class="login-container">
        <div class="logo-box">
          <div class="logo" />
        </div>
        <el-form v-show="keyboardLogin" ref="loginForm" :rules="loginRules" auto-complete="off" :model="loginForm"
          label-position="left" label-width="0px" class="card-box login-form">
          <h3 class="title">{{ $t('login.title') }}</h3>
          <lang-select class="set-language" />
          <el-input v-model="loginForm.password" type="password" style="position:fixed;left:0px;top:0px;width:0px;"
            clearable />
          <el-input v-model="loginForm.username" style="position:fixed;left:0px;top:0px;width:0px;" clearable />
          <el-form-item prop="username" class="with-border" style="margin-top:55px;">
            <span class="svg-container svg-container-login">
              <svg-icon icon-class="user" />
            </span>
            <el-input v-model="loginForm.username" :autofocus="true" name="username" type="text" auto-complete="off"
              :placeholder="$t('login.placeholder.account')" />
          </el-form-item>
          <el-form-item prop="password" class="with-border" style="margin-top:25px;">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input v-model="loginForm.password" name="password" :type="passwordType" auto-complete="off"
              :placeholder="$t('login.placeholder.password')" @keyup.enter.native="handleLogin" />
            <span class="show-pwd" @click="showPwd"><svg-icon icon-class="eye" /></span>
          </el-form-item>
          <!--<el-form-item class="with-border" prop="captchaCode" style="margin-top:18px;">
          <span class="svg-container">
            <svg-icon icon-class="verifyCode" />
          </span>
          <el-input v-model="loginForm.captchaCode" :placeholder="$t('login.placeholder.verifyCode')" @keyup.enter.native="handleLogin" />
          <div class="captcha">
            <el-tooltip :content="$t('login.tips')">
              <img :src="imgUrl" @click="changeCaptcha">
            </el-tooltip>
          </div>
        </el-form-item> -->
          <div class="agree">
            <el-checkbox v-model="remember">
              <span :class="'detail ' + (remember ? 'check' : 'uncheck')">记住密码</span>
            </el-checkbox>
            <el-checkbox v-model="agree" />
            <span :class="'detail ' + (agree ? 'check' : 'uncheck')" @click.stop="agree = !agree">同意游戏协议</span>
            <span class="detail" @click="handleAgree">查看协议</span>
          </div>
          <el-form-item style="margin-top:45px;">
            <el-button type="primary" size="medium" style="width:100%;" :loading="loading"
              @click.native.prevent="handleLogin">
              {{ $t('operate.login') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div v-else class="login-main">
      <div class="main-box">
        <div class="login-left">
          <div v-if="bannerItem.sytle == 1" class="banner-text-inner">
            <div class="banner-text-con">
              <h2>
                <span class="piece">{{ $t('login.bodyTittle') }}</span>
              </h2>
              <div class="des-text">
                <p>{{ $t('login.bodyDesc') }}</p>
              </div>
            </div>
            <router-link v-if="bannerItem.url" :to="bannerItem.url" target="route" class="banner-btn">立即查看</router-link>
          </div>
          <img :src="bannerItem.img" class="fade-in-up-infinite">
        </div>
        <div class="login-container">
          <el-form v-show="keyboardLogin" ref="loginForm" :rules="loginRules" auto-complete="off" :model="loginForm"
            label-position="left" label-width="0px" class="card-box login-form">
            <h3 class="title">{{ $t('login.title') }}</h3>
            <lang-select class="set-language" />
            <el-input v-model="loginForm.password" type="password" style="position:fixed;left:0px;top:0px;width:0px;"
              clearable />
            <el-input v-model="loginForm.username" style="position:fixed;left:0px;top:0px;width:0px;" clearable />
            <el-form-item prop="username" class="with-border" style="margin-top:55px;">
              <span class="svg-container svg-container-login">
                <svg-icon icon-class="user" />
              </span>
              <el-input v-model="loginForm.username" :autofocus="true" name="username" type="text" auto-complete="off"
                :placeholder="$t('login.placeholder.account')" />
            </el-form-item>
            <el-form-item prop="password" class="with-border" style="margin-top:25px;">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <el-input v-model="loginForm.password" name="password" :type="passwordType" auto-complete="off"
                :placeholder="$t('login.placeholder.password')" @keyup.enter.native="handleLogin" />
              <span class="show-pwd" @click="showPwd"><svg-icon icon-class="eye" /></span>
            </el-form-item>
            <!--<el-form-item class="with-border" prop="captchaCode" style="margin-top:18px;">
            <span class="svg-container">
              <svg-icon icon-class="verifyCode" />
            </span>
            <el-input v-model="loginForm.captchaCode" :placeholder="$t('login.placeholder.verifyCode')" @keyup.enter.native="handleLogin" />
            <div class="captcha">
              <el-tooltip :content="$t('login.tips')">
                <img :src="imgUrl" @click="changeCaptcha">
              </el-tooltip>
            </div>
          </el-form-item> -->
            <div class="agree">
              <el-checkbox v-model="remember">
                <span :class="'detail ' + (remember ? 'check' : 'uncheck')">记住密码</span>
              </el-checkbox>
              <el-checkbox v-model="agree" />
              <span :class="'detail ' + (agree ? 'check' : 'uncheck')" @click.stop="agree = !agree">同意游戏协议</span> <span
                class="detail" @click.stop="handleAgree">查看协议</span>
            </div>
            <el-form-item style="margin-top:45px;">
              <el-button type="primary" size="medium" style="width:100%;" :loading="loading"
                @click.native.prevent="handleLogin">
                {{ $t('operate.login') }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <el-dialog title="游戏协议" :visible.sync="dialogFormVisible" append-to-body :close-on-press-escape="false"
      :close-on-click-modal="false" top="5vh" :width="device === 'mobile' ? '90%' : '65%'">
      <div class="agree-content">
        <div class="item bold">本网站隶属于优势玩家俱乐部，旨在为各位玩家提供练习参考，不具有任何实际投资建议。所有玩家在网站上的行为必须遵从以下规则：</div>
        <div class="item bold">本条款适用范围</div>
        <div class="item">1.1
          当玩家点击"同意"后进入我们的网站，或者在我们俱乐部开立账户都被视为接受这些条款。当玩家同意接受这些条款或者当玩家继续使用本网站时，就要受到本俱乐部的条款以及保密条款的约束。当这些条款和其它作为参考的条款相抵触时，应该以这些条款为准。
        </div>

        <div class="item">1.2 本俱乐部保留有在任何时间修改条款的权利，并不需要预先通知玩家。但是本俱乐部将尽量将重要条款的变化以在我们网站显着的地方公布的形式来通知我们的玩家。</div>

        <div class="item">1.3
          玩家有责任义务定时地查阅我们的条款以确保仍然同意接受这些条款。当用户仍继续使用本网站服务，将视为用户无条件同意并接受所公布的条款规则和保密条款，以及相关的修改或更新。任何在条款规则修改生效前的游玩都是以当时的条款为准。
        </div>

        <div class="item">1.4 所有条款规则及保密条款都是书面形式提供。</div>

        <div class="item bold">2.玩家承诺</div>
        <div class="item">2.1
          当玩家接受这些条款规则就表示玩家无条件地保证玩家已经年满18周岁，或者在所在当地已经到达合法年龄并且有能力对他们自己的行为负责并受到我们条款约束。本俱乐部保留随时中止任何涉及未成年人交易的权利。</div>

        <div class="item">2.2 此外玩家必须同意接受及无条件地向本俱乐部承诺保证：
          这是玩家自身的责任，在本俱乐部注册及游玩前确定他们的行为是符合当地及国家法律的要求。我们鼓励玩家在注册或游玩前咨询法律意见以核实确定与本俱乐部的交易在任何方面都没有违反任何法律条例。对于玩家违反任何当地或者国家法律条例的行为,本俱乐部均不承担任何责任。我们鼓励玩家在本俱乐部注册和/或游玩前咨询法律意见。
        </div>

        <div class="item bold">3.账户及个人信息</div>
        <div class="item">3.1 玩家账户由本俱乐部提供，不得随意转借他人。</div>

        <div class="item">3.2 在使用本俱乐部游玩服务前，玩家须确认账户是否已开通。</div>

        <div class="item">3.3
          玩家将拥有专属的用户名和密码（"账户登入信息"）。用户应保密其账户登入信息，并对任何滥用或非法对第三方披露账户登入信息的后果负责。当用户发现其账户登入信息已被泄露，其账户安全性已经降低或曾被第三方使用时，应立即告知本俱乐部，本俱乐部会根据情况提供新的账户登入信息给用户。用户使用正确的账户登入信息在本网站作出的所有在线游玩或要求将被视为有效。
        </div>

        <div class="item">3.4
          当用户发现其账户信息被第三方滥用时，应尽快通知本俱乐部停用该账户。对停用账户过程中发生的合理延误，本俱乐部将不需负任何责任。只有在收到用户的通知称其账户登入信息安全性已降低，本俱乐部停用其账户之后（而不是之前），以该账户所作的在线游玩或要求才会被视为作废。
        </div>

        <div class="item">3.5
          本俱乐部有可能不定期地要求用户更改密码或账户登入信息。当本俱乐部有理由相信有破坏本网站安全或滥用本网站的情况出现时，有可能会暂停用户的账户。本俱乐部可在对用户发出通知后，有权更改用户的账户登入信息。</div>

        <div class="item">3.6 为维护网络安全和保护用户的资产，本俱乐部会不定期进行网络安全测试。因此在安全测试中，当本俱乐部为了核实用户的户主身份要求用户提供额外的信息或文件时，用户须予以配合。</div>

        <div class="item">3.7
          用户有责任主动维护其账户信息。就此而言，用户在每12个月之内必须至少有一次登入本网站并使用本俱乐部服务。如果用户账户连续12个月或者更长时间没有交易，本俱乐部将有权终止该账户，用户将会丧失账户内余额及对本俱乐部提出要求的权利。
        </div>

        <div class="item">3.8
          如果用户违反了本条款的任何规定，本俱乐部可以随时停用或取消该用户的账户。假如本俱乐部认为用户严重违反了本条款的规定，本俱乐部有权依照本条款并依法采取补救措施，和有权保留用户的账户余额作为该用户履行因违反本条款而可能产生的债务的保证金。
          第三方损失；虚假的个人信息；"筹码倾销"；通过游玩转移资金；或任何本俱乐部有理由相信构成欺诈或不道德交易的行为。</div>
      </div>
      <div class="footer">
        <el-button type="primary" size="mini" @click="handleAgreeProtocol">同意</el-button>
        <el-button type="danger" size="mini" @click="handleCancelProtocol">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LangSelect from '@/components/LangSelect'
import { checkCaptchaCode } from '@/api/common/login'
import { baseUrl } from '@/data/config'
import { mapGetters } from 'vuex'

export default {
  name: 'Login',
  components: { LangSelect },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('login.validateMsg.account')))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('login.validateMsg.password')))
      } else {
        callback()
      }
    }
    const validateCaptchaCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('login.validateMsg.verifyCode')))
      }
      checkCaptchaCode(this.loginForm.captchaCode, this.publicKey).then(
        r => {
          this.captchaCodeVerify = false
          if (r.data) {
            this.captchaCodeVerify = true
            callback()
          } else {
            this.captchaCodeVerify = true
            callback()
            // callback(new Error(this.$t('login.validateMsg.verifyCodeError')))
          }
        }
      )
    }
    return {
      captchaCodeVerify: false,
      publicKey: '',
      imgUrl: '',
      bannerItem: { id: 1, title: this.$t('login.bodyTittle'), desc: this.$t('login.bodyDesc'), url: '', sytle: 1, img: require('../../assets/images/banner-left.png') },
      userTypeIcon: 'fa-user-o',
      userType: 2,
      loginForm: {
        username: '',
        password: '',
        captchaCode: ''
      },
      userTypeVisible: false,
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        captchaCode: [
          { required: true, trigger: 'blur', validator: validateCaptchaCode },
          { len: 4, message: this.$t('login.validateMsg.verifyCodeLen'), trigger: 'blur' }
        ]
      },
      passwordType: 'password',
      loading: false,
      keyboardLogin: true,
      validKey: '',
      dialogFormVisible: false,
      agree: true,
      remember: true
    }
  },
  computed: {
    ...mapGetters(['device'])
  },
  created() {
    this.changeCaptcha()
  },
  methods: {
    createKey() {
      return Date.now() + '_' + Math.floor(Math.random() * 1000)
    },
    changeCaptcha() {
      this.publicKey = this.createKey()
      this.imgUrl = baseUrl + '/captcha/getCaptchaCode/' + this.publicKey
      if (localStorage.getItem('remember')) {
        this.loginForm.username = localStorage.getItem('username')
        this.loginForm.password = localStorage.getItem('password')
        this.remember = true
      } else {
        this.remember = false
      }
    },
    showLoginType() {
      this.keyboardLogin = !this.keyboardLogin
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    handleLogin() {
      if (!this.agree) {
        this.$message.error('请勾选同意游戏协议')
        return
      }
      if (window.returnCitySN) {
        this.loginForm.loginIp = window.returnCitySN['cip']
        this.loginForm.loginAddr = window.returnCitySN['cname']
      }
      if (this.remember) {
        localStorage.setItem('remember', this.remember)
        localStorage.setItem('username', this.loginForm.username)
        localStorage.setItem('password', this.loginForm.password)
      } else {
        localStorage.removeItem('remember')
        localStorage.removeItem('username')
        localStorage.removeItem('password')
      }
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('Login', this.loginForm).then(r => {
            if (r.data.code === 20000) {
              this.$router.push({ path: '/dashboard/index' })
            } else {
              this.loading = false
              if (r.data.code === 400020) {
                this.$message.error(this.$t('login.validateMsg.passwordError'))
              } else if (r.data.code === 400012) {
                this.$message.error(this.$t('login.validateMsg.accountClosed'))
              } else if (r.data.code === 400011) {
                this.$message.error(this.$t('login.validateMsg.accountNotExisted'))
              } else {
                this.$message.error(r.data.message)
              }
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    handleAgree() {
      this.dialogFormVisible = !this.dialogFormVisible
      console.log('this.dialogFormVisible.............', this.dialogFormVisible)
    },
    handleAgreeProtocol() {
      this.dialogFormVisible = false
      this.agree = true
    },
    handleCancelProtocol() {
      this.dialogFormVisible = false
      this.agree = false
    }

  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
@import "src/styles/mixin.scss";
$bg: #2d3a4b;
$dark_gray: #333333;

.login-main {
  width: 100%;
  position: absolute;
  top: 60px;
  left: 0px;
  background-color: #000000;
  background-image: url(../../assets/images/login-bg.png);
  background-repeat: no-repeat;
  background-position: 100px 10px;
  height: 527px;
  overflow: hidden;
}

/*banner图*/
.banner-text-inner {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  margin-left: -600px;
  color: white;
}

.banner-text-con {
  position: absolute;
  top: 90px;
}

.banner-text-con .piece {
  margin-right: 30px;
  font-size: 40px !important;
}

.banner-text-con .des-text {
  font-size: 20px !important;
  line-height: 36px;
  width: 600px;
}

.banner-btn {
  font-size: 20px;
  color: #fff;
  border: 1px solid #fff;
  width: 18.33%;
  height: 43px;
  line-height: 43px;
  display: inline-block;
  text-align: center;
  border-radius: 2px;
  position: absolute;
  bottom: 80px;
}

.login-left {
  float: left;
  width: 700px;
  height: 500px;
}

.login-left img {
  float: left;
  margin-top: 260px;
  width: 516px;
  height: 316px;
  margin-left: -20px;
}

.login-container {
  font-size: 12px;

  .el-input {
    display: inline-block;
    height: 35px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      border-radius: 0px;
      padding: 10px 5px 5px 15px;
      height: 35px;
    }
  }

  .el-form-item.with-border {
    border-radius: 5px;
    border: 1px solid #d4d4d4;
    color: #454545;
  }

  .el-form-item--small .el-form-item__content {
    line-height: 25px;
  }

  .qrcode-login {
    position: absolute;
    top: 3px;
    right: 5px;
    font-size: 40px;
    color: #a1a1a1;
    cursor: pointer;
  }

  .qrcode-login:hover {
    color: $dark_gray;
  }

  .keyboard-login {
    position: absolute;
    top: 3px;
    right: 5px;
    font-size: 40px;
    color: #a1a1a1;
    cursor: pointer;
  }

  .keyboard-login:hover {
    color: $dark_gray;
  }

  .qrcode-data {
    height: 200px;
  }

  position: relative;
  right: 0px;
  background-color:#ffffff;

  .login-form {
    position: absolute;
    left: 0;
    right: 0;
    width: 520px;
    padding: 35px 35px 15px 35px;
    margin: 120px auto;
    margin-top: 85px;
    margin-right: 0px;
    border: 1px solid #d4d4d4;
    float: left;
    /*padding:20px 25px 25px 25px;*/
    width: 340px;
    text-align: center;
    background-color: #ffffff;
    height: 360px;
  }

  .tips {
    font-size: 12px;
    color: #333333;
    padding-top: 10px;
    padding-bottom: 10px;
    margin: 0 auto;

    span {
      cursor: pointer;
      margin-left: 100px;
    }

    span:hover,
    a:hover {
      color: #006dfe;
    }
  }

  .svg-container {
    padding: 0px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    font-size: 14px !important;

    &_login {
      font-size: 20px;
    }
  }

  .title {
    font-size: 20px !important;
    color: $dark_gray;
    margin: 0px auto 40px auto;
    text-align: center;
    font-weight: bold;
    position: relative;
  }

  .user-type {
    color: #a1a1a1;
    position: absolute;
    top: 3px;
    left: 5px;
    font-size: 20px;
  }

  .user-type:hover {
    color: $dark_gray;
  }

  .set-language {
    color: #333333;
    position: absolute;
    top: 35px;
    left: 35px;
  }

  .set-language:hover {
    color: $dark_gray;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 46px !important;
      font-weight: 400;
      color: $dark_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }

    .set-language {
      color: #a1a1a1;
      position: absolute;
      top: 5px;
      right: 0px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 30%;
    bottom: 10px;
  }
}

.account-type {
  position: relative;
  text-align: center;
  margin-bottom: 0px;
  line-height: 8px;
  height: 8px;
  cursor: pointer;
  border-bottom: 1px solid #d4d4d4;
}

.type-title {
  padding: 0 7px;
  position: relative;
  z-index: 2;
  background-color: #fff;
  font-size: 13px !important;
  color: #666666;
}

.type-title:hover {
  color: #171717;
}

.type-title .type-icon {
  padding-right: 7px;
  display: inline-block;
  position: relative;
  z-index: 2;
}

.captcha {
  height: 35px;
  width: 74px;
  float: right;
  position: absolute;
  right: 1px;
  top: 0px;
}

.captcha img {
  height: 100%;
  width: 100%;
}

.mobile .login-container {
  background-color: transparent;
}

.mobile .login-container .login-form {
  width: 90%;
  padding-left: 5%;
  margin: 0 auto;
  margin-top: 15%;
  background-color: #0a1519;
  border: 1px solid #323d43;
  border-radius: 5px;
}

.mobile .login-container .el-form-item.with-border {
  border: 1px solid #323d43;
  color: #ffffff;
}

.mobile .el-form-item--small .el-form-item__error {
  background-color: transparent !important;
}

.mobile .login-container .svg-container {
  color: #ffffff;
}

.mobile .login-container .title {
  color: #ffffff;
}

.mobile .login-container .set-language {
  color: #ffffff;
}

.mobile .login-container .el-input input {
  color: #ffffff !important;
}

.mobile .logo-box {
  display: flex;
  /*flex-direction: column;
     align-items: center;
    justify-content: center;*/
}

.mobile .logo {
  height: 70px;
  width: 192px;
  background-image: url(../../assets/images/logo.png);
  background-repeat: no-repeat;
  background-size: 192px 70px;
  border: none;
  margin: 0 auto;
  margin-top: 18%;
}

.agree {
  background-size: 98% 98%;
  position: relative;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail {
  color: #006dfe;
  margin-left: 10px;
  cursor: pointer;
  font-size: 0.8rem;
}

.detail.check {
  margin-left: 5px;
  color: #006dfe;
}

.detail.uncheck {
  margin-left: 5px;
  color: #666666;
}

.agree-text {
  font-size: 1.2rem;
}

.agree-content {
  line-height: 25px;
}

.footer {
  display: flex;
  margin-left: 40%;
  padding-top: 10px;
  padding-bottom: 10px;
}

.captcha {
  background-size: 98% 98%;
  position: relative;
}

.captcha img {
  position: absolute;
  top: 0%;
  right: 3.5%;
  width: auto;
  margin-top: 1.5%;
  height: 80%;
  opacity: 0.8;
}

.username .input-control,
.password .input-control,
.captcha .input-control {
  color: #FFF;
  width: 100%;
  height: 100%;
  padding-left: 23%;
  display: inline-block;
  background: transparent;
  border: none;
}

.btn-panel {
  height: 16%;
}

.btn-login,
.btn-guest {
  cursor: pointer;
  height: 100%;
  ;
  text-align: center;
  padding-top: 4%;
  float: left;
}

.btn-login {
  background-color: #006dfe;
  width: 52.8%;
  margin-right: 5%;
}

.btn-guest {
  background-color: #f56c6c;
  width: 40%;
}

.loading {
  width: 20%;
  height: 60%;
}
</style>
