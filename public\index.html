<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="referrer" content="no-referrer" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>
    <%= webpackConfig.name %>
  </title>
  <script src="http://pv.sohu.com/cityjson?ie=utf-8"></script>
</head>

<body>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>
<script>
  window.getip = function (obj) {
    window.returnCitySN = { cip: obj.ip, cname: obj.addr }
    console.log(window.returnCitySN)
  }
  function loadGetIP() {
    if (window.returnCitySN) {
      window.clearInterval(timer)
    }
    var oHead = document.getElementsByTagName('HEAD').item(0)
    var oScript = document.createElement("script")
    oScript.type = "text/javascript"
    oScript.src = "https://whois.pconline.com.cn/ipJson.jsp?callback=getip"
    oHead.appendChild(oScript)
  }
  var timer = setInterval(loadGetIP, 1000)
</script>
<script src="https://whois.pconline.com.cn/ipJson.jsp?callback=getip"></script>

</html>