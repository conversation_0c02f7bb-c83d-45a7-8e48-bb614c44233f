import request from '@/utils/request'

export function listMenu(subsystemId) {
  return request({
    url: '/menu/list',
    method: 'get',
    params: {
      subsystemId: subsystemId
    }
  })
}
export function listOption(parentId) {
  return request({
    url: '/menu/listOption',
    method: 'get',
    params: {
      parentId: parentId
    }
  })
}
export function listMenuButtonOption(refId, refType, refNo) {
  return request({
    url: '/menu/list/menuButtonOption',
    method: 'get',
    params: {
      refId: refId,
      refType: refType,
      refNo: refNo
    }
  })
}
export function listUserMenuButtonOption(userId) {
  return request({
    url: '/menu/list/userMenuButtonOption',
    method: 'get',
    params: {
      userId: userId
    }
  })
}
export function addMenu(data) {
  return request({
    url: '/menu/add',
    method: 'post',
    data
  })
}

export function deleteMenu(id) {
  return request({
    url: '/menu/delete',
    method: 'get',
    params: {
      id: id
    }
  })
}

export function updateMenu(data) {
  return request({
    url: '/menu/update',
    method: 'post',
    data
  })
}
