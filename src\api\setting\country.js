import request from '@/utils/request'

export function listCountry(data) {
  return request({
    url: '/country/list',
    method: 'post',
    data
  })
}
export function addCountry(data) {
  return request({
    url: '/country/add',
    method: 'post',
    data
  })
}
export function updateCountry(data) {
  return request({
    url: '/country/update',
    method: 'post',
    data
  })
}
export function deleteCountry(ids) {
  return request({
    url: '/country/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
export function blockCountry(ids) {
  return request({
    url: '/country/block',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
export function unblockCountry(ids) {
  return request({
    url: '/country/unblock',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
