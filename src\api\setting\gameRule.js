import request from '@/utils/request'
export function list(data) {
  return request({
    url: '/gameRule/list',
    method: 'post',
    data
  })
}
export function add(data) {
  return request({
    url: '/gameRule/add',
    method: 'post',
    data
  })
}
export function update(data) {
  return request({
    url: '/gameRule/update',
    method: 'post',
    data
  })
}
export function del(ids) {
  return request({
    url: '/gameRule/delete',
    method: 'get',
    params: {
      ids: ids
    }
  })
}
export function getByRuleNo(ruleNo, createBy) {
  return request({
    url: '/gameRule/getByRuleNo',
    method: 'get',
    params: {
      ruleNo: ruleNo,
      createBy: 0
    }
  })
}

export function verify(ruleNo, createBy) {
  return request({
    url: '/gameRule/verify',
    method: 'get',
    params: {
      ruleNo: ruleNo,
      createBy: createBy
    }
  })
}
