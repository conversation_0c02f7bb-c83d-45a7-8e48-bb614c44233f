export default {
  system: {
    reflesh: '리플',
    close: '닫기',
    closeOther: '닫기 기타',
    closeAll: '모두 닫기'
  },
  test: {
    title: '페이지 스타일 설정',
    theme: '테마 색상'
  },
  login: {
    title: '사용자 로그인',
    tips: '명확하게 볼 수 없습니다. 클릭하여 변경',
    bodyTittle: '优势玩家 의 궁극적인 경험',
    bodyDesc: '합법적인 라이선스, 첨단 장비, 고품질 서비스, 전문 인력, 24시간 공정하고 안전한 게임 서비스를 제공합니다. 재충전이 편리하고 빠른 인출, 실제 비디오, 게임의 재미를 즐길 수 있습니다.',
    placeholder: {
      account: '계정',
      password: '비밀번호',
      verifyCode: '확인 코드'
    },
    validateMsg: {
      verifyCodeLen: '인증 코드 길이는 4자리입니다.',
      verifyCodeError: '인증 코드 오류',
      verifyCode: '인증 코드를 입력 해주세요',
      account: '로그인 계정을 입력하세요',
      password: '로그인 비밀번호를 입력해주세요',
      passwordError: '못된 비밀번호',
      accountClosed: '계정 폐쇄',
      accountNotExisted: '폐쇄되었습니다'
    }
  },
  game: {
    gameType: '게임 타입',
    tableNo: '테이블 번호',
    matchNo: '슈 번호',
    gameNo: '게임 번호',
    gameNumber: '게임 번호',
    winAmount: '배당 금액',
    result: '결과',
    betDetail: '베팅 세부 사항',
    balance: '균형',
    betTime: '배팅 시간',
    limit: '한계',
    banker: 'R',
    player: 'B',
    luckSix: 'Luck Six',
    tie: 'D',
    pair: '페어',
    winner: '우승자',
    bankerAbbr: 'R',
    playerAbbr: 'B',
    bankerPair: 'C',
    playerPair: '플레이어 페어',
    allPair: '뱅커 페어,플레이어 페어',
    noPair: 'None',
    dragon: 'Dragon',
    tiger: 'Tiger',
    dragonAbbr: 'D',
    tigerAbbr: 'T',
    player1Equal: '플레이어 1 동일한',
    player2Equal: '플레이어 2 동일한',
    player3Equal: '플레이어 3 동일한',
    player1Double: '플레이어 1 더블',
    player2Double: '플레이어 2 더블',
    player3Double: '플레이어 3 더블',
    player1Super: '플레이어 1 감독자',
    player2Super: '플레이어 2 감독자',
    player3Super: '플레이어 3 감독자',
    chipCustom: '사용자 정의',
    remain: '',
    totalMax: '테이블 제한',
    dealer: 'Dealer',
    askPlayer: '다음 P',
    askBanker: '다음 B',
    askDragon: '다음 D',
    askTiger: '다음 T',
    changeTable: '테이블 변경',
    confirm: '비밀번호확인',
    cancel: '취소',
    repeat: '반복하다',
    toBet: '내기하다',
    openGame: '열리는',
    waiting: '대기 중',
    addTime: '추가된 시간',
    notChipTimes: '베팅하지 않은 시간이 다음보다 큽니다.',
    greaterTotalMax: '베팅 금액이 한도보다 큽니다.',
    lessMin: '베팅 금액이 최소 한도 미만입니다.',
    greaterMax: '베팅 금액이 최대 한도보다 큽니다.',
    videoHigh: '고품질',
    videoLow: '저품질',
    yourAreHere: '당신은 여기에있다',
    bull0: 'N',
    bull10: 'B',
    remainPointLow: '균형이 충분하지 않습니다',
    totalText: 'Total'
  },
  bull: {
    bankerWin: '뱅커 이기다',
    player1Win: '플레이어1 이기다',
    player2Win: '플레이어2 이기다',
    player3Win: '플레이어3 이기다',
    noBull: 'No',
    bull: 'Bull'
  },
  home: {
    table: {
      totalAgent: '총 에이전트',
      newAgentToday: '오늘 새로운 에이전트',
      totalMember: '총 회원',
      newMembersToday: '오늘의 신규 회원',
      loginAccount: '로그인 계정',
      nickname: '별명',
      loginTime: '로그인 시간',
      loginIp: 'IP 주소 로그인',
      loginAddress: '로그인 주소',
      availableBalance: '사용 가능한 잔액',
      share: '공유하다',
      rateOfCodeWashing: '코드 세척 비율'
    }
  },
  status: {
    enabled: '활성화됨',
    disabled: '장애를 입히다',
    open: '열림',
    closed: '닫은',
    onLine: '온라인',
    offLine: '오프라인'
  },
  operate: {
    login: '로그인',
    sysgen: '자동',
    add: '추가',
    edit: '편집',
    modifyResult: '결과 수정',
    deleteOne: '삭제',
    save: '저장',
    change: '수정',
    delete: '삭제',
    confirm: '확인',
    cancel: '취소',
    viewDetail: '자세히보기',
    moreOperate: '추가 설정',
    accountEnabled: '활성화',
    accountDisabled: '로그인차단',
    authSetting: '인증 설정',
    betEnabled: '베팅 가능',
    betDisabled: '차단',
    memberEdit: '회원 편집',
    agentEdit: '에이전트 편집',
    passwordChange: '비밀번호 변경',
    rechargeWithdraw: '충전 또는 철회',
    settlement: '커미션지급',
    block: '블록',
    unblock: '차단 해제',
    message: {
      tips: 'Tips',
      cancel: '취소 된',
      success: '성공',
      fail: '불합격'
    },
    info: {
      continue: '계속하시겠습니까?'
    }
  },
  chargeWithdrawForm: {
    tittle: '충전 또는 철회',
    operation: '작업',
    recharge: '충전',
    withdraw: '인출',
    rechargeType: '충전 유형',
    amount: '금액',
    cash: '현금',
    sign: '징후',
    payout: '지불금',
    priceUnit: '페소',
    placeholder: {
      amount: '금액을 입력해주세요'
    },
    validateMsg: {
      amount: '금액은 0보다 커야 합니다!'
    }
  },
  memberAddForm: {
    validateMsg: {
      account: '귀하의 계정을 입력하십시오',
      accountExist: '계정이 이미 존재합니다.',
      rateSetting: '롤링 커미션을 설정해주세요',
      rateLimit: '롤링 커미션은 2%를 초과할 수 없습니다.',
      chipOptions: '베팅 칩 옵션을 선택하세요.',
      chipOptionsLimit: '최대 3개의 베팅 칩'
    }
  },
  member: {
    accountInfo: '계정 정보 작성',
    washRateInfo: '롤링 커미션 설정 ',
    limitInfo: '제한 설정',
    operateInfo: {
      stopInfo: '이 작업은 베팅을 비활성화합니다. 계속하시겠습니까?'
    },
    passForm: {
      tittle: '비밀번호 변경',
      loginAccount: '로그인 계정',
      oldPassword: '기존 비밀번호',
      newpassword: '새 비밀번호',
      password: '비밀번호',
      passwordConfirm: '비밀번호확인',
      placeholder: '비밀번호(6-16자 또는 숫자)',
      validateMsg: {
        password: '비밀번호를 입력해주세요',
        oldPassword: '이전 비밀번호를 입력하세요.',
        newPassword: '새 비밀번호를 입력하세요',
        oldPasswordError: '이전 비밀번호 오류',
        passwordConfirm: '확인 비밀번호를 입력해주세요',
        passwordFormate: '잘못된 비밀번호 형식',
        passwordDifferent: '비밀번호가 일치하지 않습니다.'
      }
    },
    bettingStatus: '베팅 상태',
    subordinateAgent: '에이전트',
    account: '계정',
    nickname: '별명',
    memberAccount: '회원 계정',
    memberNickname: '별명',
    fullName: '성명',
    mobilePhone: '전화 번호',
    memberType: '유형',
    normal: '정상',
    stealth: '몰래 하기',
    role: '역할',
    agent: '에이전트',
    avatar: '화신',
    pagcor: '파그코르',
    cashier: '출납원',
    currency: '통화',
    accountBalance: '계정 잔액',
    rateOfCodeWashing: '롤링 커미션',
    limitRed: '베팅 범위',
    onLine: '온라인',
    recentlyLoggedInIp: '마지막 로그인 Ip',
    recentlyLoggedInTime: '마지막 로그인 시간',
    createdTime: '시작시간',
    codeWashingFrom: '칩 수',
    codeWashingTo: '칩 수',
    washTips: '무한대의 경우 0',
    operation: '작업',
    totalDeposit: '총예금',
    totalWithdrawal: '총 인출',
    totalValidBet: '총 유효 베팅',
    totalWinOrLose: '총 승패',
    commission: '커미션(%)',
    commissionBalance: '커미션 밸런스',
    jetton: {
      limitSetting: '제한 설정',
      chipNo: '칩 번호',
      chipName: '칩 이름',
      lowerLimit: '최소 한도',
      upperLimit: '최대 한도',
      chipsOption: '칩 옵션'
    },
    placeholder: {
      memberAccount: '회원 계정으로 검색'
    },
    level: 'Level',
    betMin: 'Min Bet Per Time',
    betMax: 'Max Bet Per Time',
    betMax2: 'Max Bet Per Game',
    betMax3: 'Max Bet Draw Per Game',
    maxWin: 'Max Win Per Day',
    dataError: ' greater than '
  },
  agentForm: {
    accountStatus: '계정 상태',
    bettingStatus: '베팅 상태',
    agentAccount: '계정',
    agentNickname: '별명',
    fullName: '성명',
    mobilePhone: '휴대전화',
    accountBalance: '계정 잔액',
    rateOfCodeWashing: '롤링 커미션',
    limitRed: '베팅 범위',
    onLine: '온라인',
    share: '공유하다(%)',
    operation: '기타 설정',
    parentAgent: '상위 에이전트',
    createdTime: '시작시간',
    childAgent: '하위 에이전트',
    info: {
      noData: '더 이상 에이전트 데이터가 없습니다.',
      noParentData: '우수한 에이전트 데이터 없음',
      noChildData: '하위 수준 에이전트 데이터 없음'
    }
  },
  jetton: {
    chipInfo: '칩 정보',
    chipNo: '칩 번호',
    chipName: '칩 이름',
    betLowerLimit: '최소 베팅 한도',
    betUpperLimit: '베팅 최대 한도',
    chipOption: '칩 옵션',
    operation: '작업',
    validateMsg: {
      jettonNo: '칩 번호는 필수 항목입니다.',
      jettonName: '칩 이름은 필수 항목입니다.',
      downLimit: '베팅 하한선이 필요합니다',
      upperLimit: '베팅 상한선이 필요합니다'
    },
    msg: {
      chipRepeat: '칩 반복 설정',
      setChip: '칩을 먼저 설정하십시오',
      threeChip: '3종류의 칩을 먼저 설정해주세요',
      deleteData: '삭제할 데이터를 선택하세요.'
    }
  },
  tableInfo: {
    tableInfo: '데스크 정보',
    tableNo: '테이블 번호',
    tableName: '테이블 이름',
    tableNameEn: '영어로 이름',
    tableType: '테이블 타입',
    cebu: 'Cebu',
    manila: 'Manila',
    casino: 'Casino',
    networkTable: 'Network',
    liveTable: 'Live',
    gameType: '게임 타입',
    betMethod: '베팅 방법',
    PCVideoUrl: 'PC Video Url',
    PCVideoUrl1: 'PC Video Url 1',
    PCVideoUrl2: 'PC Video Url 2',
    PCVideoUrl3: 'PC Video Url 3',
    dealerUrl: 'Dealer Url',
    AppVideoUrl: 'App Video Url',
    AppVideoUrl1: 'App Video Url 1',
    AppVideoUrl2: 'App Video Url 2',
    AppVideoUrl3: 'App Video Url 3',
    shoeIp: 'Shoe Ip',
    port: 'Port',
    shoePort: 'Fight Interval',
    serverPort: 'Server Port',
    serverIp: 'Server Ip',
    notChipTimes: 'Not Chip',
    chipInterval: 'Chip Interval',
    totalMax: 'Total Max',
    totalMax2: 'Total Max 2',
    totalMax3: 'Total Max 3',
    betMin: 'Min Bet',
    betMax: 'Max Bet',
    tieMin: 'Min Tie Bet',
    tieMax: 'Max Tie Bet',
    pairMin: 'Min Pair Bet',
    pairMax: 'Max Pair Bet',
    superMin: 'Min Super 6',
    superMax: 'Max Super 6',
    isConfirm: 'Confirm Card',
    isHide: 'Hide Card',
    isJoin: 'Is Join',
    need: 'Yes',
    noNeed: 'No',
    freeCommission: 'Is Free',
    currencyCode: '통화 코드',
    freeYes: 'Yes',
    freeNo: 'No',
    status: '상태',
    operation: '컨트롤',
    validateMsg: {
      tableNo: '테이블 번호가 필요합니다',
      tableName: '테이블 이름은 필수 항목입니다',
      tableNameEn: '영어 이름은 필수 항목입니다.',
      gameType: '게임 유형은 필수 항목입니다.',
      gameCategory: '베팅 방법은 필수 항목입니다.',
      rtmp: 'PC Vedio Url 1 is required',
      rtmp2: 'PC Vedio Url 2 is required',
      rtmp3: 'PC Vedio Url 3 is required',
      dealerRtmp: 'Dealer Url is required',
      rtsp: 'App Vedio Url 1 is required',
      rtsp2: 'App Vedio Url 2 is required',
      rtsp3: 'App Vedio Url 3 is required',
      betMin: 'Min Bet is required',
      betMax: 'Max Bet is required',
      tieMin: 'Min Tie Bet is required',
      tieMax: 'Max Tie Bet is required',
      pairMin: 'Min Pair Bet is required',
      pairMax: 'Max Pair Bet is required',
      superMin: 'Min Super 6 is required',
      superMax: 'Max Super 6 is required',
      totalMax: 'Max Total is required',
      totalMax2: 'Max Total 2 is required',
      totalMax3: 'Max Total 3 is required',
      serverIp: 'Server Ip is required',
      serverPort: 'Server Port is required',
      shoeIp: 'Shoe Ip is required',
      shoePort: 'Fight Interval is required',
      notChipTimes: 'Not Chip is required'
    }
  },
  gameCategory: {
    netBet: 'Net Bet',
    mobileBet: 'Phone Bet',
    speed: 'Speed',
    proxy: 'Proxy'
  },
  gameTypeEnum: {
    all: 'All',
    baccarat: 'Baccarat',
    dragonAndTiger: 'DragonAndTiger',
    bullAndBull: 'BullAndBull'
  },
  tableInfoStatusEnum: {
    new: '새로운',
    end: '끝',
    betting: '도박이다',
    bet: '이미 베팅',
    start: '시작',
    wait: '기다리다',
    break: '부서지다',
    pause: '정지시키다',
    washing: '커미션'
  },
  timeUnit: {
    second: '초'
  },
  noticeInfo: {
    notice: '알아 채다',
    noticeStatus: '공지 현황',
    noticeType: '통지 유형',
    noticeTittle: '공지 제목',
    noticeContent: '공지 내용',
    noticeContentEn: '영어 콘텐츠',
    operation: '작업',
    placeholder: {
      messageType: '메시지 유형을 선택하십시오'
    }
  },
  noticeStatus: {
    valid: '유효한',
    invalid: '유효하지 않은'
  },
  noticeType: {
    webMessage: '웹 공지',
    frontMessage: '프론트 페이지 공지'
  },
  dealer: {
    account: '계정',
    nickName: '별명',
    fullName: '성명',
    phone: '핸드폰',
    avatar: '영상',
    onLine: '온라인',
    createTime: '시간 만들기',
    remark: '주목',
    operation: '작업',
    info: '딜러 정보',
    clickToAdd: '추가하려면 클릭',
    clickToChange: '변경하려면 클릭',
    validateMsg: {
      account: '계정이 필요합니다',
      avatar: '이미지는 필수 항목입니다.',
      nickname: '닉네임은 필수 항목입니다.',
      fullName: '전체 이름은 필수 항목입니다.',
      phoneNo: '전화번호는 필수 항목입니다.'
    },
    placeholder: {
      account: '계좌번호로 조회'
    }
  },
  pointHistory: {
    operateTime: '발급시간',
    userType: '사용자 유형',
    userAccount: '사용자 계정',
    nickName: '사용자 닉네임',
    balanceBefore: '이전 금액',
    operateAmount: '송금액',
    balanceAfter: '현재금액',
    operateAccount: '운영자[계정]',
    operateType: '작동 유형',
    placeholder: {
      pointType: '작업 유형별 쿼리',
      account: '계정별 쿼리'
    }
  },
  dateTemplate: {
    today: '오늘',
    aWeekAgo: '일주일 전',
    tomorrow: '내일',
    selectDate: '날짜를 선택하세요',
    to: '에게',
    startDate: '시작일',
    endDate: '종료일',
    lastMonth: '지난 달',
    lastWeek: '지난주',
    last3Months: '지난 3개월'
  },
  userType: {
    agent: '에이전트',
    member: '회원'
  },
  betInfo: {
    betNo: '베팅 번호',
    tableNo: '테이블 번호',
    shoeNo: '슈 번호',
    gameNo: '게임 번호',
    memberAccount: '회원 계정',
    memberName: '회원 이름',
    betAmount: '베팅 금액',
    betDetail: '베팅 세부 사항',
    cardType: '카드 유형',
    cardsView: '카드 보기',
    cardResult: '카드 결과',
    winAmount: '배당 금액',
    banlance: '균형',
    washAmount: '금액 세척',
    washRate: '롤링 커미션(%)',
    commission: '수수료',
    betTime: '배팅 시간',
    placeholder: {
      tableNo: '책상 번호',
      matchNo: '상자 번호',
      gameNo: '게임 번호',
      gameType: '게임 유형별 쿼리',
      betNo: '베팅 번호로 쿼리',
      userAccount: '사용자 계정별 쿼리'
    }
  },
  macthInfo: {
    tableInfo: '데스크 정보',
    shoeNo: '슈 번호',
    gameNo: '게임 번호',
    cardsResult: '카드 결과',
    gameStartTime: '게임 시작 시간',
    placeholder: {
      table: '테이블을 선택하십시오',
      gameNo: '게임 번호를 입력하세요.'
    }
  },
  memberSettle: {
    placeholder: {
      gameType: '게임 유형을 선택하십시오'
    },
    gameType: '게임 타입',
    memberAccount: '회원 계정',
    memberNickname: '별명',
    currentAmount: '현재 금액',
    betTimes: '배팅 시간',
    betAmount: '베팅 금액',
    winAmount: '배당 금액',
    totalWashAmount: '총 커미션',
    commissionAmount: '수수료 금액'
  },
  agentSettle: {
    placeholder: {
      gameType: '게임 유형을 선택하십시오'
    },
    gameType: '게임 타입',
    agentAccount: '에이전트 계정',
    agentNickname: '에이전트 닉네임',
    totalWinAmount: '총 배당 금액',
    totalWashAmount: '커미션 총계',
    washRate: '커미션(%)',
    commissionAmount: '수수료 금액',
    washIncome: '커미션 수익',
    share: '공유하다(%)',
    shareIncome: '소득 공유',
    totalIncome: '총 수입',
    superiorIncome: '우수한 소득',
    operation: '작업',
    back: '뒤',
    agentReport: '보고서'
  },
  jettonSettle: {
    agentAccount: '에이전트 계정',
    agentNickname: '에이전트 닉네임',
    memberAccount: '회원 계정',
    memberNickname: '별명',
    currentWashRate: '현재 세척율',
    totalWashAmount: '커미션 총계',
    commissionAmount: '총 수수료 금액',
    operation: '작업',
    agentTittle: '에이전트 수수료 정산',
    memberTittle: '회원 수수료 정산'
  },
  header: {
    logout: '로그 아웃',
    backHome: '첫 페이지로 돌아가기',
    passChange: '비밀번호 변경',
    appDownload: '앱 다운로드',
    menuNavigation: '메뉴 탐색',
    playNow: '지금 플레이',
    copyright: '优势玩家 판권 소유',
    welcome: '환영 로그인'
  },
  role: {
    roleName: '규칙 이름',
    roleNo: '규칙 번호',
    sortNo: '정렬 번호',
    userType: '사용자 유형',
    status: '상태',
    remark: '주목',
    operation: '작업',
    roleInfo: '규칙 정보',
    nodeLocation: '노드 위치',
    permissionConfiguration: '권한 구성'
  },
  button: {
    name: '버튼 이름',
    no: '버튼 번호',
    sortNo: '정렬 번호',
    buttonInfo: '버튼 정보',
    status: '상태',
    remark: '주목',
    operation: '작업',
    validateMsg: {
      buttonName: '버튼 이름을 입력하세요',
      buttonNo: '버튼 번호를 입력하세요',
      sortNo: '짧은 번호를 입력하십시오.'
    }
  },
  menu: {
    locale: 'Locale',
    componentName: '구성 요소 이름',
    componentPath: 'Component Path',
    menuIcon: '메뉴 아이콘',
    menuName: '메뉴 이름',
    menuUrl: 'Menu Url',
    sortNo: 'Sort No',
    status: '상태',
    remark: '주목',
    operation: '작업',
    menuInfo: '메뉴 정보',
    menuLocation: '메뉴 위치',
    systemMenu: '메뉴 시스템',
    menuType: '메뉴 레벨',
    ifHidden: '숨겨진 경우',
    funtionManage: '기능 관리자',
    buttonOptions: {
      slected: '선택한 기능',
      waiting: '선택 기능을 기다리는 중'
    },
    levelOptions: {
      level1: '원 레벨',
      other: '다른 레벨'
    }
  },
  commonStatus: {
    valid: '유효한',
    invalid: '유효하지 않은'
  },
  hiddenOptions: {
    hidden: 'Hidden',
    show: 'Show'
  },
  winRate: {
    placeholder: '게임 유형을 선택하세요.',
    gameType: '게임 타입',
    rateNo: '요금 번호',
    rate: '비율 비율',
    status: '상태',
    remark: '주목',
    operation: '작업',
    rateInfo: '비율 정보',
    validateMsg: {
      rate: '비율을 입력하십시오',
      remark: '비고를 입력하세요'
    }
  },
  currencyCode: {
    RMB: 'RMB',
    USD: 'USD'
  },
  router: {
    dashboard: '대시 보드',
    changePassword: '비밀번호 변경',
    authorityModule: '권한 설정',
    accountCenter: '파트너 관리',
    reportMange: '관리 내역',
    gameSetting: '게임 설정',
    menuMange: '메뉴 관리',
    buttonMange: '버튼 관리',
    roleManage: '역할 관리',
    pointHistory: '잔액 이동 내역',
    betBill: '베팅 내역',
    memberDailyReport: '회원 일일 보고서',
    agentDailyReport: '에이전트 일일 보고서',
    gameInTable: '본 게임',
    operateHistory: '운영 이력',
    commissionSettlement: '정산 내역',
    agentList: '에이전트 목록',
    agentAdd: '에이전트 추가',
    agentEdit: '에이전트 편집',
    memberList: '회원 목록',
    memberAdd: '회원 추가',
    memberEdit: '회원 수정',
    subAccountList: '하위 계정 목록',
    jetton: 'Jetton',
    tableInfo: '데스크 정보',
    notice: '알아 채다',
    dealer: '상인',
    winRate: '승률 지불',
    country: 'IP Blocking',
    gameResult: '게임 결과',
    ipWhiteList: 'IP White List',
    otherList: '기타 목록',
    otherAdd: '기타 추가',
    otherEdit: '기타 편집'
  },
  userTypeEnum: {
    superAdmin: '최고 관리자',
    agent: '에이전트',
    member: '회원',
    subAccount: '하위 계정',
    dealer: '상인'
  },
  operateTypeEnum: {
    login: '로그인 ',
    accountOperate: '계정 로그인차단/활성화',
    betOperate: '차단/활성화',
    recharge: '충전',
    withdraw: '인출',
    passwordChange: '비밀번호 변경',
    memberEdit: '회원 편집',
    agentEdit: '에이전트 편집',
    commissionSettle: '수수료 정산'
  },
  platformEnum: {
    pc: 'PC',
    android: 'Android',
    apple: 'Apple',
    browser: 'Browser'
  },
  operateHistory: {
    placeholder: {
      operateType: '작동 유형 선택',
      userType: '사용자 유형 선택',
      operatePlatform: '운영 플랫폼 선택',
      operateAccount: '운영 계정 입력'
    },
    operateAccount: '계정 운영',
    operateType: '작동 유형',
    operatedAccount: '운영 계정',
    userType: '사용자 유형',
    operatePlatform: '플랫폼 운영',
    operateIp: 'Operate Ip',
    operateArea: '운영 지역',
    operateTime: '발급시간'
  },
  subAccount: {
    subAccountInfo: '하위 계정 정보',
    placeholder: '계정별 쿼리',
    accountStatus: '계정 상태',
    subordinateAgent: '에이전트',
    account: '계정',
    nickname: '별명',
    name: '성명',
    phone: '핸드폰',
    onLine: '온라인',
    createdTime: '시작시간',
    remark: '주목',
    operation: '작업',
    subAccountAuth: '하위 계정 인증',
    password: '비밀번호',
    confirmPassword: '비밀번호확인'
  },
  blockStatusEnum: {
    all: '모두',
    blocked: '막힌',
    unblocked: '차단 해제'
  },
  countryInfo: {
    info: '국가 정보',
    code: '암호',
    name: '이름',
    nameEn: '영어 이름 ',
    block: '블록',
    yes: '예',
    no: '아니요',
    validateMsg: {
      name: '이름을 입력하세요',
      code: '코드를 입력하세요',
      nameEn: '영문 이름을 입력하세요'
    },
    msg: {
      blockData: '차단할 데이터를 선택하세요.',
      unblockData: '차단할 데이터를 선택하세요.',
      editData: '수정해야 할 데이터를 선택하세요.'
    },
    placeholder: {
      keywords: '이름/코드로 검색',
      blockStatus: '차단 상태로 검색'
    }
  },
  ipWhiteListInfo: {
    title: 'IP Info',
    type: 'IP Type',
    ipAddress: 'IP Address',
    blackIp: 'Black IP',
    whiteIp: 'White IP',
    placeholder: {
      type: 'Search by IP Type',
      ipAddress: 'Search by IP Address'
    }
  }
}
