import { constantRouterMap, getAsyncRouterMap } from '@/router'

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },
  actions: {
    GenerateRoutes({ commit }, userInfo) {
      return new Promise(resolve => {
        getAsyncRouterMap(function(data) {
          commit('SET_ROUTERS', data)
          resolve()
        }, userInfo)
      })
    }
  }
}
export default permission
