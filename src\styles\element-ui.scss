// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
  font-size: 12px;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.navbar .el-breadcrumb__inner.is-link, .navbar .el-breadcrumb__inner a {
  color: white;
}

//修改的样式======================
.el-menu-item {
  height: 45px;
  line-height: 45px;
  font-size: 12px !important;
}
.el-submenu__title{
  height: 45px;
  line-height: 45px;
  font-weight: bold;
  font-size: 12px !important;
}
.submenu-title-noDropdown{
  font-weight: bold
}
.el-submenu .el-menu-item {
  height: 45px;
  line-height: 45px;
}
.el-scrollbar{
  border-right: 1px solid #dcdfe6;
}
.el-menu-item.is-active{
  /* color: #f56c6c !important;
  background-color: #fef0f0 ecf8ff !important;*/
  color: #006dfe !important;
  background-color: #ffffff !important;
  font-weight: bold;
}
.menu-wrapper .el-menu-item:hover {
  font-weight: bold;
}
 //popover conifg
 .el-popover {
  min-width: 125px !important;
  font-size: 12px !important;
}
.link-type, .link-type:focus{
    color: $--color-primary !important;
}
.el-form-item__label{
  color: $--color-info  !important;
  font-weight: normal !important;
}
.el-form-item__label{
  min-width: 65px; 
}
.el-form--label-top .el-form-item__label{
  padding: 0px;
}
.el-badge__content{
  z-index: 2;
}
//tag
.el-tag--danger{
  color:$red;
}

.el-input__inner{
  color: $--color-info !important;
}

//el talbe
thead, .el-table{
  color: $--color-info !important;
};
th, td{
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}
.el-table th > .cell{
  line-height: 27px;
  word-break: keep-all;
}

.el-table-master{
  tr.current-row > td{
    background-color: #ffffff !important;
  }
  td{
    padding: 0px !important;
  };
  th{
    padding: 4px !important;
  };
  .cell{
    padding-left: 4px !important;
    padding-right: 0px !important;
  }
  
  .el-input__inner{
    border-radius: 0px !important;
    border: 0px solid $--color-primary !important;
    margin-left: -4px;
  };
  .el-input__inner:focus{
    border: 1px solid !important;
  };
}
/*el-divider*/
.el-divider--horizontal {
  border-bottom: 1px solid #dcdfe6;
}
.el-divider {
  background-color: transparent !important;
}
/*el-pagination*/
.el-pagination {
  margin-top: 5px;
}
.inpu-large .el-input__inner{
  width: 400px !important;
}
.inpu-medium .el-input__inner{
  width: 300px !important;
}
.inpu-normal .el-input__inner{
  width: 200px !important;
}
.inpu-small .el-input__inner{
  width: 50px !important;
}
.inpu-small{
  width: 50px !important;
}
.el-table--striped .el-table__body tr.el-table__row--striped.current-row td {
  background-color: #fafafa !important; }
.el-table__body tr.hover-row > td, .el-table__body tr.hover-row.current-row > td, .el-table__body tr.hover-row.el-table__row--striped > td, .el-table__body tr.hover-row.el-table__row--striped.current-row > td {
  background-color: #fafafa !important; }
.el-table__body tr.current-row > td {
  background-color: #fafafa !important; }
.app-wrapper .el-input__inner{
  font-size: 12px !important;
}
.charge-mode .el-input__inner{
  font-size: 16px !important;
}
.main-box .el-input__inner {
  font-size: 14px !important;
}
.el-textarea__inner{
  font-size: 12px !important;
}
.el-autocomplete-suggestion li {
  font-size: 12px !important;
}
.el-select-dropdown__item{
  font-size: 12px !important;
}
.el-dialog__title{
  font-size: 16px !important;
}
.el-dialog__body {
  color: #333333 !important;
}
.el-message-box__content {
  font-size: 12px !important;
}
.el-message-box__title {
  font-size: 16px !important;
}
.el-select-dropdown__empty {
  font-size: 12px !important;
}
.el-form-item--small.el-form-item{
  margin-bottom:5px !important;
}
.form-item-normal .el-form-item--small.el-form-item{
  margin-bottom:18px !important;
}
.el-form-item--small .el-form-item__error{
  background-color: #ffffff !important;
  z-index: 2;
  padding-top: auto;
  margin-top: 1px;
}
.el-form-item__content .el-select {
  width: 100% !important;
}
.el-form-item__content .el-select {
  width: 100% !important;
}
.el-form-item__content .el-date-editor{
  width: 100% !important;
}
.el-form-item__content .el-input-number{
  width: 100% !important;
}
.el-form-item__content .el-autocomplete{
  width: 100% !important;
}
.el-picker-panel__shortcut{
  font-size: 12px !important;
}
.el-textarea__inner{
  color: #333333 !important;
}
.el-input-number .el-input__inner{
  text-align: left !important;
}
.radius-top-none .el-input__inner{
  text-align: center !important;
}
.el-range-editor--small .el-range-input {
  font-size: 12px !important; 
}
.el-dropdown-menu--small .el-dropdown-menu__item{
  font-size: 12px !important; 
}
.el-dialog__body{
  padding: 0px 20px !important;
}
.el-radio {
  color: #000000;
}
.el-checkbox {
  color: #000000;
}
.cell .el-button-group{
  line-height: 0px !important;
}
.custom-style .el-input__inner{
  border-radius: 0px;
  padding: 0px 5px !important;
  width: 100%;
}
.custom-style .el-autocomplete{
  width: 100%;
}
.custom-style .el-select{
  width: 100%;
}
.custom-style .el-radio + .el-radio{
  margin-left: 5px;
}
.charge-mode .el-radio__inner {
  margin-bottom: 20px;
}
.vue-image-crop-upload .vicp-wrap .vicp-operate a {
  color: #666666 !important;
  font-size: 12px !important;
  border: 1px solid #dcdfe6;
}
.el-divider--horizontal {
  margin: 12px 0 !important;
}
.mobile .el-message-box {
  width: 90%;
}
.mobile .el-message {
  width: 90%;
  min-width: 90%;
  top: 20% !important;
}

// mini-form
.mini-form .el-form-item {
  margin-bottom: 0px !important;
}
.mini-form .el-upload__tip__inline {
  margin-left: 10px;
  display: inline;
}
.mini-form .el-row .el-col {
  margin: 0px;
  padding: 0px;
}
.mini-form .el-form-item--mini {
  padding: 1px 0px;
}
.mini-form .el-form-item--mini.el-form-item, .mini-form .el-form-item--small.el-form-item{
  padding: 1px 0px;
}
.mini-form .el-form-item__error {
  display: none;
}
.mini-form .el-checkbox {
  margin-right: 8px;
}
.mini-form .el-radio {
  margin-right: 8px;
}
.mini-form .el-checkbox__label {
  padding-left: 0px;
}