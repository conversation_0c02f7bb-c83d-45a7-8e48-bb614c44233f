import request from '@/utils/request'

export function changePassword(username, password, validKey, userType) {
  return request({
    url: '/api/auth/password/change',
    method: 'get',
    params: {
      username: username,
      password: password,
      validKey: validKey,
      userType: userType
    }
  })
}

export function updateUserAuth(data) {
  return request({
    url: '/authority/user/update',
    method: 'post',
    data
  })
}
