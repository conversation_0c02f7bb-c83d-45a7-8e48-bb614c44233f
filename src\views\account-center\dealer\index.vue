<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8">
        <el-button-group>
          <el-button v-show="add" size="mini" type="primary" icon="el-icon-plus" round @click="handleAddClick">{{ $t('operate.add') }}</el-button>
        </el-button-group>
      </el-col>
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-input v-model="agentParam.userName" style="width:144px" placeholder="根据账号查询" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="user.userName" align="center" :label="$t('dealer.account')" width="144" />
      <el-table-column prop="user.nickName" align="center" :label="$t('dealer.nickName')" width="94" />
      <el-table-column prop="fullName" align="center" :label="$t('dealer.fullName')" width="120" />
      <el-table-column prop="phoneNo" align="center" :label="$t('dealer.phone')" width="164" />
      <el-table-column :label="$t('dealer.avatar')" align="center" width="114">
        <template slot-scope="scope">
          <el-popover trigger="hover" placement="top">
            <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.user.avatar"
              fit="none"
            />
            <div slot="reference" class="name-wrapper">
              <el-tag size="medium">{{ $t('operate.viewDetail') }}</el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="isOnline" :label="$t('dealer.onLine')" align="center" width="84">
        <template slot-scope="scope">
          {{ scope.row.isOnline ? $t('status.onLine') : $t('status.offLine') }}
        </template>
      </el-table-column>
      <el-table-column prop="createDt" :label="$t('dealer.createTime')" align="center" width="144" :formatter="dateTimeFormat" />
      <el-table-column prop="remark" :label="$t('dealer.remark')" show-overflow-tooltip />
      <el-table-column fixed="right" align="center" :label="$t('dealer.operation')" width="100">
        <template slot-scope="scope">
          <el-button-group>
            <el-button v-show="edit" plain size="mini" style="width:74px;" @click="handleEditClick(scope.row)">{{ $t('operate.edit') }}</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="end" style="padding:5px 0; ">
      <el-pagination background :current-page="currentPage" :page-sizes="[10, 50, 100, 200]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-row>
    <my-upload
      v-model="avatarShow"
      field="img"
      :width="200"
      :height="200"
      :url="uploadUrl"
      :headers="myHeaders"
      @crop-success="cropSuccess"
      @crop-upload-success="cropUploadSuccess"
      @crop-upload-fail="cropUploadFail"
    />
    <el-dialog :title="$t('dealer.info')" :visible.sync="dialogFormVisible" class="form-item-normal" :close-on-press-escape="false" :close-on-click-modal="false" top="15vh" width="25%">
      <el-form ref="postForm" label-width="90px" label-suffix=":" :rules="rules" :model="object" class="form-container">
        <div class="createPost-main-container">
          <el-form-item :label="$t('dealer.avatar')" prop="user.avatar" required>
            <el-row>
              <el-col>
                <el-row type="flex" justify="center">
                  <div @click="setAvatar">
                    <el-avatar v-if="!object.user.avatar" :size="100" :src="require('../../../assets/images/avatar-add.png')" />
                    <el-avatar v-else :size="100" :src="object.user.avatar" />
                  </div>
                </el-row>
              </el-col>
              <el-col>
                <el-row type="flex" justify="center">
                  <el-link type="primary" @click="setAvatar">
                    <span v-if="!object.user.avatar">
                      {{ $t('dealer.clickToAdd') }}
                    </span>
                    <span v-else>
                      {{ $t('dealer.clickToChange') }}
                    </span>
                  </el-link>
                </el-row>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="$t('dealer.account')" prop="user.userName">
            <el-input v-model="object.user.userName" class="input-with-select" clearable>
              <el-button slot="append" type="text" :loading="remoteLoading" @click="genUserName">{{ $t('operate.sysgen') }}</el-button>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('dealer.nickName')" prop="user.nickName">
            <el-input v-model="object.user.nickName" clearable />
          </el-form-item>
          <el-form-item :label="$t('dealer.fullName')" prop="member.fullName">
            <el-input v-model="object.member.fullName" clearable />
          </el-form-item>
          <el-form-item :label="$t('dealer.phone')" prop="member.phoneNo">
            <el-input v-model="object.member.phoneNo" clearable />
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">{{ $t('operate.cancel') }}</el-button>
        <el-button type="primary" :loading="submitting" @click="submitDealerEdit">{{ $t('operate.edit') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { addMember, listMemberInfo, genAccount } from '@/api/account/member'
import moment from 'moment'
import myUpload from 'vue-image-crop-upload'
import { mapGetters } from 'vuex'
import { dealerUploadUrl } from '@/data/config'
import { getToken } from '@/utils/auth' // 验权
import { checkAccountExist } from '@/api/account/accountCheck'
import { deepClone } from '@/utils/transferUtil'
const defaultForm = {
  user: {
    avatar: '',
    userName: ''
  },
  member: {
    percentAgent: 0,
    memberType: 5,
    parentId: 0,
    fullName: '',
    phoneNo: ''
  }
}
export default {
  name: 'DealerList',
  components: {
    'my-upload': myUpload
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('memberAddForm.validateMsg.account')))
      } else {
        checkAccountExist(
          this.object.user.userName,
          5,
          this.object.user.id
        ).then(r => {
          if (r.data) {
            callback(new Error(this.$t('memberAddForm.validateMsg.accountExist')))
          } else {
            callback()
          }
        }).catch(function(error) {
          callback(new Error(error))
        })
      }
    }
    return {
      avatarShow: false,
      uploadUrl: dealerUploadUrl,
      imgDataUrl: '',
      avatarUrl: '',
      agentParam: {
        memberType: 5
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      currentUserId: 0,
      rules: {
        'user.userName': [{ validator: validateUsername },
          { required: true, message: this.$t('dealer.validateMsg.account'), trigger: 'blur' }],
        'user.avatar': [{ required: true, message: this.$t('dealer.validateMsg.avatar'), trigger: 'blur' }],
        'user.nickName': [{ required: true, message: this.$t('dealer.validateMsg.nickname'), trigger: 'blur' }],
        'member.fullName': [{ required: true, message: this.$t('dealer.validateMsg.fullName'), trigger: 'blur' }],
        'member.phoneNo': [{ required: true, message: this.$t('dealer.validateMsg.phoneNo'), trigger: 'blur' }]
      },
      object: Object.assign({}, defaultForm),
      remoteLoading: false,
      submitting: false
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ]),
    myHeaders: function() {
      return {
        'X-Token': getToken()
      }
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    this.listTableData()
  },
  methods: {
    genUserName() {
      this.remoteLoading = true
      genAccount(5).then(r => {
        this.object.user.userName = r.data
        this.remoteLoading = false
      })
    },
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      this.agentParam.parentId = this.member.id
      this.agentParam.pageSize = this.pageSize
      this.agentParam.pageNo = this.currentPage
      listMemberInfo(this.agentParam).then(r => {
        this.objectList = r.data.list
        this.total = r.data.total
      })
    },
    handleAddClick() {
      this.dialogFormVisible = true
      this.object = Object.assign({}, defaultForm)
    },
    handleEditClick(val) {
      this.object = deepClone(defaultForm)
      this.object.member = {
        id: val.id,
        percentAgent: val.percentAgent,
        memberType: val.memberType,
        parentId: val.parentId,
        fullName: val.fullName,
        phoneNo: val.phoneNo
      }
      this.object.user = val.user
      this.dialogFormVisible = true
    },
    submitDealerEdit() {
      this.$refs['postForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          this.object.member.parentId = this.member.id
          addMember(this.object).then(r => {
            this.listTableData()
            this.dialogFormVisible = false
            this.submitting = false
          }).catch(() => {
            this.submitting = false
          })
        }
      })
    },
    setAvatar() {
      this.avatarShow = true
    },
    cropSuccess(imgDataUrl, field) {
      this.imgDataUrl = imgDataUrl
    },
    cropUploadSuccess(jsonData, field) {
      this.avatarUrl = jsonData.data[0].url
      this.object.user.avatar = this.avatarUrl
    },
    cropUploadFail(status, field) {
      console.log(status)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.listTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.listTableData()
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
<style>
.input-with-select .el-input-group__append {
  background-color: #fff;
  color: #006dfe;
}
</style>
