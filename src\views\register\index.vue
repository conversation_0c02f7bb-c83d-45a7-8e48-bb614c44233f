<template>
  <div v-if="device === 'mobile'" class="container mobile"> <!-- mobile version -->
    <div class="login-bg" />
    <div class="content">
      <!--登录 -->
      <el-row class="login-panel">
        <el-col class="login-info">
          <div class="logo">
            <img :src="logoUrl">
          </div>
          <div class="username">
            <div class="username-text">{{ $t('login.placeholder.account') }}</div>
            <el-input v-model="loginForm.username" class="input-control id lang-cn" :autofocus="autofocus" name="username" type="text" auto-complete="off" />
          </div>
          <div class="password">
            <div class="username-text">{{ $t('login.placeholder.password') }}</div>
            <el-input
              v-model="loginForm.password"
              class="input-control id lang-cn"
              name="password"
              :type="passwordType"
              auto-complete="off"
              @keyup.enter.native="handleLogin"
            />
          </div>
          <!-- <div class="captcha">
            <el-input
              v-model="loginForm.captchaCode"
              class="input-control id lang-cn"
              name="captcha"
              auto-complete="off"
              :placeholder="$t('login.captcha')"
              @keyup.enter.native="handleLogin"
            />
            <img :src="imgUrl" @click="changeCaptcha">
          </div> -->
          <div class="btn-panel">
            <a class="btn-login" @click="handleLogin">
              <i v-if="loading" class="el-icon-loading" />
              {{ $t("operate.login") }}</a>
          </div>
        </el-col>
      </el-row>
      <!--语言 -->
      <el-row :gutter="25">
        <el-col :span="4" :offset="6">
          <el-image
            :src="krUrl"
            :fit="fit"
            @click="setLang(arguments, 'kr')"
          />
        </el-col>
        <el-col :span="4">
          <el-image
            :src="enUrl"
            :fit="fit"
            @click="setLang(arguments, 'en')"
          />
        </el-col>
        <el-col :span="4">
          <el-image
            :src="cnUrl"
            :fit="fit"
            @click="setLang(arguments, 'zh')"
          />
        </el-col>
      </el-row>
    </div>
  </div>
  <div v-else class="container"><!-- PC version -->
    <div class="login-bg" />
    <div class="content">
      <!--语言 -->
      <!--<el-row class="top-panel">
        <el-col :span="8" :offset="8">
          <el-image
            :src="logoUrl"
          />
        </el-col>
      </el-row> -->
      <el-row class="login-panel" :gutter="60">
        <el-col :span="8" :offset="8" class="login-info">
          <span>
            <div class="logo3">
              <!--<el-image
                :src="logoUrl"
                :fit="fit"
                width="100%"
                height="100%"
              /> -->
              <img :src="logoUrl">
              <!-- <img :src="logoUrl"> -->
              <!--<div class="login-text">{{ $t("login.loginText") }}</div> -->
            </div>
            <el-form ref="postForm" label-width="80px" class="form-container">
              <div class="input-control">
                <el-form-item label="用户名" class="postInfo-container-item">
                  <el-input v-model="loginForm.username" clearable />
                </el-form-item>
              </div>
            </el-form>
            <div class="username input-control">
              <el-row>
                <el-col :span="8">
                  <div class="username-text">{{ $t('login.placeholder.account') }}</div>
                </el-col>
                <el-col :span="16">
                  <el-input v-model="loginForm.username" class="id lang-cn" :autofocus="autofocus" name="username" type="text" auto-complete="off" />
                </el-col>
              </el-row>
            </div>
            <div class="password input-control">
              <el-row>
                <el-col :span="8">
                  <div class="username-text">{{ $t('login.placeholder.password') }}</div>
                </el-col>
                <el-col :span="16">
                  <el-input
                    v-model="loginForm.password"
                    class="id lang-cn"
                    name="password"
                    :type="passwordType"
                    auto-complete="off"
                    @keyup.enter.native="handleLogin"
                  />
                </el-col>
              </el-row>
            </div>
            <!--<div class="captcha">
              <el-input
                v-model="loginForm.captchaCode"
                class="input-control id lang-cn"
                name="captcha"
                auto-complete="off"
                :placeholder="$t('login.captcha')"
                @keyup.enter.native="handleLogin"
              />
              <img :src="imgUrl" @click="changeCaptcha">
            </div> -->
            <div class="btn-panel">
              <a class="btn-login" @click="handleLogin">
                <i v-if="loading" class="el-icon-loading" />
                {{ $t("operate.login") }}</a>
            </div>
          </span>
          <span v-if="false">
            <div class="ip-block">
              <div class="ip">{{ blockedIp }}</div>
              <div class="tips">{{ $t('login.ipBlockedTips') }}</div>
            </div>
          </span>
          <el-row class="bottom-panel" :gutter="18">
            <!--<el-col :span="4" :offset="3">
              <el-image
                :src="krUrl"
                :fit="fit"
                @click="setLang(arguments, 'kr')"
              />
            </el-col>
            <el-col :span="4" :offset="2">
              <el-image
                :src="enUrl"
                :fit="fit"
                @click="setLang(arguments, 'en')"
              />
            </el-col>
            <el-col :span="4" :offset="2">
              <el-image
                :src="cnUrl"
                :fit="fit"
                @click="setLang(arguments, 'zh')"
              />
            </el-col> -->
            <!--<el-col :span="4" :offset="2">
              <el-image
                :src="twUrl"
                :fit="fit"
                @click="setLang(arguments, 'tw')"
              />
            </el-col> -->
            <!--<el-col v-show="showTag.fullscreen" :span="1">
              <el-image
                :src="fullscreenUrl"
                :fit="fit"
                @click="setScreenFull"
              />
            </el-col> -->
          </el-row>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>

import { checkCaptchaCode } from '@/api/common/login'
import { baseUrl } from '@/data/config'
import { mapState } from 'vuex'
export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('login.validateMsg.account')))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('login.validateMsg.password')))
      } else {
        callback()
      }
    }
    const validateCaptchaCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('login.validateMsg.verifyCode')))
      }
      checkCaptchaCode(this.loginForm.captchaCode, this.publicKey).then(
        r => {
          this.captchaCodeVerify = false
          if (r.data) {
            this.captchaCodeVerify = true
            callback()
          } else {
            callback(new Error(this.$t('login.validateMsg.verifyCodeError')))
          }
        }
      )
    }
    return {
      captchaCodeVerify: false,
      publicKey: '',
      imgUrl: '',
      bannerItem: { id: 1, title: this.$t('login.bodyTittle'), desc: this.$t('login.bodyDesc'), url: '', sytle: 1, img: require('../../assets/images/banner-left.png') },
      userTypeIcon: 'fa-user-o',
      userType: 2,
      loginForm: {
        username: '',
        password: '',
        captchaCode: ''
      },
      userTypeVisible: false,
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        captchaCode: [
          { required: true, trigger: 'blur', validator: validateCaptchaCode },
          { len: 4, message: this.$t('login.validateMsg.verifyCodeLen'), trigger: 'blur' }
        ]
      },
      passwordType: 'password',
      loading: false,
      keyboardLogin: true,
      validKey: '',
      fit: 'fill',
      isKr: false,
      isCn: false,
      isEn: false,
      krUrl: require('../../assets/images/login/kr.png'),
      cnUrl: require('../../assets/images/login/cn.png'),
      enUrl: require('../../assets/images/login/en.png'),
      twUrl: require('../../assets/images/login/tw.png'),
      langUrl: {
        kr: require('../../assets/images/login/kr.png'),
        krh: require('../../assets/images/login/krh.png'),
        cn: require('../../assets/images/login/cn.png'),
        cnh: require('../../assets/images/login/cnh.png'),
        en: require('../../assets/images/login/en.png'),
        enh: require('../../assets/images/login/enh.png'),
        tw: require('../../assets/images/login/tw.png'),
        twh: require('../../assets/images/login/twh.png')
      },
      logoUrl: require('../../assets/images/logo.png'),
      autofocus: true
    }
  },
  computed: {
    lang: {
      get() {
        return this.$store.state.app.language
      },
      set(lang) {
        this.$i18n.locale = lang
        this.$store.dispatch('app/setLanguage', lang)
      }
    },
    ...mapState({
      device: state => state.app.device
    })
  },
  created() {
    this.changeCaptcha()
    this.setDefLang()
  },
  methods: {
    setDefLang() {
      if (!this.lang) {
        this.isKr = true
      } else {
        if (this.lang === 'kr') {
          this.isKr = true
        } else if (this.lang === 'zh') {
          this.isCn = true
        } else {
          this.isEn = true
        }
      }
      this.setLang(null, this.lang)
    },
    setLang(arg, lang) {
      if (lang === 'kr') {
        this.krUrl = this.langUrl.krh
        this.cnUrl = this.langUrl.cn
        this.enUrl = this.langUrl.en
        this.twUrl = this.langUrl.tw
      } else if (lang === 'zh') {
        this.krUrl = this.langUrl.kr
        this.cnUrl = this.langUrl.cnh
        this.enUrl = this.langUrl.en
        this.twUrl = this.langUrl.tw
      } else if (lang === 'tw') {
        this.krUrl = this.langUrl.kr
        this.cnUrl = this.langUrl.cn
        this.enUrl = this.langUrl.en
        this.twUrl = this.langUrl.twh
      } else {
        this.krUrl = this.langUrl.kr
        this.cnUrl = this.langUrl.cn
        this.enUrl = this.langUrl.enh
        this.twUrl = this.langUrl.tw
      }
      this.lang = lang
    },
    createKey() {
      return Date.now() + '_' + Math.floor(Math.random() * 1000)
    },
    changeCaptcha() {
      this.publicKey = this.createKey()
      this.imgUrl = baseUrl + '/captcha/getCaptchaCode/' + this.publicKey
    },
    showLoginType() {
      this.keyboardLogin = !this.keyboardLogin
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    handleLogin() {
      if (window.returnCitySN) {
        this.loginForm.loginIp = window.returnCitySN['cip']
        this.loginForm.loginAddr = window.returnCitySN['cname']
      }
      if (!this.loginForm.username) {
        this.$message.error(this.$t('login.validateMsg.account'))
        return
      }
      if (!this.loginForm.password) {
        this.$message.error(this.$t('login.validateMsg.inputPassword'))
        return
      }
      this.loading = true
      this.$store.dispatch('Login', this.loginForm).then(r => {
        if (r.data.code === 20000) {
          this.$router.push({ path: '/dashboard/index' })
        } else {
          console.log(r)
          this.loading = false
          if (r.data.code === 400020) {
            this.$message.error(this.$t('login.validateMsg.passwordError'))
          }
          if (r.data.code === 400012 || r.data.code === 400015) {
            this.$message.error(this.$t('login.validateMsg.accountClosed'))
          }
          if (r.data.code === 400011) {
            this.$message.error(this.$t('login.validateMsg.accountNotExisted'))
          }
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss">
  .container1 .el-input__inner {
    height: 100%;
    line-height: 100%;
    background: transparent;
    border: none;
    background: #ffffff;
  }
</style>
<style lang="scss" scoped>
  .container{
    overflow:hidden;
    pointer-events:visible;
    -webkit-transform-origin:top left;
    transform-origin:top left;
    width:100%;
    height:100%;
    z-index:1;
  }
  .top-panel {
    margin-top: 2%;
  }
  .bottom-panel{
    margin-top: 10%;
    height: 40%;
  }
  .container {
    height:100%;
    width:100%;
    overflow:hidden;
    position:relative;
    padding: 0px;
    z-index:1;
  }
  .container .login-bg {
    position:absolute;
    /*background:url("../../assets/images/login/login-bg.jpg") no-repeat 50% 50%; */
    background-size:100% 100%;
    height:100%;
    left:50%;
    top:0;
    transform:translate(-50%,0);
    width:100%;
    /* animation:bg 5s infinite ease-in-out alternate; */
    opacity:1;
    background-color: #ededed;
  }
  @keyframes bg {
    0% {
      left:46%
    }
    100% {
      left:50%
    }
  }
  .container .poker {
    position:absolute;
    top: 5%;
    bottom: 0%;
    height: 90%;
    width: 35%;
    right: 5%;
    z-index:2;
    animation:poker 5s infinite ease-in-out alternate;
    background-size: 100% 100%;
    animation:fadeInUp 5s infinite;
    animation-direction:alternate;
    -webkit-animation:fadeInUp 5s infinite;
    -webkit-animation-direction:alternate;
  }
  .container .poker-btn {
    position:absolute;
    top: 83%;
    bottom:-17.5%;
    height:16.5%;
    width:13%;
    right:15%;
    z-index:2;
    /* animation:poker 5s infinite ease-in-out alternate;*/
    background:url("../../assets/images/login/play.png") no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
  }
  .poker-btn:hover {
    background:url("../../assets/images/login/playh.png") no-repeat;
    background-size: 100% 100%;
  }
  .container .poker2 {
    position:relative;
    top: 5%;
    bottom: 0%;
    height: 90%;
    width: 35%;
    left: 5%;
    z-index:2;
    animation:poker 5s infinite ease-in-out alternate;
    background-size: 100% 100%;
    animation:fadeInUp 5s infinite;
    animation-direction:alternate;
    -webkit-animation:fadeInUp 5s infinite;
    -webkit-animation-direction:alternate;
  }
  .container .sabong {
    /*animation:fadeInUp 5s infinite;
    animation-direction:alternate;
    -webkit-animation:fadeInUp 5s infinite;
    -webkit-animation-direction:alternate;*/
    margin-top: 40%;
    opacity: 0;
  }
  .container .sabong2 {
    /*animation:fadeInUp 10s infinite;
    animation-direction:alternate;
    -webkit-animation:fadeInUp 10s infinite;
    -webkit-animation-direction:alternate;*/
    margin-top: 40%;
    opacity: 0;
  }
  @keyframes poker {
    0% {
      right:3%
    }
    100% {
      right:5%
    }
  }
  .content {
    height:100%;
    width:100%;
    overflow:hidden;
    position:relative;
    z-index:3;
    padding: 0.5%;
    padding-top: 1%;
  }
  img{
    width: 100%;
    height: 100%;
  }
  .login-panel{
    margin-top: 8.5%;
    height: 70%;
  }
  .download-panel{
    margin-top: 50%;
    border-radius: 5%;
    border:1px solid rgba(255, 255, 255, 0.2);
    padding: 10%;
    z-index: 6;
    background: rgba(0, 0, 0, 0.3);
  }
  .download-desc {
    margin-top: 5%;
    margin-bottom: 5%;
  }
  .download-btn {
    border-radius: .8rem;
    border: .1rem #FFF solid;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 2% 5%;
    text-decoration: none;
    color: #FFF;
    font-size: 1.2rem;
    width: 90%;
    margin: 0 auto;
  }
  .login-info{
    height:100%;
    /*background-color: rgba(0, 0, 0, 0.9); */
    padding: 2.5%;
    border: 0px solid #fdc700;
    border-radius: 0.5rem;
    /* background-image:url('../../assets/images/login/login-panel.png'); */
    background-size: 100% 100%;
    padding-top: 1%;
  }
  .logo {
    height:15%;
    width: 100%;
    margin: 0 auto;
    line-height: 15%;
    text-align: center;
    margin-top: 10%;
  }
  .logo img {
    width: auto;
    height: 100%;
  }
  .login-text {
    font-size: 2rem;
    color: #fdc700;
  }
  .logo2 {
    height:100%;
    width: 100%;
    margin: 0 auto;
    line-height: 10%;
    text-align: center;
  }
  .logo2 img {
    width: auto;
    height: 100%;
  }
  .logo3 {
    height: 26%;
    width: 80%;
    margin: 0 auto;
    line-height: 15%;
    text-align: center;
    margin-top: 0%;
  }
  .logo3 img {
    height: 100%;
    width: 100%;
  }
  .username, .password, .captcha {
    margin-bottom: 2.5%;
    height:20%;
    width: 100%;
    font-size:1.6rem;
  }
  .username {
    /*background:url('../../assets/images/login/input-bg.png') no-repeat; */
    background-size: 100% 100%;
  }
  .username-text {
    margin-left: 1.5%;
    font-size: 1.2rem !important;
    color: #000000;
  }
  .pass2ord-text {
    margin-left: 1.5%;
    font-size: 1.2rem;
    color: #000000;
  }
  .password {
    /*background:url('../../assets/images/login/input-bg.png') no-repeat; */
    background-size: 100% 100%;
  }
  .captcha {
    background:url('../../assets/images/login/captcha.png') no-repeat;
    background-size: 98% 98%;
    position: relative;
  }
  .captcha img {
    position: absolute;
    top: 0%;
    right: 3.5%;
    width: auto;
    margin-top: 1.5%;
    height: 80%;
    opacity: 0.8;
  }
  .input-control,.password .input-control, .captcha .input-control{
    color:#000000;
    width:100%;
    height:10%;
    padding-left:2%;
    display:inline-block;
    border: solid 1px #d9d9d9;
    background-color: #ffffff;
    padding-top: 5px;
    padding-right: 5px;
    border-radius: 5px;
  }
  .btn-panel{
    height:12%;
  }
  .btn-login, .btn-guest {
    cursor:pointer;
    border-radius:.5rem;
    font-size:1.8rem !important;
    height:100%;;
    text-align:center;
    float:left;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
  }
  .btn-login {
    background-color: rgba(118, 100, 238 ,1);
    width: 50%;
    margin-left: 25%;
    border: 0px solid #fdc700;
    margin-top: 5%;
  }
  .btn-guest {
    background-color:#f56c6c;
    width: 40%;
  }
  .loading {
    width: 20%;
    height:60%;
  }
  .ip-block {
    background-color: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    font-size: 3rem;
    padding-top: 30%;
    text-align: center;
    border-radius: 5%;
  }
  .ip-block .ip {
    color: white;
  }
  .ip-block .tips {
    padding-top: 5%;
    color: red;
  }
  .block-container {
    height:100%;
    width:100%;
    overflow:hidden;
    position:relative;
    padding: 0px;
    z-index:1;
    color: #000000;
    padding: 5%;
  }
  .error-container {
    margin-top: 0%;
  }
 .error-code {
   font-size: 4rem;
   float: left;
  }
 .error-time {
   font-size: 0.5rem;
   float: left;
   margin-left: 5%;
   margin-top: 3%;
  }
 .access-denied {
   font-size: 2rem;
   color: #999999;
  }
 .what-happened {
   font-size: 2rem;
   margin-top: 5%;
  }
 .what-info {
   margin-top: 1%;
  }
 .block-line {
   margin-top: 5%;
   margin-bottom: 1%;
   border-top: 1px solid #d9d9d9;
  }
 .block-bottom {
   color: #666666;
  }
 .your-ip {
   float: left;
  }
 .performance {
   float: left;
   margin-left: 2%;
  }
  /* mobile */
  .container.mobile .login-panel {
    margin-top: 25%;
    margin-left: 6%;
    margin-right: 5%;
    margin-bottom: 0%;
  }
  .container.mobile .login-bg  {
    background-size: auto 100%;
  }
  .container.mobile .login-info {
    background-color: transparent;
    background-image: none;
    border: none;
  }
  .container.mobile .logo {
    height: 25%;
    margin-bottom: 10%;
    margin-top: 0;
  }
  .container.mobile .username,
  .container.mobile .password,
  .container.mobile .captcha {
    margin-bottom: 5%;
    height:16%;
    width: 100%;
    font-size:1.6rem;
  }
  .container.mobile .btn-panel {
    height:10%;
  }
</style>
