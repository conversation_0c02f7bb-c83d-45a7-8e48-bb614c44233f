<template>
  <div class="app-container game-setting">
    <el-form>
      <el-row>
        <el-col>
          <span class="text">每靴从第</span>
          <el-select v-model="form.ruleValue2" clearable placeholder=" " class="input">
            <el-option
              v-for="index in 60"
              :key="index"
              :label="index+''"
              :value="index+''"
            />
          </el-select>
          <span class="text">口牌开始计验证</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <span class="text">验证靴数</span>
          <el-input
            v-model="form.ruleValue7"
            class="input"
          />
          <span class="text">靴</span>

          <span class="text ml-10">从</span>
          <el-input
            v-model="form.ruleValue9"
            class="input"
          />
          <span class="text">靴开始验证</span>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col>
          <span class="text">连续</span>
          <el-input
            v-model="form.ruleValue3"
            class="input"
          />
          <span class="text">口</span>
          <el-radio-group v-model="form.ruleValue4">
            <el-radio label="1">KKK</el-radio>
            <el-radio label="2">ABC</el-radio>
          </el-radio-group>
        </el-col>
      </el-row>-->
      <el-row>
        <el-col>
          <span class="text">出现</span>
          <el-input
            v-model="form.ruleValue3"
            class="input"
          />
          <span class="text">点和</span>
          <span class="text">连续下</span>
          <el-select v-model="form.ruleValue6" clearable placeholder=" " class="input">
            <el-option
              v-for="index in 60"
              :key="index"
              :label="index+''"
              :value="index+''"
            />
          </el-select>
          <span class="text">口</span>
          <span class="text">买</span>
          <el-select v-model="form.ruleValue5" clearable placeholder=" " class="input">
            <el-option label="B" value="B" />
            <el-option label="P" value="P" />
          </el-select>
        </el-col>
      </el-row>
      <!--保存 -->
      <div>
        <el-row>
          <el-col>
            <el-button class="save" type="primary" @click="handleSave">保存</el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getByRuleNo, add } from '@/api/setting/gameRule'
export default {
  name: 'TieSeting',
  data() {
    return {
      tabName: 'first',
      agentParam: {},
      ruleNo: 'TIE_SETTING',
      form: {
        id: null,
        ruleNo: 'TIE_SETTING',
        ruleName: '买和设置',
        ruleType: '',
        ruleValue: '',
        ruleValue2: '',
        ruleValue3: '',
        ruleValue4: '',
        ruleValue5: '',
        ruleValue6: '',
        ruleValue7: '',
        ruleValue8: '',
        ruleValue9: '1',
        ruleValue10: '2',
        ruleValue11: '2',
        ruleValue12: '',
        ruleValue13: '',
        ruleValue14: '',
        ruleValue15: '',
        ruleValue16: '',
        ruleValue17: '',
        ruleValue18: '',
        ruleValue19: '',
        ruleValue20: '',
        createBy: this.$store.getters.userId,
        modifiedBy: this.$store.getters.userId
      },
      cardQtyEnums: [{ label: '6', value: '6' }, { label: '7', value: '7' }, { label: '8', value: '8' }, { label: '9', value: '9' }, { label: '10', value: '10' }],
      roadTypeEnums: [{ label: '大路', value: '1' }, { label: '大眼仔', value: '2' }, { label: '小路', value: '3' }, { label: '蟑螂路', value: '4' }],
      equalEnums: [{ label: '大于', value: '1' }, { label: '等于', value: '2' }, { label: '小于', value: '3' }],
      cardsEnums: ['A', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'],
      bpData: [],
      chipData: [],
      tempChipData: [],
      onlyUpdate: false
    }
  },
  /*
  系统设置：SYS_SETTING
  系统生路：SYS_GEN_ROAD
  形态设置：SHAPE
  比例设置：RATE
  点数设置：POINT
  牌数设置：CARD_POINT
  牌型设置：CARD_TYPE
  胜负设置：WIN_LOSE
  验证选项：VALID_OPTION
  */
  watch: {
    //
  },
  created() {
    this.form.ruleNo = this.ruleNo
    this.handleRuleNo()
  },
  methods: {
    handleRuleNo() {
      getByRuleNo(this.ruleNo, this.$store.getters.userId).then(r => {
        if (r.data && !this.onlyUpdate) {
          this.form = r.data
        }
      })
    },
    handleSave() {
      this.form.createBy = this.$store.getters.userId
      this.form.modifiedBy = this.$store.getters.userId
      if (this.onlyUpdate) {
        this.onlyUpdate = false
        this.$emit('submit', this.form)
      } else {
        add(this.form).then(r => {
          if (r.code === 20000) {
            this.handleRuleNo()
            this.$message.success('保存成功')
          } else {
            this.$message.success('保存失败')
          }
        })
      }
    },
    setData(data) {
      this.form = data
      this.onlyUpdate = true
    }
  }
}
</script>

<style>
.game-setting .el-table--small th, .game-setting .el-table--small td {
  padding: 0;
}
.game-setting .el-table--small td .el-input__inner {
  border-radius: 0;
  border: 0 solid #DCDFE6;
  text-align: center;
}
.game-setting .el-table--small th, .game-setting .el-table--small td {
  padding: 0;
}
.game-setting .el-table .cell {
  padding: 0;
}
.game-setting th, .game-setting td {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.game-setting .el-row {
  margin-bottom: 10px;
}
.game-setting .input {
  width: 100px;
}
.game-setting .save {
  margin-left: 25px;
  width: 100px;
}
.game-setting .text {
  margin-right: 10px;
}
.game-setting .el-checkbox {
  margin-right: 10px;
}
</style>
