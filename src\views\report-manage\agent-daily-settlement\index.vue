<template>
  <div class="app-container">
    <el-row type="flex" justify="right" style="padding-bottom:5px ">
      <el-col :span="8" />
      <el-col :span="16">
        <el-row type="flex" justify="end">
          <el-date-picker
            v-model="memberParam.date"
            type="daterange"
            align="right"
            unlink-panels
            :start-placeholder="$t('dateTemplate.startDate')"
            :end-placeholder="$t('dateTemplate.endDate')"
            :picker-options="pickerOptions"
          />
          <el-select v-model="memberParam.gameType" clearable :placeholder="$t('agentSettle.placeholder.gameType')">
            <el-option
              v-for="item in gameTypeEnums"
              :key="item.type"
              :label="$t(item.label)"
              :value="item.type"
            />
          </el-select>
          <el-input v-model="memberParam.userName" style="width:224px" :placeholder="$t('betInfo.placeholder.userAccount')" clearable />
          <el-button icon="fa fa-search" type="primary" @click="handFilter" />
          <!-- <el-button icon="el-icon-download" type="primary" @click="queryExportData">导出</el-button> -->
        </el-row>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" :data="objectList" tooltip-effect="dark" style="width: 100%" border highlight-current-row show-summary :summary-method="getSummaries">
      <el-table-column type="selection" align="center" width="42" />
      <el-table-column type="index" align="center" width="42" />
      <el-table-column prop="user.userName" align="center" :label="$t('agentSettle.agentAccount')" width="124" />
      <el-table-column prop="user.nickName" align="center" :label="$t('agentSettle.agentNickname')" width="124" />
      <el-table-column prop="gameType" align="center" :label="$t('agentSettle.gameType')" :formatter="gameTypeFilter" />
      <el-table-column prop="totalWinAmount" align="center" :label="$t('agentSettle.totalWinAmount')" width="144">
        <template slot-scope="scope">
          <div> {{ scope.row.totalWinAmount|numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalWashAmount" align="center" :label="$t('agentSettle.totalWashAmount')" width="144">
        <template slot-scope="scope">
          <div> {{ scope.row.totalWashAmount|numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="washRate" align="center" :label="$t('agentSettle.washRate')">
        <template slot-scope="scope">
          <div> {{ scope.row.washRate }}% </div>
        </template>
      </el-table-column>
      <el-table-column prop="washRate" align="center" :label="$t('agentSettle.washIncome') + 'dd'">
        <template slot-scope="scope">
          <div> {{ scope.row.totalWashRateAmount|numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column prop="member.percentAgent" align="center" :label="$t('agentSettle.share')">
        <template slot-scope="scope">
          <div> {{ scope.row.member.percentAgent }}% </div>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('agentSettle.shareIncome')" width="114">
        <template slot-scope="scope">
          <div> {{ scope.row.percentAgentProfit|numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('agentSettle.totalIncome')" width="114">
        <template slot-scope="scope">
          <div> {{ scope.row.totalAgentProfit|numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('agentSettle.superiorIncome')" show-overflow-tooltip>
        <template slot-scope="scope">
          <div> {{ scope.row.totalParentProfit|numberFilter|moneyFilter }} </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" :label="$t('agentSettle.operation')" width="180">
        <template slot-scope="scope">
          <el-button-group>
            <el-button plain :disabled="scope.row.member.id === member.id" size="mini" style="width:74px;" @click="handleBackClick(scope.row)">{{ $t('agentSettle.back') }}</el-button>
            <el-button plain :disabled="canAgent" size="mini" style="width:74px;" @click="handleAgentClick(scope.row)">{{ $t('agentSettle.agentReport') }}</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { listAgentDaily } from '@/api/settlement/agentDaily'
import { getGameTypeEnum, GameTypeEnum } from '@/enums/setting.js'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { formatMoney, formatNumber } from '@/utils/formatter'
export default {
  name: 'MemberList',
  filters: {
    numberFilter(data) {
      return formatNumber(data)
    },
    moneyFilter(money) {
      return formatMoney(money)
    }
  },
  props: {
    add: {
      type: Boolean,
      default: false
    },
    edit: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      memberParam: {
        memberType: 2,
        gameType: 0,
        date: ''
      },
      objectList: [],
      multipleSelection: [],
      currentPage: 1,
      total: 0,
      pageSize: 10,
      ids: [],
      dialogFormVisible: false,
      dialogPassVisible: false,
      currentUserId: 0,
      amountData: {
        pointType: 1,
        pointMethod: 1
      },
      passwordData: {},
      canAgent: false,
      pickerOptions: {
        shortcuts: [{
          text: this.$t('dateTemplate.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.lastMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: this.$t('dateTemplate.last3Months'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      sums: []
    }
  },
  computed: {
    ...mapGetters([
      'member'
    ]),
    gameTypeEnums() {
      return GameTypeEnum
    }
  },
  watch: {
    multipleSelection: function() {
      const arr = []
      for (const i in this.multipleSelection) {
        arr.push(this.multipleSelection[i].id)
      }
      this.ids = arr.join()
    }
  },
  created() {
    if (this.member.memberType === 4) {
      this.memberParam.agentId = this.member.parentId
    } else {
      this.memberParam.agentId = this.member.id
    }
    this.listTableData()
  },
  methods: {
    formatMoney,
    formatNumber,
    handleBackClick(data) {
      if (data.member.id === this.member.id) {
        this.memberParam.agentId = this.member.id
      } else {
        this.memberParam.agentId = data.member.parentId
      }
      this.listTableData()
    },
    handleAgentClick(data) {
      this.memberParam.agentId = 0
      this.memberParam.parentId = data.member.id
      this.listTableData()
    },
    handleMemberClick() {
    },
    handFilter() {
      this.currentPage = 1
      this.listTableData()
    },
    listTableData() {
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      listAgentDaily(this.memberParam).then(r => {
        this.sumTotal(r.data)
        this.objectList = r.data
        if (r.data && r.data.length > 0) {
          this.canAgent = false
        } else {
          this.canAgent = true
        }
      })
    },
    queryExportData() {
      this.memberParam.pageNo = 1
      this.memberParam.pageSize = 1000
      if (this.memberParam.date) {
        this.memberParam.dateFrom = this.memberParam.date[0]
        this.memberParam.dateTo = this.memberParam.date[1]
      } else {
        this.memberParam.dateFrom = ''
        this.memberParam.dateTo = ''
      }
      listAgentDaily(this.memberParam).then(r => {
        if (r.data && r.data.length > 0) {
          this.exportTableData(r.data)
        }
      })
    },
    exportTableData(dataList) {
      this.downloadLoading = true
      if (!dataList || dataList.length === 0) {
        this.downloadLoading = false
        this.$message({
          message: '导出数据为空',
          type: 'error'
        })
        return
      }
        import('@/vendor/Export2Excel').then(excel => {
          // const tHeader = [
          //   '代理账号', '代理名称', '台类型','总赢','总洗码量','洗码率(%)', '洗码费', '占股']
          const filterVal = [
            'user.userName', 'user.nickName', 'gameType',
            'totalWinAmount', 'totalWashAmount', 'washRate',
            'member.percentAgent']
          const list = dataList
          const data = this.formatJson(filterVal, list)
          console.log(data)
          // excel.export_json_to_excel({
          //   header: tHeader,
          //   data,
          //   filename: '代理日报表',
          //   autoWidth: this.autoWidth
          // })
          this.downloadLoading = false
        })
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'user.userName') {
          return v['user']['userName']
        } else if (j === 'user.nickName') {
          return v['user']['nickName']
        } else if (j === 'gameType') {
          return getGameTypeEnum(v[j]).label
        } else if (j === 'member.percentAgent') {
          return v['member']['percentAgent']
        } else {
          return v[j]
        }
      }))
    },
    gameTypeFilter: function(row, column) {
      return this.$t(getGameTypeEnum(row.gameType).label)
    },
    dateTimeFormat: function(row, column) {
      var date = row[column.property]
      if (!date) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    getSummaries(param) {
      const { columns } = param
      const sums = []
      columns.forEach((column, index) => {
        if (this.sums.length) {
          sums[index] = this.sums[index]
        }
      })
      return sums
    },
    sumTotal(objectList) {
      this.sums = ['', '', '', '', '', 0, 0, '', 0, '', 0, 0, 0]
      this.sums[2] = this.$t('game.totalText')
      for (var i = 0; i < objectList.length; i++) {
        this.sums[5] = this.sums[5] + objectList[i].totalWinAmount
        this.sums[6] = this.sums[6] + objectList[i].totalWashAmount
        this.sums[8] = this.sums[8] + objectList[i].totalWashRateAmount
        this.sums[10] = this.sums[10] + objectList[i].percentAgentProfit
        this.sums[11] = this.sums[11] + objectList[i].totalAgentProfit
        this.sums[12] = this.sums[12] + objectList[i].totalParentProfit
      }
      for (i = 0; i < this.sums.length; i++) {
        if (i > 2 && this.sums[i]) {
          this.sums[i] = formatMoney(formatNumber(this.sums[i]))
        }
      }
    }
  }
}
</script>
